/*=============================================================================
	UnEdCam.cpp: Unreal editor camera movement/selection functions
	Copyright 1997-1999 Epic Games, Inc. All Rights Reserved.

	Revision history:
		* Created by <PERSON>
=============================================================================*/

#include "EditorPrivate.h"
#include "UnRender.h"
//#include "UnSkeletalMesh.h"
#include "UnStat.h"
//milestone 4
#include <wchar.h>

#if 1 //U2Ed
extern void brushclipDeleteMarkers();
#endif

extern FVector GMatineeIPCamLocation;
extern UBOOL matAlwaysShowPath;
extern void matDrawMatineeIP( FSceneNode* InFrame );





extern void vertexedit_Click( UViewport* InViewport, ABrush* pBrush, FVector InLocation, UBOOL InCumulative, UBOOL InAllowDuplicates );

TArray<FVertexHit> VertexHitList;
TArray<HBezierControlPoint> BezierControlPointList;
TArray<NAME_INDEX> InterpolationPathCheckList;	// List of tag name indexes already processed

/*-----------------------------------------------------------------------------
	Globals.
-----------------------------------------------------------------------------*/

// Click flags.
enum EViewportClick
{
	CF_MOVE_ACTOR	= 1,	// Set if the actors have been moved since first click
	CF_MOVE_TEXTURE = 2,	// Set if textures have been adjusted since first click
	CF_MOVE_ALL     = (CF_MOVE_ACTOR | CF_MOVE_TEXTURE),
};

// Internal declarations.
void NoteTextureMovement( ULevel* Level );
void MoveActors( UViewport* Viewport, ULevel* Level, FVector Delta, FRotator DeltaRot, UBOOL Constrained, AActor* ViewActor, UBOOL bForceSnapping = 0 );

// Global variables.
#if 1 //U2Ed
__declspec(dllexport) INT GLastScroll=0;
#else
INT GLastScroll=0;
#endif
INT GFixPanU=0, GFixPanV=0;
INT GFixScale=0;
INT GForceXSnap=0, GForceYSnap=0, GForceZSnap=0;
#if 1 //U2Ed
FString GTexNameFilter;
#endif

// Editor state.
UBOOL GPivotShown=0, GSnapping=0;
FVector GPivotLocation, GSnappedLocation, GGridBase;
FRotator GPivotRotation, GSnappedRotation;
//milestone 4 addition
__declspec(dllexport) bool GPawnsLabel =0;
__declspec(dllexport) bool GTriggersLabel;
__declspec(dllexport) bool GLightsLabel;
__declspec(dllexport) bool GMoversLabel;
__declspec(dllexport) bool GSlabelLabel;

// Temporary.
static TArray<INT> OriginalUVectors;
static TArray<INT> OriginalVVectors;

/*-----------------------------------------------------------------------------
   Primitive mappings of input to axis movement and rotation.
-----------------------------------------------------------------------------*/

//
// Axial rotation.
//
void CalcAxialRot
( 
	UViewport*	Viewport, 
	SWORD		MouseX,
	SWORD		MouseY,
	DWORD		Buttons,
	FRotator&	Delta
)
{
	guard(CalcAxialPerspRot);

	// Do single-axis movement.
	if	   ( (Buttons&(MOUSE_Left|MOUSE_Right))==(MOUSE_Left)             ) Delta.Pitch = +MouseX*4;
	else if( (Buttons&(MOUSE_Left|MOUSE_Right))==(MOUSE_Right)            )	Delta.Yaw   = +MouseX*4;
	else if( (Buttons&(MOUSE_Left|MOUSE_Right))==(MOUSE_Left|MOUSE_Right) ) Delta.Roll  = -MouseY*4;

	unguard;
}

//
// Freeform movement and rotation.
//
void CalcFreeMoveRot
(
	UViewport*	Viewport,
	FLOAT		MouseX,
	FLOAT		MouseY,
	DWORD		Buttons,
	FVector&	Delta,
	FRotator&	DeltaRot
)
{
	guard(CalcFreeMoveRot);
	if( Viewport->IsOrtho() )
	{
		// Figure axes.
		FLOAT *OrthoAxis1, *OrthoAxis2, Axis2Sign, Axis1Sign, *OrthoAngle, AngleSign;
		FLOAT DeltaPitch = DeltaRot.Pitch;
		FLOAT DeltaYaw   = DeltaRot.Yaw;
		FLOAT DeltaRoll  = DeltaRot.Roll;
		if( Viewport->Actor->RendMap == REN_OrthXY )
		{
			OrthoAxis1 = &Delta.X;  	Axis1Sign = +1;
			OrthoAxis2 = &Delta.Y;  	Axis2Sign = +1;
			OrthoAngle = &DeltaYaw;		AngleSign = +1;
		}
		else if( Viewport->Actor->RendMap==REN_OrthXZ )
		{
			OrthoAxis1 = &Delta.X; 		Axis1Sign = +1;
			OrthoAxis2 = &Delta.Z; 		Axis2Sign = -1;
			OrthoAngle = &DeltaPitch; 	AngleSign = +1;
		}
		else if( Viewport->Actor->RendMap==REN_OrthYZ )
		{
			OrthoAxis1 = &Delta.Y; 		Axis1Sign = +1;
			OrthoAxis2 = &Delta.Z; 		Axis2Sign = -1;
			OrthoAngle = &DeltaRoll; 	AngleSign = +1;
		}
		else
		{
			appErrorf( TEXT("Invalid rendering mode") );
			return;
		}

		// Special movement controls.
		if( (Buttons&(MOUSE_Left|MOUSE_Right))==MOUSE_Left )
		{
			// Left button: Move up/down/left/right.
			*OrthoAxis1 = Viewport->Actor->OrthoZoom/30000.0f*(FLOAT)MouseX;
			if     ( MouseX<0 && *OrthoAxis1==0 ) *OrthoAxis1 = -Axis1Sign;
			else if( MouseX>0 && *OrthoAxis1==0 ) *OrthoAxis1 = +Axis1Sign;

			*OrthoAxis2 = Axis2Sign*Viewport->Actor->OrthoZoom/30000.0f*(FLOAT)MouseY;
			if     ( MouseY<0 && *OrthoAxis2==0 ) *OrthoAxis2 = -Axis2Sign;
			else if( MouseY>0 && *OrthoAxis2==0 ) *OrthoAxis2 = +Axis2Sign;
		}
		else if( (Buttons&(MOUSE_Left|MOUSE_Right))==(MOUSE_Left|MOUSE_Right) )
		{
			// Both buttons: Zoom in/out.
			// Viewport->Actor->OrthoZoom  = 100000.0f; // minimap
			Viewport->Actor->OrthoZoom -= Viewport->Actor->OrthoZoom/200.0f * (FLOAT)MouseY;
			if( Viewport->Actor->OrthoZoom<500.0f     ) Viewport->Actor->OrthoZoom = 500.0f;
			if( Viewport->Actor->OrthoZoom>2000000.0f ) Viewport->Actor->OrthoZoom = 2000000.0f;
		}
#if ADDITIONS_IMPROVEMENTS
		else if( (Buttons&(MOUSE_WheelUp|MOUSE_WheelDown)) )
		{
			// Mouse wheel: Zoom in/out.
			Viewport->Actor->OrthoZoom -= Viewport->Actor->OrthoZoom/200.0f * ((Buttons&MOUSE_WheelUp) ? 25.0 : -25.0);
			if( Viewport->Actor->OrthoZoom<500.0f     ) Viewport->Actor->OrthoZoom = 500.0f;
			if( Viewport->Actor->OrthoZoom>2000000.0f ) Viewport->Actor->OrthoZoom = 2000000.0f;
		}
#endif
		else if( (Buttons&(MOUSE_Left|MOUSE_Right))==MOUSE_Right )
		{
			// Right button: Rotate.
			if( OrthoAngle!=NULL )
				*OrthoAngle = -AngleSign*8.0f*(FLOAT)MouseX;
		}
		DeltaRot.Pitch	= appRound(DeltaPitch);
		DeltaRot.Yaw	= appRound(DeltaYaw);
		DeltaRot.Roll	= appRound(DeltaRoll);
	}
	else
	{
		APlayerPawn* Actor = Viewport->Actor;

		if( (Buttons&(MOUSE_Left|MOUSE_Right))==(MOUSE_Left) )
		{
			// Left button: move ahead and yaw.
			Delta.X      = -MouseY * GMath.CosTab(Actor->ViewRotation.Yaw);
			Delta.Y      = -MouseY * GMath.SinTab(Actor->ViewRotation.Yaw);
			DeltaRot.Yaw = +MouseX * 64.0f / 20.0f;
		}
		else if( (Buttons&(MOUSE_Left|MOUSE_Right))==(MOUSE_Left|MOUSE_Right) )
		{
			// Both buttons: Move up and left/right.
			Delta.X      = +MouseX * -GMath.SinTab(Actor->ViewRotation.Yaw);
			Delta.Y      = +MouseX *  GMath.CosTab(Actor->ViewRotation.Yaw);
			Delta.Z      = -MouseY;
		}
		else if( (Buttons&(MOUSE_Left|MOUSE_Right))==(MOUSE_Right) )
		{
			// Right button: Pitch and yaw.
			DeltaRot.Pitch = (64.0f/12.0f) * -MouseY;
			DeltaRot.Yaw   = (64.0f/20.0f) * +MouseX;
		}
	}
	unguard;
}

//
// Perform axial movement and rotation.
//
void CalcAxialMoveRot
(
	UViewport*	Viewport,
	FLOAT		MouseX,
	FLOAT		MouseY,
	DWORD		Buttons,
	FVector&	Delta,
	FRotator&	DeltaRot
)
{
	guard(CalcFreeMoveRot);
	if( Viewport->IsOrtho() )
	{
		// Figure out axes.
		FLOAT *OrthoAxis1,*OrthoAxis2,Axis2Sign,Axis1Sign,*OrthoAngle,AngleSign;
		FLOAT DeltaPitch = DeltaRot.Pitch;
		FLOAT DeltaYaw   = DeltaRot.Yaw;
		FLOAT DeltaRoll  = DeltaRot.Roll;
		if( Viewport->Actor->RendMap == REN_OrthXY )
		{
			OrthoAxis1 = &Delta.X;  	Axis1Sign = +1;
			OrthoAxis2 = &Delta.Y;  	Axis2Sign = +1;
			OrthoAngle = &DeltaYaw;		AngleSign = +1;
		}
		else if( Viewport->Actor->RendMap == REN_OrthXZ )
		{
			OrthoAxis1 = &Delta.X; 		Axis1Sign = +1;
			OrthoAxis2 = &Delta.Z;		Axis2Sign = -1;
			OrthoAngle = &DeltaPitch; 	AngleSign = +1;
		}
		else if( Viewport->Actor->RendMap == REN_OrthYZ )
		{
			OrthoAxis1 = &Delta.Y; 		Axis1Sign = +1;
			OrthoAxis2 = &Delta.Z; 		Axis2Sign = -1;
			OrthoAngle = &DeltaRoll; 	AngleSign = +1;
		}
		else
		{
			appErrorf( TEXT("Invalid rendering mode") );
			return;
		}

		// Special movement controls.
		if( Buttons & (MOUSE_Left | MOUSE_Right) )
		{
			// Left, right, or both are pressed.
			if( Buttons & MOUSE_Left )
			{
				// Left button: Screen's X-Axis.
      			*OrthoAxis1 = Viewport->Actor->OrthoZoom/30000.0f*(FLOAT)MouseX;
      			if     ( MouseX<0 && *OrthoAxis1==0 ) *OrthoAxis1 = -Axis1Sign;
      			else if( MouseX>0 && *OrthoAxis1==0 ) *OrthoAxis1 = +Axis1Sign;
			}
			if( Buttons & MOUSE_Right )
			{
				// Right button: Screen's Y-Axis.
      			*OrthoAxis2 = Axis2Sign*Viewport->Actor->OrthoZoom/30000.0f*(FLOAT)MouseY;
      			if     ( MouseY<0 && *OrthoAxis2==0 ) *OrthoAxis2 = -Axis2Sign;
      			else if( MouseY>0 && *OrthoAxis2==0 ) *OrthoAxis2 = +Axis2Sign;
			}
		}
		else if( Buttons & MOUSE_Middle )
		{
			// Middle button: Zoom in/out.
			Viewport->Actor->OrthoZoom -= Viewport->Actor->OrthoZoom/200.0f * (FLOAT)MouseY;
			if	   ( Viewport->Actor->OrthoZoom<500.0     ) Viewport->Actor->OrthoZoom = 500.0;
			else if( Viewport->Actor->OrthoZoom>2000000.0 ) Viewport->Actor->OrthoZoom = 2000000.0;
		}
		DeltaRot.Pitch	= appRound(DeltaPitch);
		DeltaRot.Yaw	= appRound(DeltaYaw);
		DeltaRot.Roll	= appRound(DeltaRoll);
	}
	else
	{
		// Do single-axis movement.
		if		((Buttons&(MOUSE_Left|MOUSE_Right))==(MOUSE_Left))			   Delta.X = +MouseX;
		else if ((Buttons&(MOUSE_Left|MOUSE_Right))==(MOUSE_Right))			   Delta.Y = +MouseX;
		else if ((Buttons&(MOUSE_Left|MOUSE_Right))==(MOUSE_Left|MOUSE_Right)) Delta.Z = -MouseY;
	}
	unguard;
}

//
// Mixed movement and rotation.
//
void CalcMixedMoveRot
(
	UViewport*	Viewport,
	FLOAT		MouseX,
	FLOAT		MouseY,
	DWORD		Buttons,
	FVector&	Delta,
	FRotator&	DeltaRot
)
{
	guard(CalcMixedMoveRot);
	if( Viewport->IsOrtho() )
		CalcFreeMoveRot( Viewport, MouseX, MouseY, Buttons, Delta, DeltaRot );
	else
		CalcAxialMoveRot( Viewport, MouseX, MouseY, Buttons, Delta, DeltaRot );
	unguard;
}

/*-----------------------------------------------------------------------------
   Viewport movement computation.
-----------------------------------------------------------------------------*/

#if 1 //U2Ed
extern FVector GBoxSelStart, GBoxSelEnd;
extern UBOOL GbIsBoxSel;
#if ADDITIONS_IMPROVEMENTS
extern UBOOL GbIsFlying;
#endif
#endif

//
// Move and rotate viewport freely.
//
void ViewportMoveRot
(
	UViewport*	Viewport,
	FVector&	Delta,
	FRotator&	DeltaRot
)
{
	guard(ViewportMoveRot);

	Viewport->Actor->ViewRotation.AddBounded( DeltaRot.Pitch, DeltaRot.Yaw, DeltaRot.Roll );
	Viewport->Actor->Location.AddBounded( Delta );

	unguard;
}

//
// Move and rotate viewport using gravity and collision where appropriate.
//
void ViewportMoveRotWithPhysics
(
	UViewport*	Viewport,
	FVector&	Delta,
	FRotator&	DeltaRot
)
{
	guard(ViewportMoveRotWithPhysics);

	Viewport->Actor->ViewRotation.AddBounded( 4.0f*DeltaRot.Pitch, 4.0f*DeltaRot.Yaw, 4.0f*DeltaRot.Roll );
	Viewport->Actor->Location.AddBounded( Delta );

	unguard;
}

/*-----------------------------------------------------------------------------
   Scale functions.
-----------------------------------------------------------------------------*/

//
// See if a scale is within acceptable bounds:
//
UBOOL ScaleIsWithinBounds( FVector* V, FLOAT Min, FLOAT Max )
{
	guard(ScaleIsWithinBounds);
	FLOAT Temp;

	Temp = Abs(V->X);
	if( Temp<Min || Temp>Max )
		return 0;

	Temp = Abs (V->Y);
	if( Temp<Min || Temp>Max )
		return 0;

	Temp = Abs (V->Z);
	if( Temp<Min || Temp>Max )
		return 0;

	return 1;
	unguard;
}

/*-----------------------------------------------------------------------------
   Change transacting.
-----------------------------------------------------------------------------*/

//
// If this is the first time called since first click, note all selected actors.
//
void UEditorEngine::NoteActorMovement( ULevel* Level )
{
	guard(NoteActorMovement);
	if( !GUndo && !(GEditor->ClickFlags & CF_MOVE_ACTOR) )
	{
		GEditor->ClickFlags |= CF_MOVE_ACTOR;
		GEditor->Trans->Begin( TEXT("Actor movement") );
		GSnapping=0;
		INT i;
		for( i=0; i<Level->Actors.Num(); i++ )
		{
			AActor* Actor = Level->Actors(i);
			if( Actor && Actor->bSelected )
				break;
		}
		if( i==Level->Actors.Num() )
		{
			Level->Brush()->Modify();
			Level->Brush()->bSelected = 1;
			GEditor->NoteSelectionChange( Level );
		}
		for( i=0; i<Level->Actors.Num(); i++ )
		{
			AActor* Actor = Level->Actors(i);
			if( Actor && Actor->bSelected && Actor->bEdShouldSnap )
				GSnapping = 1;
		}
		for( i=0; i<Level->Actors.Num(); i++ )
		{
			AActor* Actor = Level->Actors(i);
			if( Actor && Actor->bSelected )
			{
				Actor->Modify();
				if( Actor->IsBrush() )
					Actor->Brush->Polys->Element.ModifyAllItems();
				Actor->bEdSnap |= GSnapping;
			}
		}
		GEditor->Trans->End();
	}
	unguard;
}

//
// Finish snapping all brushes in a level.
//
void UEditorEngine::FinishAllSnaps( ULevel* Level )
{
	guard(UEditorEngine::FinishAllSnaps);
	ClickFlags &= ~CF_MOVE_ACTOR;
	for( INT i=0; i<Level->Actors.Num(); i++ )
		if( Level->Actors(i) && Level->Actors(i)->bSelected )
			Level->Actors(i)->PostEditMove();
	unguard;
}

//
// Set the editor's pivot location.
//
void UEditorEngine::SetPivot( FVector NewPivot, UBOOL SnapPivotToGrid, UBOOL DoMoveActors )
{
	guard(UEditorEngine::SetPivot);

	// Set the pivot.
	GPivotLocation   = NewPivot;
	GPivotRotation   = FRotator(0,0,0);
	GGridBase        = FVector(0,0,0);
	GSnappedLocation = GPivotLocation;
	GSnappedRotation = GPivotRotation;
	if( GSnapping || SnapPivotToGrid )
		Constraints.Snap( Level, GSnappedLocation, GGridBase, GSnappedRotation );
	if( SnapPivotToGrid )
	{
		if( DoMoveActors )
			MoveActors( NULL, Level, GSnappedLocation-GPivotLocation, FRotator(0,0,0), 0, NULL );
		GPivotLocation = GSnappedLocation;
		GPivotRotation = GSnappedRotation;
	}
	else
	{
		GGridBase = GPivotLocation - GSnappedLocation;
		GSnappedLocation = GPivotLocation;
		Constraints.Snap( Level, GSnappedLocation, GGridBase, GSnappedRotation );
		GPivotLocation = GSnappedLocation;
	}

	// Check all actors.
	INT Count=0, SnapCount=0;
	AActor* SingleActor=NULL;
	for( INT i=0; i<Level->Actors.Num(); i++ )
	{
		if( Level->Actors(i) && Level->Actors(i)->bSelected )
		{
			Count++;
			SnapCount += Level->Actors(i)->bEdShouldSnap;
			SingleActor = Level->Actors(i);
		}
	}

	// Apply to actors.
	if( Count==1 )
	{
		ABrush* Brush=Cast<ABrush>( SingleActor );
		if( Brush )
		{
			FModelCoords Coords, Uncoords;
			Brush->BuildCoords( &Coords, &Uncoords );
			Brush->Modify();
			Brush->PrePivot += (GSnappedLocation - Brush->Location).TransformVectorBy( Uncoords.PointXform );
			Brush->Location = GSnappedLocation;
			Brush->PostEditChange();
		}
	}

	// Update showing.
	GPivotShown = SnapCount>0 || Count>1;
	unguard;
}

//
// Reset the editor's pivot location.
//
void UEditorEngine::ResetPivot()
{
	guard(UEditorEngine::ResetPivot);
	GPivotShown = 0;
	GSnapping   = 0;
	unguard;
}

//
// Move a single actors.
//
void MoveSingleActor( AActor* Actor, FVector Delta, FRotator DeltaRot )
{
	guard(MoveSingleActor);
	if( Delta != FVector(0,0,0) )
	{
		Actor->bDynamicLight = 1;
		Actor->bLightChanged = 1;
	}
	Actor->Location.AddBounded( Delta );

	if( Actor->IsBrush() && !Actor->IsMovingBrush() )
	{
		if( !DeltaRot.IsZero() )
		{
			// Rotate brush vertices
			ABrush* Brush = (ABrush*)Actor;
			for( INT poly = 0 ; poly < Brush->Brush->Polys->Element.Num() ; poly++ )
			{
				FPoly* Poly = &(Brush->Brush->Polys->Element(poly));

				// Rotate the vertices
				for( INT vertex = 0 ; vertex < Poly->NumVertices ; vertex++ )
					Poly->Vertex[vertex] = Brush->PrePivot + ( Poly->Vertex[vertex] - Brush->PrePivot ).TransformVectorBy( GMath.UnitCoords * DeltaRot );
				Poly->Base = Brush->PrePivot + ( Poly->Base - Brush->PrePivot ).TransformVectorBy( GMath.UnitCoords * DeltaRot );

				// Rotate the texture vectors
				Poly->TextureU = Poly->TextureU.TransformVectorBy( GMath.UnitCoords * DeltaRot );
				Poly->TextureV = Poly->TextureV.TransformVectorBy( GMath.UnitCoords * DeltaRot );

				// Recalc the normal for the poly
				Poly->Normal = FVector(0,0,0);	// Force the normal to recalc
				Poly->Finalize(0);
			}

			Brush->Brush->BuildBound();
		}
	}
	else
		Actor->Rotation += DeltaRot;

	if( Cast<APawn>( Actor ) )
		Cast<APawn>( Actor )->ViewRotation = Actor->Rotation;
	unguard;
}

//
// Move and rotate actors.
//
void MoveActors( UViewport* Viewport, ULevel* Level, FVector Delta, FRotator DeltaRot, UBOOL Constrained, AActor* ViewActor, UBOOL bForceSnapping )
{
	guard(MoveActors);

	if( (Delta.IsZero() && DeltaRot.IsZero()) /*|| (Viewport && Viewport->Actor->RendMap == REN_MatineePreview)*/ )
		return;

	// Transact the actors.
	GEditor->NoteActorMovement( Level );

	// Update global pivot.
	if( Constrained )
	{
		FVector OldLocation = GSnappedLocation;
		FRotator OldRotation = GSnappedRotation;
		GSnappedLocation      = (GPivotLocation += Delta   );
		GSnappedRotation      = (GPivotRotation += DeltaRot);
		if( GSnapping || bForceSnapping )
			GEditor->Constraints.Snap( Level, GSnappedLocation, GGridBase, GSnappedRotation );
		Delta                 = GSnappedLocation - OldLocation;
		DeltaRot              = GSnappedRotation - OldRotation;
	}

	if( GbIsBoxSel )
	{
		check(Viewport);

		if( Viewport->IsOrtho() )
			GBoxSelEnd += Delta;
		//else
		//	GBoxSelEnd = Viewport->MouseClientPos;
		return;
	}

	// Move the actors.
	if( Delta!=FVector(0,0,0) || DeltaRot!=FRotator(0,0,0) )
	{
		for( INT i=0; i<Level->Actors.Num(); i++ )
		{
			AActor* Actor = Level->Actors(i);
			if( Actor && (Actor->bSelected || Actor==ViewActor) /*&& !Actor->bLockLocation*/ )
			{
				// Cannot move brushes while in brush clip mode - only regular actors.
				// This allows you to adjust the clipping marker positions, but the brushes
				// will remain locked in place.
				if( GEditor->Mode == EM_BrushClip && Actor->IsBrush() )
					continue;

				// Can't move any actors while in vertex editing mode
				if( (GEditor->Mode == EM_VertexEdit || GEditor->Mode == EM_FaceDrag) && Actor->IsBrush() )
				{
					Actor->Brush->Polys->Element.ModifyAllItems();
					Actor->Brush->Modify();

					for( INT vertex = 0 ; vertex < VertexHitList.Num() ; vertex++ )
						if( VertexHitList(vertex).pBrush == (ABrush*)Actor )
						{
							FVector* Vtx = &(VertexHitList(vertex).pBrush->Brush->Polys->Element(VertexHitList(vertex).PolyIndex).Vertex[VertexHitList(vertex).VertexIndex]);

							FVector Vertex = Vtx->TransformPointBy( VertexHitList(vertex).pBrush->ToWorld() );
							Vertex += Delta;
							*Vtx = Vertex.TransformPointBy( VertexHitList(vertex).pBrush->ToLocal() );
						}

					Actor->Brush->PostEditChange();
					continue;
				}

				FVector Arm   = GSnappedLocation - Actor->Location;
				FVector Rel   = Arm - Arm.TransformVectorBy(GMath.UnitCoords * DeltaRot);
				MoveSingleActor( Actor, Delta + Rel, DeltaRot );
			}
		}
	}

	// If we have bezier control points selected, move them.
#if 0
	if( BezierControlPointList.Num() )
	{
		for( INT x = 0 ; x < BezierControlPointList.Num() ; x++ )
		{
			UMatAction* MA = BezierControlPointList(x).MA;
			if( BezierControlPointList(x).bStart )
			{
				MA->EndControlPoint += Delta;
				if( MA->bSmoothCorner )	MA->StartControlPoint = MA->EndControlPoint * -1;
			}
			else
			{
				MA->StartControlPoint += Delta;
				if( MA->bSmoothCorner )	MA->EndControlPoint = MA->StartControlPoint * -1;
			}
		}
		if( GMatineeTools.GetCurrent() )
			GMatineeTools.GetCurrent()->PreparePath();
	}
#endif

	unguard;
}

#if 1 //U2Ed (Vertex Editing)
//FVector VertexEditWkPos;
FVector GSaveSnappedPosition;
//UBOOL bVtxDragging = 0;
#endif

#if 1 // added by Legend 1/31/1999
/*-----------------------------------------------------------------------------
   Vertex editing functions.
-----------------------------------------------------------------------------*/

#if SUPPORTS_PRAGMA_PACK
#pragma pack(push,1)
#endif

struct FPolyVertex {
	FPolyVertex::FPolyVertex( INT i, INT j ) : PolyIndex(i), VertexIndex(j) {};
	INT PolyIndex;
	INT VertexIndex;
};

#if SUPPORTS_PRAGMA_PACK
#pragma pack(pop)
#endif

static AActor* VertexEditActor=NULL;
static TArray<FPolyVertex> VertexEditList;

//
// Find the selected brush and grab the closest vertex when <Alt> is pressed
//
void GrabVertex( ULevel* Level )
{
	guard(GrabVertex);

	if( VertexEditActor!=NULL )
		return;

	// Find the selected brush -- abort if none is found.
	AActor* Actor=NULL;
	for( INT i=0; i<Level->Actors.Num(); i++ )
	{
		Actor = Level->Actors(i);
		if( Actor && Actor->bSelected && Actor->IsBrush() )
		{
			VertexEditActor = Actor;
			break;
		}
	}
	if( VertexEditActor==NULL )
		return;

	//!! Tim, Undo doesn't seem to work for vertex editing.  Do I need to set RF_Transactional? //WDM
	VertexEditActor->Brush->Modify();

	// examine all the points and grab those that are within range of the pivot location
	UPolys* Polys = VertexEditActor->Brush->Polys;
	for( INT i=0; i<Polys->Element.Num(); i++ ) 
	{
		FCoords BrushC(VertexEditActor->ToWorld());
		for( INT j=0; j<Polys->Element(i).NumVertices; j++ ) 
		{
			FVector Location = Polys->Element(i).Vertex[j].TransformPointBy(BrushC);
			// match GPivotLocation against Brush's vertex positions -- find "close" vertex
			if( FDist( Location, GPivotLocation ) < GEditor->Constraints.SnapDistance ) {
				VertexEditList.AddItem( FPolyVertex( i, j ) );
			}
		}
	}

	unguard;
}

INT RecomputePoly( FPoly* Poly, INT i )
{
	// force recalculation of normal, and texture U and V coordinates in FPoly::Finalize()
	Poly->Normal = FVector(0,0,0);

	//if( !GEditor->Constraints.TextureLock )
	//{
	//	Poly->TextureU = FVector(0,0,0);
	//	Poly->TextureV = FVector(0,0,0);
	//}
	
	// catch normalization exceptions to warn about non-planar polys
	try
	{
		return Poly->Finalize( 0 );
	}
	catch(...)
	{
		debugf( TEXT("WARNING: FPoly::Finalize() failed on Poly %d  (You broke the poly!)"), i );
	}

	return 0;
}

//
// Release the vertex when <Alt> is released, then update the brush
//
void ReleaseVertex( ULevel* Level )
{
	guard(ReleaseVertex);

	if( VertexEditActor==NULL )
		return;

	// finalize all the polys in the brush (recompute poly surface and TextureU/V normals)
	UPolys* Polys = VertexEditActor->Brush->Polys;
	for( INT i=0; i<Polys->Element.Num(); i++ ) 
		RecomputePoly( &Polys->Element(i), i );

	VertexEditActor->Brush->BuildBound();

	VertexEditActor=NULL;
	VertexEditList.Empty();

	unguard;
}

//
// Move a vertex.
//
void MoveVertex( ULevel* Level, FVector Delta, UBOOL Constrained )
{
	guard(MoveVertex);

	// Transact the actors.
	GEditor->NoteActorMovement( Level );

	if( VertexEditActor==NULL )
		return;

	// Update global pivot.
	if( Constrained )
	{
		FVector OldLocation = GSnappedLocation;
		GSnappedLocation = ( GPivotLocation += Delta );
		if( GSnapping )
		{
			GGridBase = FVector(0,0,0);
			GEditor->Constraints.Snap( Level, GSnappedLocation, GGridBase, GSnappedRotation );
		}
		Delta = GSnappedLocation - OldLocation;
	}

	// Move the vertex.
	if( Delta!=FVector(0,0,0) )
	{
		// examine all the points
		UPolys* Polys = VertexEditActor->Brush->Polys;

		Polys->Element.ModifyAllItems();

		FModelCoords Uncoords;
		((ABrush*)VertexEditActor)->BuildCoords( NULL, &Uncoords );
		VertexEditActor->Brush->Modify();
		for( INT k=0; k<VertexEditList.Num(); k++ ) 
		{
			INT i = VertexEditList(k).PolyIndex;
			INT j = VertexEditList(k).VertexIndex;
			Polys->Element(i).Vertex[j] += Delta.TransformVectorBy( Uncoords.PointXform );
		}
		VertexEditActor->Brush->PostEditChange();
	}

	unguard;
}
#endif

/*-----------------------------------------------------------------------------
   Editor surface transacting.
-----------------------------------------------------------------------------*/

//
// If this is the first time textures have been adjusted since the user first
// pressed a mouse button, save selected polygons transactionally so this can
// be undone/redone:
//
void NoteTextureMovement( ULevel* Level )
{
	guard(NoteTextureMovement);
	if( !GUndo && !(GEditor->ClickFlags & CF_MOVE_TEXTURE) )
	{
		GEditor->Trans->Begin( TEXT("Texture movement") );
		Level->Model->ModifySelectedSurfs(1);
		GEditor->Trans->End ();
		GEditor->ClickFlags |= CF_MOVE_TEXTURE;
	}
	unguard;
}

#if 1 //U2Ed
// Checks the array of vertices and makes sure that the brushes in that list are still selected.  If not,
// the vertex is removed from the list.
void vertexedit_Refresh()
{
	guard(vertexedit_Refresh);

	for( INT vertex = 0 ; vertex < VertexHitList.Num() ; vertex++ )
		if( !VertexHitList(vertex).pBrush->bSelected )
		{
			VertexHitList.Remove(vertex);
			vertex = 0;
		}

	unguard;
}

// Fills up an array with a unique list of brushes which have vertices selected on them.
void vertexedit_GetBrushList( TArray<ABrush*>* BrushList )
{
	UBOOL bExists;

	BrushList->Empty();

	// Build a list of unique brushes
	//
	for( INT vertex = 0 ; vertex < VertexHitList.Num() ; vertex++ )
	{
		bExists = 0;

		for( INT x = 0 ; x < BrushList->Num() && !bExists ; x++ )
		{
			if( VertexHitList(vertex).pBrush == (*BrushList)(x) )
			{
				bExists = 1;
				break;
			}
		}

		if( !bExists )
			(*BrushList)( BrushList->Add() ) = VertexHitList(vertex).pBrush;
	}
}
#endif

/*-----------------------------------------------------------------------------
   Editor viewport movement.
-----------------------------------------------------------------------------*/

//
// Move the edit-viewport.
//
void UEditorEngine::MouseDelta
(
	UViewport*	Viewport,
	DWORD		Buttons,
	FLOAT		MouseX,
	FLOAT		MouseY
)
{
	guard(UEditorEngine::MouseDelta);

	FVector     	Delta,Vector,SnapMin,SnapMax,DeltaMin,DeltaMax,DeltaFree;
	FRotator		DeltaRot;
	FLOAT			TempFloat,Speed;
	static FLOAT	TextureAngle=0.0;

	if( Viewport->Actor->RendMap==REN_TexView )
	{
		if( Buttons & MOUSE_FirstHit )
		{
			Viewport->SetMouseCapture( 0, 1 );
		}
		else if( Buttons & MOUSE_LastRelease )
		{
			Viewport->SetMouseCapture( 0, 0 );
		}
		return;
	}
	else if( Viewport->Actor->RendMap==REN_TexBrowser )
	{
		return;
	}

	ABrush* BrushActor = Viewport->Actor->GetLevel()->Brush();

	Delta.X    		= 0.0;  Delta.Y  		= 0.0;  Delta.Z   		= 0.0;
	DeltaRot.Pitch	= 0.0;  DeltaRot.Yaw	= 0.0;  DeltaRot.Roll	= 0.0;
	//
	if( Buttons & MOUSE_FirstHit )
	{
		// Reset flags that last for the duration of the click.
		Viewport->SetMouseCapture( 1, 1 );
		ClickFlags &= ~(CF_MOVE_ALL);
		BrushActor->Modify();

		if( Mode==EM_VertexEdit )
		{
			GEditor->Trans->Begin( TEXT("Vertex Editing") );

			for( int vertex = 0 ; vertex < VertexHitList.Num() ; vertex++ )
			{
				VertexHitList(vertex).pBrush->Modify();
				VertexHitList(vertex).pBrush->Brush->Polys->Modify();
			}

			// Move the pivot point to the first vertex in the selection list.
			if( VertexHitList.Num() )
			{
				FCoords BrushW(VertexHitList(0).pBrush->ToWorld());
				FVector Vertex = VertexHitList(0).pBrush->Brush->Polys->Element(VertexHitList(0).PolyIndex).Vertex[VertexHitList(0).VertexIndex].TransformPointBy(BrushW);
				SetPivot( Vertex, 1, 0 );
			}
		}
		else if( Mode==EM_FaceDrag )
		{
			GEditor->Trans->Begin( TEXT("Face Dragging") );

			VertexHitList.Empty();

			if( Viewport->IsOrtho() )
			{
				// Loop through all the faces on the selected brushes.  For each one that qualifies to
				// be dragged, add it's vertices to the vertex editing list.
				for( INT i = 0 ; i < Level->Actors.Num() ; i++ )
				{
					AActor* Actor = Level->Actors(i);
					if( Actor && Actor->bSelected && Actor->IsBrush() )
					{
						FVector ClickLocation = GEditor->ClickLocation.TransformPointBy( Actor->ToLocal() );

						if( Viewport->Actor->RendMap == REN_OrthXY )
							ClickLocation.Z = 0;
						else if( Viewport->Actor->RendMap == REN_OrthXZ )
							ClickLocation.Y = 0;
						else
							ClickLocation.X = 0;
	
						for( INT poly = 0 ; poly < Actor->Brush->Polys->Element.Num() ; poly++ )
						{
							FPoly* Poly = &(Actor->Brush->Polys->Element(poly));

							FVector TestVector = Poly->Base - ClickLocation;
							TestVector.Normalize();

							FLOAT Dot = TestVector | Poly->Normal;
							if( Dot < 0.0f
									&& !Poly->IsBackfaced( ClickLocation ) )
							{
								UBOOL bOK = 0;

								// As a final test, attempt to trace a line to the each vertex of the face from the
								// click location.  If we can reach any one of it's vertices, include it.
								for( INT cmppoly = 0 ; cmppoly < Actor->Brush->Polys->Element.Num() && !bOK ; cmppoly++ )
								{
									FPoly* CmpPoly = &(Actor->Brush->Polys->Element(cmppoly));

									if( CmpPoly == Poly )
										continue;

									FVector Center = FVector(0,0,0);
									for( INT cmpvtx = 0 ; cmpvtx < CmpPoly->NumVertices ; cmpvtx++ )
										Center += CmpPoly->Vertex[cmpvtx];
									Center /= CmpPoly->NumVertices;

									FVector Dir = Center - ClickLocation;
									Dir.Normalize();
									if( CmpPoly->DoesLineIntersect( ClickLocation, ClickLocation + (Dir * 16384 ), NULL ) )
									{
										bOK = 1;
										break;
									}
								}

								// We've passed all the tests, so add this face to the hit list.

								if( bOK )
									for( INT vertex = 0 ; vertex < Poly->NumVertices ; vertex++ )
										vertexedit_Click( Viewport, (ABrush*)Actor, Poly->Vertex[vertex], 1, 1 );

							}
						}
					}
				}
			}
		}
		else if( Mode==EM_BrushSnap )
		{
			BrushActor->TempScale = BrushActor->PostScale;
			GForceXSnap           = 0;
			GForceYSnap           = 0;
			GForceZSnap           = 0;
		}
		else if(Mode == EM_TexturePan || Mode == EM_TextureRotate)
		{
			UModel*	Model = Viewport->Actor->GetLevel()->Model;

			// Guarantee that texture points and vectors for selected surfaces
			// are unique.

			OriginalUVectors.Empty(Model->Surfs.Num());
			OriginalVVectors.Empty(Model->Surfs.Num());

			for(INT SurfaceIndex = 0;SurfaceIndex < Model->Surfs.Num();SurfaceIndex++)
			{
				FBspSurf&	Surf = Model->Surfs(SurfaceIndex);

				OriginalUVectors.AddItem(Surf.vTextureU);
				OriginalVVectors.AddItem(Surf.vTextureV);

				if(Surf.PolyFlags & PF_Selected)
				{
					FVector	Base = Model->Points(Surf.pBase),
							TextureU = Model->Vectors(Surf.vTextureU),
							TextureV = Model->Vectors(Surf.vTextureV);

					Surf.pBase = Model->Points.AddItem(Base);
					Surf.vTextureU = Model->Vectors.AddItem(TextureU);
					Surf.vTextureV = Model->Vectors.AddItem(TextureV);
				}
			}

			TextureAngle = 0.0;
		}

		if( Viewport->IsOrtho()
				&& (Viewport->Input->KeyDown(IK_Alt)
				&& Viewport->Input->KeyDown(IK_Ctrl) ) ) 
		{
			// Start box selection
			GbIsBoxSel = true;
			GBoxSelStart = GBoxSelEnd = GEditor->ClickLocation;
		}
	}
	if( Buttons & MOUSE_LastRelease )
	{
		Viewport->SetMouseCapture( 0, 0 );
		FinishAllSnaps( Viewport->Actor->GetLevel() );

		if( Mode==EM_VertexEdit || Mode==EM_FaceDrag )
		{
			TArray<ABrush*> Brushes;
			vertexedit_GetBrushList( &Brushes );

			// Do clean up work on the final list of brushes.
			for( int brush = 0 ; brush < Brushes.Num() ; brush++ )
			{
				UPolys* Polys = Brushes(brush)->Brush->Polys;

				for( INT x = 0 ; x < Polys->Element.Num() ; x++ ) 
				{
					if( Polys->Element(x).Fix() < 3 )
					{
						// This poly is no longer valid, remove it from the brush.
						debugf( TEXT("Warning : Not enough vertices, poly removed"));
						Polys->Element.Remove(x);
						x = 0;
					}
				}
				Brushes(brush)->Brush->BuildBound();
				edactApplyTransformToBrush( Brushes(brush) );
			}

			GEditor->Trans->End();
		}
		else if(Mode == EM_TexturePan || Mode == EM_TextureRotate)
		{
			if(OriginalUVectors.Num() && OriginalVVectors.Num())
			{
				// Finishing up texture manipulation.  Go through and minimize the set of
				// vectors we've been adjusting by merging the new vectors in and eliminating
				// duplicates.

				UModel*		Model = Viewport->Actor->GetLevel()->Model;
				FMemMark	Mark(GMem);

				for(INT SurfaceIndex = 0;SurfaceIndex < Model->Surfs.Num();SurfaceIndex++)
				{
					FBspSurf&	Surf = Model->Surfs(SurfaceIndex);

					if(Surf.PolyFlags & PF_Selected)
					{
						// Update texture coordinates in FPoly.

						polyUpdateMaster(Model,SurfaceIndex,1,1);

						// Eliminate references to duplicate points/vectors.

						Surf.pBase = bspAddPoint(Model,&Model->Points(Surf.pBase),0);
						Surf.vTextureU = bspAddVector(Model,&Model->Vectors(Surf.vTextureU),0);
						Surf.vTextureV = bspAddVector(Model,&Model->Vectors(Surf.vTextureV),0);
					}
				}

				Mark.Pop();

				OriginalUVectors.Empty();
				OriginalVVectors.Empty();
			}
		}
		if( GbIsBoxSel )
		{
			GbIsBoxSel = false;
			GEditor->edactBoxSelect( Viewport, GEditor->Level, GBoxSelStart, GBoxSelEnd );
			EdCallback( EDC_RedrawAllViewports, 0 );
		}
	}

	switch( Mode )
	{
		case EM_None:
			debugf( NAME_Warning, TEXT("Editor is disabled") );
			break;
		case EM_BrushClip:
			goto ViewportMove;
		case EM_VertexEdit:
		case EM_FaceDrag:
			{
				if( !Viewport->Input->KeyDown(IK_Ctrl) 
						|| GbIsBoxSel )
					goto ViewportMove;

				CalcFreeMoveRot( Viewport, MouseX, MouseY, Buttons, Delta, DeltaRot );
				MoveActors( Viewport, Level, Delta, DeltaRot, 1, (Buttons & MOUSE_Shift) ? Viewport->Actor : NULL, 1 );

				// If we're using the sizingbox, the brush bounding boxes need to be constantly
				// updated so the size will be shown properly while dragging vertices.
#if 0
				if( GUnrealEd->UseSizingBox )
				{
					TArray<ABrush*> Brushes;
					vertexedit_GetBrushList( &Brushes );
					for( INT brush = 0 ; brush < Brushes.Num() ; brush++ )
						Brushes(brush)->Brush->BuildBound();
				}
#endif
			}
			break;
		case EM_ViewportMove:
		case EM_Matinee:

			if( Buttons & MOUSE_Alt )
			{
				GrabVertex( Viewport->Actor->GetLevel() );
			}
			// release the vertex if either the mouse button or <Alt> key is released
			else if( Buttons & MOUSE_LastRelease || !( Buttons & MOUSE_Alt ) )
			{
				ReleaseVertex( Viewport->Actor->GetLevel() );
			}
		case EM_ViewportZoom:
			ViewportMove:
			if( GbIsBoxSel )
			{
				CalcFreeMoveRot( Viewport, MouseX, MouseY, Buttons, Delta, DeltaRot );
				MoveActors( Viewport, Level, Delta, DeltaRot, 0, NULL );
			}
			else if( Buttons & (MOUSE_FirstHit | MOUSE_LastRelease | MOUSE_SetMode | MOUSE_ExitMode) )
			{
				Viewport->Actor->Velocity = FVector(0,0,0);
			}
			else
			{
				if( Buttons & MOUSE_Alt )
				{
					if( !GbIsBoxSel )
					{
						// Move selected vertex.
						CalcFreeMoveRot( Viewport, MouseX, MouseY, Buttons, Delta, DeltaRot );
						Delta *= 0.25f*MovementSpeed;
					}
				}
				else
#if ADDITIONS_IMPROVEMENTS
				if( !(Buttons & (MOUSE_Ctrl | MOUSE_Shift) ) || GbIsFlying )
#else
				if( !(Buttons & (MOUSE_Ctrl | MOUSE_Shift) ) )
#endif
				{
					// Move camera.
					Speed = 0.30*MovementSpeed;
					if( Viewport->IsOrtho() && Buttons==MOUSE_Right )
					{
						Buttons = MOUSE_Left;
						Speed   = 0.60*MovementSpeed;
					}
					CalcFreeMoveRot( Viewport, MouseX, MouseY, Buttons, Delta, DeltaRot );
					Delta *= Speed;
				}
				else
				{
					// Move actors.
					CalcMixedMoveRot( Viewport, MouseX, MouseY, Buttons, Delta, DeltaRot );
					Delta *= 0.25*MovementSpeed;
				}
				if( Mode==EM_ViewportZoom )
				{
					Delta = (Viewport->Actor->Velocity += Delta);
				}
				if( Buttons & MOUSE_Alt )
				{
					if( !GbIsBoxSel )
					{
						// Move selected vertex.
						MoveVertex( Level, Delta, 1 );
					}
				}
				else
#if ADDITIONS_IMPROVEMENTS
				if( !(Buttons & (MOUSE_Ctrl | MOUSE_Shift) ) || GbIsFlying )
#else
				if( !(Buttons & (MOUSE_Ctrl | MOUSE_Shift) ) )
#endif
				{
					// Move camera.
					ViewportMoveRotWithPhysics( Viewport, Delta, DeltaRot );
				}
				else
				{
					// Move actors.
					MoveActors( Viewport, Level, Delta, DeltaRot, 1, (Buttons & MOUSE_Shift) ? Viewport->Actor : NULL );
				}
			}
			break;
		case EM_BrushRotate:
			if( !(Buttons&MOUSE_Ctrl) )
				goto ViewportMove;
			CalcAxialRot( Viewport, MouseX, MouseY, Buttons, DeltaRot );
			if( DeltaRot != FRotator(0,0,0) )
			{
				NoteActorMovement( Level );
 				MoveActors( Viewport, Level, FVector(0,0,0), DeltaRot, 1, (Buttons & MOUSE_Shift) ? Viewport->Actor : NULL );
			}
			break;
		case EM_BrushSheer:
			if( !(Buttons&MOUSE_Ctrl) )
				goto ViewportMove;
			NoteActorMovement( Level );
#if 1 // added by Legend 1/31/1999
			for( INT i=0; i<Level->Actors.Num(); i++ )
			{
				AActor* Actor = Level->Actors(i);
				if( Actor && Actor->IsBrush() && Actor->bSelected ) {
		   			((ABrush*)Actor)->Modify();
					((ABrush*)Actor)->MainScale.SheerRate = Clamp( ((ABrush*)Actor)->MainScale.SheerRate + (FLOAT)(-MouseY) / 240.0f, -4.0f, 4.0f );
				}
			}
#else
   			BrushActor->Modify();
			BrushActor->MainScale.SheerRate = Clamp( BrushActor->MainScale.SheerRate + (FLOAT)(-MouseY) / 240.0f, -4.0f, 4.0f );
#endif
			break;
		case EM_BrushScale:
			if( !(Buttons&MOUSE_Ctrl) )
				goto ViewportMove;
			NoteActorMovement( Level );
#if 1 // added by Legend 1/31/1999
			for( INT i=0; i<Level->Actors.Num(); i++ )
			{
				AActor* Actor = Level->Actors(i);
				if( Actor && Actor->bSelected )
				{
					if( Actor->IsBrush() )
					{
						ABrush* Brush = Cast<ABrush>(Actor);
		   				Brush->Modify();
						Vector = Brush->MainScale.Scale * (1 + (FLOAT)(-MouseY) / 256.0f);
						if( ScaleIsWithinBounds(&Vector,0.05f,400.0f) )
						{
							Brush->MainScale.Scale = Vector;
						}
					}
					if( Buttons&MOUSE_Alt )
					{
						Actor->Location *= (1 + (FLOAT)(-MouseY) / 256.0f);
					}
				}
			}
#else
   			BrushActor->Modify();
			Vector = BrushActor->MainScale.Scale * (1 + (FLOAT)(-MouseY) / 256.0);
			if( ScaleIsWithinBounds(&Vector,0.05,400.0) )
				BrushActor->MainScale.Scale = Vector;
#endif
			break;
		case EM_BrushStretch:
			if (!(Buttons&MOUSE_Ctrl))
				goto ViewportMove;
			NoteActorMovement( Level );
#if 1 // added by Legend 1/31/1999
			CalcAxialMoveRot( Viewport, MouseX, MouseY, Buttons, Delta, DeltaRot );
			for( INT i=0; i<Level->Actors.Num(); i++ )
			{
				AActor* Actor = Level->Actors(i);
				if( Actor && Actor->bSelected ) {
					if( Actor->IsBrush() )
					{
						ABrush* Brush = Cast<ABrush>(Actor);
						Brush->Modify();
						Vector = Brush->MainScale.Scale;
						Vector.X *= (1 + Delta.X / 256.0f);
						Vector.Y *= (1 + Delta.Y / 256.0f);
						Vector.Z *= (1 + Delta.Z / 256.0f);
						if( ScaleIsWithinBounds( &Vector, 0.05f, 400.0f ) )
						{
							Brush->MainScale.Scale = Vector;
						}
					}
					if( Buttons&MOUSE_Alt )
					{
						Actor->Location.X *= (1 + Delta.X / 256.0f);
						Actor->Location.Y *= (1 + Delta.Y / 256.0f);
						Actor->Location.Z *= (1 + Delta.Z / 256.0f);
					}
				}
			}
#else
			BrushActor->Modify();
			CalcAxialMoveRot( Viewport, MouseX, MouseY, Buttons, Delta, DeltaRot );
			Vector = BrushActor->MainScale.Scale;
			Vector.X *= (1 + Delta.X / 256.0);
			Vector.Y *= (1 + Delta.Y / 256.0);
			Vector.Z *= (1 + Delta.Z / 256.0);
			if( ScaleIsWithinBounds(&Vector,0.05,400.0) )
				BrushActor->MainScale.Scale = Vector;
#endif
			break;
		case EM_BrushSnap:
			if( !(Buttons&MOUSE_Ctrl) )
				goto ViewportMove;
			NoteActorMovement( Level );
#if 1 // added by Legend 1/31/1999
			CalcAxialMoveRot( Viewport, MouseX, MouseY, Buttons, Delta, DeltaRot );
			for( INT i=0; i<Level->Actors.Num(); i++ )
			{
				AActor* Actor = Level->Actors(i);
				if( Actor && Actor->IsBrush() && Actor->bSelected ) {
		   			((ABrush*)Actor)->Modify();
					Vector = ((ABrush*)Actor)->TempScale.Scale;
					Vector.X *= (1 + Delta.X / 400.0f);
					Vector.Y *= (1 + Delta.Y / 400.0f);
					Vector.Z *= (1 + Delta.Z / 400.0f);
					if( ScaleIsWithinBounds(&Vector,0.05f,400.0f) )
					{
						((ABrush*)Actor)->TempScale.Scale = Vector;
						((ABrush*)Actor)->PostScale.Scale = Vector;
						if( Viewport->Actor->GetLevel()->Brush()->Brush->Polys->Element.Num()==0 )
							break;
						FBox Box = ((ABrush*)Actor)->Brush->GetRenderBoundingBox( Actor, 1 );

						SnapMin   = Box.Min; Constraints.Snap(SnapMin,FVector(0,0,0));
						DeltaMin  = ((ABrush*)Actor)->Location - SnapMin;
						DeltaFree = ((ABrush*)Actor)->Location - Box.Min;
						SnapMin.X = ((ABrush*)Actor)->PostScale.Scale.X * DeltaMin.X/DeltaFree.X;
						SnapMin.Y = ((ABrush*)Actor)->PostScale.Scale.Y * DeltaMin.Y/DeltaFree.Y;
						SnapMin.Z = ((ABrush*)Actor)->PostScale.Scale.Z * DeltaMin.Z/DeltaFree.Z;

						SnapMax   = Box.Max; Constraints.Snap(SnapMax,FVector(0,0,0));
						DeltaMax  = ((ABrush*)Actor)->Location - SnapMax;
						DeltaFree = ((ABrush*)Actor)->Location - Box.Max;
						SnapMax.X = ((ABrush*)Actor)->PostScale.Scale.X * DeltaMax.X/DeltaFree.X;
						SnapMax.Y = ((ABrush*)Actor)->PostScale.Scale.Y * DeltaMax.Y/DeltaFree.Y;
						SnapMax.Z = ((ABrush*)Actor)->PostScale.Scale.Z * DeltaMax.Z/DeltaFree.Z;

						// Set PostScale so brush extents are grid snapped in all directions of movement.
						if( GForceXSnap || Delta.X!=0 )
						{
							GForceXSnap = 1;
							if( (SnapMin.X>0.05) &&
								((SnapMax.X<=0.05) ||
								(Abs(SnapMin.X-((ABrush*)Actor)->PostScale.Scale.X) < Abs(SnapMax.X-((ABrush*)Actor)->PostScale.Scale.X))))
								((ABrush*)Actor)->PostScale.Scale.X = SnapMin.X;
							else if( SnapMax.X>0.05 )
								((ABrush*)Actor)->PostScale.Scale.X = SnapMax.X;
						}
						if( GForceYSnap || Delta.Y!=0 )
						{
							GForceYSnap = 1;
							if( (SnapMin.Y>0.05) &&
								((SnapMax.Y<=0.05) ||
								(Abs(SnapMin.Y-((ABrush*)Actor)->PostScale.Scale.Y) < Abs(SnapMax.Y-((ABrush*)Actor)->PostScale.Scale.Y))))
								((ABrush*)Actor)->PostScale.Scale.Y = SnapMin.Y;
							else if( SnapMax.Y>0.05 )
								((ABrush*)Actor)->PostScale.Scale.Y = SnapMax.Y;
						}
						if( GForceZSnap || Delta.Z!=0 )
						{
							GForceZSnap = 1;
							if( (SnapMin.Z>0.05) &&
								((SnapMax.Z<=0.05) ||
								(Abs(SnapMin.Z-((ABrush*)Actor)->PostScale.Scale.Z) < Abs(SnapMax.Z-((ABrush*)Actor)->PostScale.Scale.Z))) )
								((ABrush*)Actor)->PostScale.Scale.Z = SnapMin.Z;
							else if( SnapMax.Z>0.05 )
								((ABrush*)Actor)->PostScale.Scale.Z = SnapMax.Z;
						}
					}
				}
			}
#else
			BrushActor->Modify();
			CalcAxialMoveRot( Viewport, MouseX, MouseY, Buttons, Delta, DeltaRot );
			Vector = BrushActor->TempScale.Scale;
			Vector.X *= (1 + Delta.X / 400.0);
			Vector.Y *= (1 + Delta.Y / 400.0);
			Vector.Z *= (1 + Delta.Z / 400.0);
			if( ScaleIsWithinBounds(&Vector,0.05,400.0) )
			{
				BrushActor->TempScale.Scale = Vector;
				BrushActor->PostScale.Scale = Vector;
				if( Viewport->Actor->GetLevel()->Brush()->Brush->Polys->Element.Num()==0 )
					break;
				FBox Box = BrushActor->Brush->GetRenderBoundingBox( BrushActor, 1 );

				SnapMin   = Box.Min; Constraints.Snap(SnapMin,FVector(0,0,0));
				DeltaMin  = BrushActor->Location - SnapMin;
				DeltaFree = BrushActor->Location - Box.Min;
				SnapMin.X = BrushActor->PostScale.Scale.X * DeltaMin.X/DeltaFree.X;
				SnapMin.Y = BrushActor->PostScale.Scale.Y * DeltaMin.Y/DeltaFree.Y;
				SnapMin.Z = BrushActor->PostScale.Scale.Z * DeltaMin.Z/DeltaFree.Z;

				SnapMax   = Box.Max; Constraints.Snap(SnapMax,FVector(0,0,0));
				DeltaMax  = BrushActor->Location - SnapMax;
				DeltaFree = BrushActor->Location - Box.Max;
				SnapMax.X = BrushActor->PostScale.Scale.X * DeltaMax.X/DeltaFree.X;
				SnapMax.Y = BrushActor->PostScale.Scale.Y * DeltaMax.Y/DeltaFree.Y;
				SnapMax.Z = BrushActor->PostScale.Scale.Z * DeltaMax.Z/DeltaFree.Z;

				// Set PostScale so brush extents are grid snapped in all directions of movement.
				if( GForceXSnap || Delta.X!=0 )
				{
					GForceXSnap = 1;
					if( (SnapMin.X>0.05) &&
						((SnapMax.X<=0.05) ||
						(Abs(SnapMin.X-BrushActor->PostScale.Scale.X) < Abs(SnapMax.X-BrushActor->PostScale.Scale.X))))
						BrushActor->PostScale.Scale.X = SnapMin.X;
					else if( SnapMax.X>0.05 )
						BrushActor->PostScale.Scale.X = SnapMax.X;
				}
				if( GForceYSnap || Delta.Y!=0 )
				{
					GForceYSnap = 1;
					if( (SnapMin.Y>0.05) &&
						((SnapMax.Y<=0.05) ||
						(Abs(SnapMin.Y-BrushActor->PostScale.Scale.Y) < Abs(SnapMax.Y-BrushActor->PostScale.Scale.Y))))
						BrushActor->PostScale.Scale.Y = SnapMin.Y;
					else if( SnapMax.Y>0.05 )
						BrushActor->PostScale.Scale.Y = SnapMax.Y;
				}
				if( GForceZSnap || Delta.Z!=0 )
				{
					GForceZSnap = 1;
					if( (SnapMin.Z>0.05) &&
						((SnapMax.Z<=0.05) ||
						(Abs(SnapMin.Z-BrushActor->PostScale.Scale.Z) < Abs(SnapMax.Z-BrushActor->PostScale.Scale.Z))) )
						BrushActor->PostScale.Scale.Z = SnapMin.Z;
					else if( SnapMax.Z>0.05 )
						BrushActor->PostScale.Scale.Z = SnapMax.Z;
				}
			}
#endif
			break;
		case EM_TexturePan:
		{
			if( !(Buttons&MOUSE_Ctrl) )
				goto ViewportMove;
			NoteTextureMovement( Level );
			if( (Buttons & MOUSE_Left) && (Buttons & MOUSE_Right) )
			{
				check(OriginalUVectors.Num()==Viewport->Actor->GetLevel()->Model->Surfs.Num());
				check(OriginalVVectors.Num()==Viewport->Actor->GetLevel()->Model->Surfs.Num());

				GFixScale += Fix(MouseY) / 32;
				TempFloat = 1.0;
				INT Temp = Unfix(GFixScale); 
				if( Constraints.GridEnabled )
				{
					while( Temp > 0 ) { TempFloat *= 0.5; Temp--; }
					while( Temp < 0 ) { TempFloat *= 2.0; Temp++; }
				}
				else
				{
					while( Temp > 0 ) { TempFloat *= 0.95f; Temp--; }
					while( Temp < 0 ) { TempFloat *= 1.05f; Temp++; }
				}
				if( TempFloat != 1.0 )
					polyTexScale(Viewport->Actor->GetLevel()->Model,TempFloat,0.0,0.0,TempFloat,0);
				GFixScale &= 0xffff;
			}
			else if( Buttons & MOUSE_Left )
			{
				FLOAT Mod = 1;
				if( Buttons & MOUSE_Shift )
					Mod = .05f;

				GFixPanU += Fix(MouseX)/(16.f*Mod);  GFixPanV += Fix(MouseY)/(16.f*Mod);
				polyTexPan(Viewport->Actor->GetLevel()->Model,Unfix(GFixPanU),Unfix(0),0);
				GFixPanU &= 0xffff; GFixPanV &= 0xffff;
			}
			else
			{
				FLOAT Mod = 1;
				if( Buttons & MOUSE_Shift )
					Mod = .05f;

				GFixPanU += Fix(MouseX)/(16.f*Mod);  GFixPanV += Fix(MouseY)/(16.f*Mod);
				polyTexPan(Viewport->Actor->GetLevel()->Model,Unfix(0),Unfix(GFixPanV),0);
				GFixPanU &= 0xffff; GFixPanV &= 0xffff;
			}
			break;
		}
		case EM_TextureRotate:
		{
			if( !(Buttons&MOUSE_Ctrl) )
				goto ViewportMove;
			check(OriginalUVectors.Num()==Viewport->Actor->GetLevel()->Model->Surfs.Num());
			check(OriginalVVectors.Num()==Viewport->Actor->GetLevel()->Model->Surfs.Num());
			NoteTextureMovement( Level );
			TextureAngle += (FLOAT)MouseX / 256.0;
			for( INT i=0; i<Viewport->Actor->GetLevel()->Model->Surfs.Num(); i++ )
			{
				FBspSurf* Surf = &Viewport->Actor->GetLevel()->Model->Surfs(i);
				if( Surf->PolyFlags & PF_Selected )
				{
					FVector U		=  Viewport->Actor->GetLevel()->Model->Vectors(OriginalUVectors(i));
					FVector V		=  Viewport->Actor->GetLevel()->Model->Vectors(OriginalVVectors(i));
					FVector* NewU	= &Viewport->Actor->GetLevel()->Model->Vectors(Surf->vTextureU);
					FVector* NewV	= &Viewport->Actor->GetLevel()->Model->Vectors(Surf->vTextureV);
					*NewU			= U * appCos(TextureAngle) + V * appSin(TextureAngle);
					*NewV			= V * appCos(TextureAngle) - U * appSin(TextureAngle);

					polyUpdateMaster(Viewport->Actor->GetLevel()->Model,i,1,1);
				}
			}
			break;
		}
		default:
			debugf( NAME_Warning, TEXT("Unknown editor mode %i"), Mode );
			goto ViewportMove;
			break;
	}
	if( Viewport->Actor->RendMap != REN_MeshView  )
	{
		Viewport->Actor->Rotation = Viewport->Actor->ViewRotation;
		Viewport->Actor->GetLevel()->SetActorZone( Viewport->Actor, 0, 0 );
	}

	unguardf(( TEXT("(Mode=%i)"), Mode ));
}

//
// Mouse position.
//
void UEditorEngine::MousePosition( UViewport* Viewport, DWORD Buttons, FLOAT X, FLOAT Y )
{
	guard(UEditorEngine::MousePosition);
	if( edcamMode(Viewport)==EM_TexView )
	{
		UTexture* Texture = (UTexture *)Viewport->MiscRes;
		X *= (FLOAT)Texture->USize/Viewport->SizeX;
		Y *= (FLOAT)Texture->VSize/Viewport->SizeY;
		if( X>=0 && X<Texture->USize && Y>=0 && Y<Texture->VSize )
			Texture->MousePosition( Buttons, X, Y );
	}
	unguard;
}

/*-----------------------------------------------------------------------------
   Keypress handling.
-----------------------------------------------------------------------------*/

//
// Handle a regular ASCII key that was pressed in UnrealEd.
// Returns 1 if proceesed, 0 if not.
//
INT UEditorEngine::Key( UViewport* Viewport, EInputKey Key )
{
	guard(UEditorEngine::Key);
	if( Viewport->Input->KeyDown(IK_Alt) )
	{
#if 1 //U2Ed
		FString Cmd;
		switch( Key )
		{
			case IK_1:	Cmd = TEXT("RMODE 1");	break;
			case IK_2:	Cmd = TEXT("RMODE 2");	break;
			case IK_3:	Cmd = TEXT("RMODE 3");	break;
			case IK_4:	Cmd = TEXT("RMODE 4");	break;
			case IK_5:	Cmd = TEXT("RMODE 5");	break;
			case IK_6:	Cmd = TEXT("RMODE 6");	break;
			case IK_7:	Cmd = TEXT("RMODE 13");	break;
			case IK_8:	Cmd = TEXT("RMODE 14");	break;
			case IK_9:	Cmd = TEXT("RMODE 15");	break;
			default:	return 0;
		}

		int iRet = Viewport->Exec( *Cmd );
		EdCallback( EDC_ViewportUpdateWindowFrame, 1 );
		return iRet;
#else
		switch( Key )
		{
			case IK_1:	return Viewport->Exec( TEXT("RMODE 1") );
			case IK_2:	return Viewport->Exec( TEXT("RMODE 2") );
			case IK_3:	return Viewport->Exec( TEXT("RMODE 3") );
			case IK_4:	return Viewport->Exec( TEXT("RMODE 4") );
			case IK_5:	return Viewport->Exec( TEXT("RMODE 5") );
			default:	break;
		}
#endif
	}
	if( UEngine::Key( Viewport, Key ) )
	{
		return 1;
	}
	else if( Viewport->Actor->RendMap==REN_TexView )
	{
		return 0;
	}
	else if( Viewport->Actor->RendMap==REN_TexBrowser )
	{
		if( appToUpper(Key)=='Q' && CurrentTexture )
		{
			debugf(TEXT("Removing texture %s from level"),CurrentTexture->GetFullName());
			for( TArray<AActor*>::TIterator ItA(Viewport->Actor->GetLevel()->Actors); ItA; ++ItA )
			{
				AActor* Actor = *ItA;
				if( Actor )
				{
					UModel* M = Actor->IsA(ALevelInfo::StaticClass()) ? Actor->GetLevel()->Model : Actor->Brush;
					if( M )
					{
						for( TArray<FBspSurf>::TIterator ItS(M->Surfs); ItS; ++ItS )
							if( ItS->Texture==CurrentTexture )
								ItS->Texture = NULL;
						if( M->Polys )
							for( TArray<FPoly>::TIterator ItP(M->Polys->Element); ItP; ++ItP )
								if( ItP->Texture==CurrentTexture )
									ItP->Texture = NULL;
					}
				}
			}
			RedrawLevel(NULL);
		}
		return 0;
	}
/*	else if( Viewport->Actor->RendMap==REN_MeshView )
	{
		if( Mesh )
			switch( appToUpper( Key ) )
			{
				case 'S': Actor->ShowFlags ^= SHOW_Bones; return 1;
				case 'B': Actor->ShowFlags ^= SHOW_Bounds; return 1;
			}
		return 0;
	}*/
#if 0 // now using accelerator tables
#if 1 //U2Ed
#if ADDITIONS_IMPROVEMENTS
	else if( !GbIsFlying && Viewport->Input->KeyDown(IK_Shift) )
#else
	else if( Viewport->Input->KeyDown(IK_Shift) )
#endif
	{
		if( Viewport->Input->KeyDown(IK_A) ) {  Exec( TEXT("ACTOR SELECT ALL") ); return 1; }
		if( Viewport->Input->KeyDown(IK_B) ) {  Exec( TEXT("POLY SELECT MATCHING BRUSH") ); return 1; }
		if( Viewport->Input->KeyDown(IK_C) ) {  Exec( TEXT("POLY SELECT ADJACENT COPLANARS") ); return 1; }
		if( Viewport->Input->KeyDown(IK_D) ) {  Exec( TEXT("ACTOR DUPLICATE") ); return 1; }
		if( Viewport->Input->KeyDown(IK_F) ) {  Exec( TEXT("POLY SELECT ADJACENT FLOORS") ); return 1; }
		if( Viewport->Input->KeyDown(IK_G) ) {  Exec( TEXT("POLY SELECT MATCHING GROUPS") ); return 1; }
		if( Viewport->Input->KeyDown(IK_I) ) {  Exec( TEXT("POLY SELECT MATCHING ITEMS") ); return 1; }
		if( Viewport->Input->KeyDown(IK_J) ) {  Exec( TEXT("POLY SELECT ADJACENT ALL") ); return 1; }
		if( Viewport->Input->KeyDown(IK_M) ) {  Exec( TEXT("POLY SELECT MEMORY SET") ); return 1; }
		if( Viewport->Input->KeyDown(IK_N) ) {  Exec( TEXT("SELECT NONE") ); return 1; }
		if( Viewport->Input->KeyDown(IK_O) ) {  Exec( TEXT("POLY SELECT MEMORY INTERSECT") ); return 1; }
		if( Viewport->Input->KeyDown(IK_Q) ) {  Exec( TEXT("POLY SELECT REVERSE") ); return 1; }
		if( Viewport->Input->KeyDown(IK_R) ) {  Exec( TEXT("POLY SELECT MEMORY RECALL") ); return 1; }
		if( Viewport->Input->KeyDown(IK_S) ) {  Exec( TEXT("POLY SELECT ALL") ); return 1; }
		if( Viewport->Input->KeyDown(IK_T) ) {  Exec( TEXT("POLY SELECT MATCHING TEXTURE") ); return 1; }
		if( Viewport->Input->KeyDown(IK_U) ) {  Exec( TEXT("POLY SELECT MEMORY UNION") ); return 1; }
		if( Viewport->Input->KeyDown(IK_W) ) {  Exec( TEXT("POLY SELECT ADJACENT WALLS") ); return 1; }
		if( Viewport->Input->KeyDown(IK_Y) ) {  Exec( TEXT("POLY SELECT ADJACENT SLANTS") ); return 1; }
		if( Viewport->Input->KeyDown(IK_X) ) {  Exec( TEXT("POLY SELECT MEMORY XOR") ); return 1; }

		return 0;
	}
	else if( Viewport->Input->KeyDown(IK_Ctrl) )
	{
		if( Viewport->Input->KeyDown(IK_C) ) { Exec( TEXT("EDIT COPY") );	return 1; }
		if( Viewport->Input->KeyDown(IK_V) ) { Exec( TEXT("EDIT PASTE") );	return 1; }
		if( Viewport->Input->KeyDown(IK_W) ) { Exec( TEXT("ACTOR DUPLICATE") );	return 1; }
		if( Viewport->Input->KeyDown(IK_X) ) { Exec( TEXT("EDIT CUT") );	return 1; }
		if( Viewport->Input->KeyDown(IK_Y) ) { Exec( TEXT("TRANSACTION REDO") );	return 1; }
		if( Viewport->Input->KeyDown(IK_Z) ) { Exec( TEXT("TRANSACTION UNDO") );	return 1; }
		if( Viewport->Input->KeyDown(IK_A) ) { Exec( TEXT("BRUSH ADD") );	return 1; }
		if( Viewport->Input->KeyDown(IK_S) ) { Exec( TEXT("BRUSH SUBTRACT") );	return 1; }
		if( Viewport->Input->KeyDown(IK_I) ) { Exec( TEXT("BRUSH FROM INTERSECTION") );	return 1; }
		if( Viewport->Input->KeyDown(IK_D) ) { Exec( TEXT("BRUSH FROM DEINTERSECTION") );	return 1; }
		if( Viewport->Input->KeyDown(IK_L) ) { GEditor->EdCallback( EDC_SaveMap, 1 );	return 1; }
		if( Viewport->Input->KeyDown(IK_E) ) { GEditor->EdCallback( EDC_SaveMapAs, 1 );	return 1; }
		if( Viewport->Input->KeyDown(IK_O) ) { GEditor->EdCallback( EDC_LoadMap, 1 );	return 1; }
		if( Viewport->Input->KeyDown(IK_P) ) { GEditor->EdCallback( EDC_PlayMap, 1 );	return 1; }

		return 0;
	}
	else if( !Viewport->Input->KeyDown(IK_Alt) )
	{
		if( Viewport->Input->KeyDown(IK_Delete) ) { Exec( TEXT("ACTOR DELETE") );	return 1; }
#if 0 //U2Ed -- We don't like this, so we disabled it.
		if( Viewport->Input->KeyDown(IK_1) ) { MovementSpeed = 1; return 1; }
		if( Viewport->Input->KeyDown(IK_2) ) {  MovementSpeed = 4; return 1; }
		if( Viewport->Input->KeyDown(IK_3) ) {  MovementSpeed = 16; return 1; }
#endif
		if( Viewport->Input->KeyDown(IK_B) ) {  Viewport->Actor->ShowFlags ^= SHOW_Brush; return 1; }
		if( Viewport->Input->KeyDown(IK_H) ) {  Viewport->Actor->ShowFlags ^= SHOW_Actors; return 1; }
		if( Viewport->Input->KeyDown(IK_K) ) {  Viewport->Actor->ShowFlags ^= SHOW_Backdrop; return 1; }
		if( Viewport->Input->KeyDown(IK_P) ) {  Viewport->Actor->ShowFlags ^= SHOW_PlayerCtrl; EdCallback( EDC_ViewportUpdateWindowFrame, 1 ); return 1; }

		return 0;
	}

	return 0;
#else
	else if( Viewport->Input->KeyDown(IK_Shift) )
	{
		switch( appToUpper(Key) )
		{
			case 'A': Exec( TEXT("ACTOR SELECT ALL") ); return 1;
			case 'B': Exec( TEXT("POLY SELECT MATCHING BRUSH") ); return 1;
			case 'C': Exec( TEXT("POLY SELECT ADJACENT COPLANARS") ); return 1;
			case 'D': Exec( TEXT("ACTOR DUPLICATE") ); return 1;
			case 'F': Exec( TEXT("POLY SELECT ADJACENT FLOORS") ); return 1;
			case 'G': Exec( TEXT("POLY SELECT MATCHING GROUPS") ); return 1;
			case 'I': Exec( TEXT("POLY SELECT MATCHING ITEMS") ); return 1;
			case 'J': Exec( TEXT("POLY SELECT ADJACENT ALL") ); return 1;
			case 'M': Exec( TEXT("POLY SELECT MEMORY SET") ); return 1;
			case 'N': Exec( TEXT("SELECT NONE") ); return 1;
			case 'O': Exec( TEXT("POLY SELECT MEMORY INTERSECT") ); return 1;
			case 'Q': Exec( TEXT("POLY SELECT REVERSE") ); return 1;
			case 'R': Exec( TEXT("POLY SELECT MEMORY RECALL") ); return 1;
			case 'S': Exec( TEXT("POLY SELECT ALL") ); return 1;
			case 'T': Exec( TEXT("POLY SELECT MATCHING TEXTURE") ); return 1;
			case 'U': Exec( TEXT("POLY SELECT MEMORY UNION") ); return 1;
			case 'W': Exec( TEXT("POLY SELECT ADJACENT WALLS") ); return 1;
			case 'Y': Exec( TEXT("POLY SELECT ADJACENT SLANTS") ); return 1;
			case 'X': Exec( TEXT("POLY SELECT MEMORY XOR") ); return 1;
			case 'Z': Exec( TEXT("SELECT NONE") ); return 1;
			default: return 0;
		}
	}
	else if( Viewport->Input->KeyDown(IK_Shift) )
	{
		switch( appToUpper( Key ) )
		{
			case 'C': Exec( TEXT("EDIT COPY") ); return 1;
			case 'V': Exec( TEXT("EDIT PASTE") ); return 1;
			case 'X': Exec( TEXT("EDIT CUT") ); return 1;
			case 'L': Viewport->Actor->ViewRotation.Pitch = 0; Viewport->Actor->ViewRotation.Roll  = 0; return 1;
			default: return 0;
		}
	}
	else if( !Viewport->Input->KeyDown(IK_Alt) )
	{
		switch( appToUpper(Key) )
		{
			case IK_Delete: Exec( TEXT("ACTOR DELETE") ); return 1;
			case '1': MovementSpeed = 1; return 1;
			case '2': MovementSpeed = 4; return 1;
			case '3': MovementSpeed = 16; return 1;
			case 'B': Viewport->Actor->ShowFlags ^= SHOW_Brush; return 1;
			case 'H': Viewport->Actor->ShowFlags ^= SHOW_Actors; return 1;
			case 'K': Viewport->Actor->ShowFlags ^= SHOW_Backdrop; return 1;
			case 'P': Viewport->Actor->ShowFlags ^= SHOW_PlayerCtrl; return 1;
			default: return 0;
		}
	}
	else
	{
		return 0;
	}
#endif
#else
	return 0;
#endif
	unguard;
}

/*-----------------------------------------------------------------------------
	Common drawing functions
-----------------------------------------------------------------------------*/

void UEditorEngine::edDrawAxisIndicator(FSceneNode* Frame)
{
	guard(UEditorEngine::DrawAxisIndicator);

	if( Frame->Viewport->IsOrtho() )
	{
		FLOAT SizeX = Frame->Viewport->SizeX;
		FLOAT SizeY = Frame->Viewport->SizeY;
		UCanvas* Canvas = Frame->Viewport->Canvas;

		INT XL, YL;
		Canvas->WrappedStrLenf( Canvas->SmallFont, XL, YL, TEXT("M") );

		FVector Origin = FVector(16.0f,SizeY - 16,0.0f);
		FVector XAxis = FVector(32.0f,SizeY - 16,0.0f) - Origin;
		FVector YAxis = FVector(16.0f,SizeY - 32,0.0f) - Origin;

		FString AxisLabels[3];
		switch( Frame->Viewport->Actor->RendMap )
		{
			case REN_OrthXY:	AxisLabels[0] = TEXT("X");	AxisLabels[1] = TEXT("Y");	AxisLabels[2] = TEXT("Z");	break;
			case REN_OrthXZ:	AxisLabels[0] = TEXT("X");	AxisLabels[1] = TEXT("Z");	AxisLabels[2] = TEXT("Y");	break;
			case REN_OrthYZ:	AxisLabels[0] = TEXT("Y");	AxisLabels[1] = TEXT("Z");	AxisLabels[2] = TEXT("X");	break;
		}

		Canvas->Color = FColor(255,255,255);

		Canvas->SetClip( 36, SizeY-20, XL, YL );
		Canvas->WrappedPrintf( Canvas->SmallFont, 0, *AxisLabels[0] );

		Canvas->SetClip( 14, SizeY-44, XL, YL );
		Canvas->WrappedPrintf( Canvas->SmallFont, 0, *AxisLabels[1] );

		Canvas->SetClip( 8, SizeY-16, XL, YL );
		Canvas->WrappedPrintf( Canvas->SmallFont, 0, *AxisLabels[2] );

		switch( Frame->Viewport->Actor->RendMap )
		{
			case REN_OrthXY:
				Frame->Viewport->RenDev->Draw2DClippedLine(Frame, FPlane(255,0,0,255), LINE_None, Origin, Origin + XAxis);
				Frame->Viewport->RenDev->Draw2DClippedLine(Frame, FPlane(0,255,0,255), LINE_None, Origin, Origin + YAxis);
				break;

			case REN_OrthXZ:
				Frame->Viewport->RenDev->Draw2DClippedLine(Frame, FPlane(255,0,0,255), LINE_None, Origin, Origin + XAxis);
				Frame->Viewport->RenDev->Draw2DClippedLine(Frame, FPlane(0,0,255,255), LINE_None, Origin, Origin + YAxis);
				break;

			case REN_OrthYZ:
				Frame->Viewport->RenDev->Draw2DClippedLine(Frame, FPlane(0,255,0,255), LINE_None, Origin, Origin + XAxis);
				Frame->Viewport->RenDev->Draw2DClippedLine(Frame, FPlane(0,0,255,255), LINE_None, Origin, Origin + YAxis);
				break;
		}

		Canvas->SetClip( 0, 0, SizeX, SizeY );
	}
	else
	{
		FLOAT SizeY = Frame->Viewport->SizeY;
		FCoords GizmoCoords = GMath.ViewCoords / Frame->Viewport->Actor->ViewRotation / FVector(44,SizeY-44,10);
		Draw3DAxis( Frame, GizmoCoords );
	}

	unguard;
}

void UEditorEngine::Draw3DAxis(FSceneNode* Frame, FCoords& AxisCoords )
{
	guard(UEditorEngine::Draw3DAxis)
	UCanvas* Canvas = Frame->Viewport->Canvas;

	INT XL, YL;
	Canvas->WrappedStrLenf( Canvas->SmallFont, XL, YL, TEXT("M") );

	const FLOAT NEAR_CLIPPING_PLANE = 10.0f;
	FVector XAxis = FVector(5.0f,0.0f,0.0f).TransformVectorBy(AxisCoords) * NEAR_CLIPPING_PLANE;
	FVector YAxis = FVector(0.0f,5.0f,0.0f).TransformVectorBy(AxisCoords) * NEAR_CLIPPING_PLANE;
	FVector ZAxis = FVector(0.0f,0.0f,5.0f).TransformVectorBy(AxisCoords) * NEAR_CLIPPING_PLANE;

	Frame->Viewport->RenDev->Draw2DClippedLine(Frame, FPlane(255,0,0,255), LINE_None, AxisCoords.Origin, AxisCoords.Origin + XAxis);
	Frame->Viewport->RenDev->Draw2DClippedLine(Frame, FPlane(0,255,0,255), LINE_None, AxisCoords.Origin, AxisCoords.Origin + YAxis);
	Frame->Viewport->RenDev->Draw2DClippedLine(Frame, FPlane(0,0,255,255), LINE_None, AxisCoords.Origin, AxisCoords.Origin + ZAxis);

	FVector C = AxisCoords.Origin + XAxis;
	Canvas->SetClip(C.X,C.Y,XL,YL);
	Canvas->WrappedPrintf(Canvas->SmallFont,0,TEXT("X"));
	
	C = AxisCoords.Origin + YAxis;
	Canvas->SetClip(C.X,C.Y,XL,YL);
	Canvas->WrappedPrintf(Canvas->SmallFont,0,TEXT("Y"));
	
	C = AxisCoords.Origin + ZAxis;
	Canvas->SetClip(C.X,C.Y,XL,YL);
	Canvas->WrappedPrintf(Canvas->SmallFont,0,TEXT("Z"));

	Canvas->SetClip( 0, 0, Frame->Viewport->SizeX, Frame->Viewport->SizeY );
	unguard;
}

/*-----------------------------------------------------------------------------
   Texture browser routines.
-----------------------------------------------------------------------------*/

void DrawViewerBackground( FSceneNode* Frame )
{
	guard(DrawViewerBackground);
	Frame->Viewport->Canvas->DrawPattern( GEditor->Bkgnd, 0, 0, Frame->X, Frame->Y, 1.0f, 0.0f, 0.0f, NULL, 1.0f, FPlane(.4f,.4f,.4f,0), FPlane(0,0,0,0), 0 );
	unguard;
}

inline INT Compare(UTexture* A, UTexture* B)
{
	// Show masked first.
	INT Mask = (B->PolyFlags & PF_Masked) - (A->PolyFlags & PF_Masked);
	if( Mask != 0 )
		return Mask;
	return appStricmp( A->GetName(), B->GetName() );
}

void DrawTextureBrowser( FSceneNode* Frame )
{
	guard(DrawTextureBrowser);
	UObject* Pkg = Frame->Viewport->MiscRes;
	if( Pkg && Frame->Viewport->Group!=NAME_None )
		Pkg = FindObject<UObject>(Pkg, *Frame->Viewport->Group); // changed to UObject so it works with fonts

	FMemMark Mark(GMem);
	enum {MAX=16384};
	UTexture**  List    = new(GMem,MAX)UTexture*;

	// Make a short list of filtered textures.
	INT n = 0;
	for( TObjectIterator<UTexture> It; It && n<MAX; ++It )
		if( It->IsIn(Pkg) )
		{
			FString TexName = It->GetName();

			if( appStrstr( *(TexName.Caps()), *(GTexNameFilter.Caps()) ) )
				List[n++] = *It;
		}

	// Sort textures by name.
	Sort( List, n );

	Frame->Viewport->Canvas->Color = FColor(255,255,255);

	// This is how I hacked up the 2 versions of the texture browser.  If you set the
	// zoom (Misc1) to more than 1000, then it goes into "variable size" mode.  Subtracting 1000
	// from the zoom will give you the scaling percentage to use.
	//
	if( Frame->Viewport->Actor->Misc1 > 1000 )	// New way
	{
		INT YL = 1;
		int TextBuffer = -1;
		int X, Y, HighYInRow = 0;
		float Scale = 1.0f * ((float)(Frame->Viewport->Actor->Misc1 - 1000) / 100.f);
		GLastScroll = 0;
		if( YL > 0 )
		{
			X = 4;
			Y = 4 - Frame->Viewport->Actor->Misc2;
			HighYInRow = -1;

			for( INT i = 0 ; i < n ; i++ )
			{
				UTexture* Texture = List[i];

				// Create and measure the 2 labels.
				FString TextLabel = Texture->GetName();
				int LabelWidth, LabelHeight;
				Frame->Viewport->Canvas->WrappedStrLenf( Frame->Viewport->Canvas->SmallFont, LabelWidth, LabelHeight, TEXT("%s"), *TextLabel );

				int SizeWidth, SizeHeight;
				FString SizeLabel = FString::Printf( TEXT("(%ix%i)"), Texture->USize, Texture->VSize );
				Frame->Viewport->Canvas->WrappedStrLenf( Frame->Viewport->Canvas->SmallFont, SizeWidth, SizeHeight, TEXT("%s"), *SizeLabel );

				if( TextBuffer == -1)
					TextBuffer = LabelHeight + SizeHeight + 4;

				// Display the texture wide enough to show it's entire text label without wrapping.
				int TextureWidth = Max( (int)(Texture->USize*Scale), (LabelWidth > SizeWidth) ? LabelWidth : SizeWidth );

				// Do we need to create a new line?
				if( X + TextureWidth > Frame->X )
				{
					X = 4;
					Y += HighYInRow + TextBuffer + 8;
					GLastScroll += HighYInRow + TextBuffer + 8;
					HighYInRow = -1;
				}
				if( (Texture->VSize*Scale) > HighYInRow ) HighYInRow = (Texture->VSize*Scale);

				PUSH_HIT(Frame,HBrowserTexture(Texture));

				// SELECTION HIGHLIGHT
				if( Texture == GEditor->CurrentTexture )
					Frame->Viewport->Canvas->DrawPattern( GEditor->BkgndHi,
						X-4, Y-4,
						TextureWidth+8, (Texture->VSize*Scale)+TextBuffer+8,
						1.0, 0.0, 0.0, NULL, 1.0, FPlane(1.,1.,1.,0), FPlane(0,0,0,0), 0 );
				else if( Texture->PolyFlags & PF_Masked )
					Frame->Viewport->Canvas->DrawPattern( GEditor->BkgndHi,
						X-4, Y-4,
						TextureWidth+8, (Texture->VSize*Scale)+TextBuffer+8,
						1.0, 0.0, 0.0, NULL, 1.0, FPlane(1.,0.,1.,0), FPlane(0,0,0,0), 0 );

				// THE TEXTURE ITSELF
				Frame->Viewport->Canvas->DrawIcon( Texture,
					X, Y,
					(Texture->USize*Scale), (Texture->VSize*Scale),
					NULL, 1.0, FPlane(1.,1.,1.,0), FPlane(0,0,0,0), 0 );


				// TEXT LABELS
				// If this texture is the current texture, draw a black border around the
				// text to make it more readable against the background.
				if( Texture == GEditor->CurrentTexture )
				{
					int Offsets[] = { 1, 0, -1, 0, 0, 1, 0, -1 };
					Frame->Viewport->Canvas->Color = FColor(0,0,0);
					for( int x = 0 ; x < 4 ; x++ )
					{
						Frame->Viewport->Canvas->SetClip( X+Offsets[x*2], Y+(Texture->VSize*Scale)+2+Offsets[(x*2)+1], TextureWidth, TextBuffer );
						Frame->Viewport->Canvas->WrappedPrintf( Frame->Viewport->Canvas->SmallFont, 0, TEXT("%s"), *TextLabel );

						Frame->Viewport->Canvas->SetClip( X+Offsets[x*2], Y+(Texture->VSize*Scale)+LabelHeight+4+Offsets[(x*2)+1], TextureWidth, TextBuffer );
						Frame->Viewport->Canvas->WrappedPrintf( Frame->Viewport->Canvas->SmallFont, 0, TEXT("%s"), *SizeLabel );
					}
				}

				Frame->Viewport->Canvas->Color = FColor(255,255,255);
				Frame->Viewport->Canvas->SetClip( X, Y+(Texture->VSize*Scale)+2, TextureWidth, TextBuffer );
				Frame->Viewport->Canvas->WrappedPrintf( Frame->Viewport->Canvas->SmallFont, 0, TEXT("%s"), *TextLabel );

				// Render the size in white if this is the selected texture
				if( Texture != GEditor->CurrentTexture )
					Frame->Viewport->Canvas->Color = FColor(192,192,192);
				Frame->Viewport->Canvas->SetClip( X, Y+(Texture->VSize*Scale)+LabelHeight+4, TextureWidth, TextBuffer );
				Frame->Viewport->Canvas->WrappedPrintf( Frame->Viewport->Canvas->SmallFont, 0, TEXT("%s"), *SizeLabel );

				Frame->Viewport->Canvas->Color = FColor(255,255,255);
				Frame->Viewport->Canvas->SetClip( 0, 0, Frame->X, Frame->Y );

				// Update position
				X += TextureWidth + 8;

				POP_HIT(Frame);
			}
		}
		GLastScroll += HighYInRow + TextBuffer + 8;
		GLastScroll = Max(0, GLastScroll - Frame->Y);
	}
	else	// Old way
	{
		INT			Size	= Frame->Viewport->Actor->Misc1;
		INT			PerRow	= Frame->X/Size;
		if( PerRow < 1 ) return;
		INT			Space	= (Frame->X - Size*PerRow)/(PerRow+1);
		INT			VSkip	= (Size>=64) ? 10 : 0;

		INT YL = Space+(Size+Space+VSkip)*((n+PerRow-1)/PerRow);
		if( YL > 0 )
		{
			INT YOfs = -((Frame->Viewport->Actor->Misc2*Frame->Y)/512);
			for( INT i=0; i<n; i++ )
			{
				UTexture* Texture = List[i];
				INT X = (Size+Space)*(i%PerRow);
				INT Y = (Size+Space+VSkip)*(i/PerRow)+YOfs;
				if( Y+Size+Space+VSkip>0 && Y<Frame->Y )
				{
					PUSH_HIT(Frame,HBrowserTexture(Texture));
					if( Texture==GEditor->CurrentTexture )
						Frame->Viewport->Canvas->DrawPattern( GEditor->BkgndHi, X+1, Y+1, Size+Space*2-2, Size+Space*2+VSkip-2, 1.0, 0.0, 0.0, NULL, 1.0, FPlane(1.,1.,1.,0), FPlane(0,0,0,0), 0 );
					else if( Texture->PolyFlags & PF_Masked )
						Frame->Viewport->Canvas->DrawPattern( GEditor->BkgndHi, X+1, Y+1, Size+Space*2-2, Size+Space*2+VSkip-2, 1.0, 0.0, 0.0, NULL, 1.0, FPlane(1.,0.,1.,0), FPlane(0,0,0,0), 0 );
					FLOAT Scale=0.125;
					while( Texture->USize/Scale>Size || Texture->VSize/Scale>Size )
						Scale *= 2;
					Frame->Viewport->Canvas->DrawPattern( Texture, X+Space, Y+Space, Size, Size, Scale, X+Space, Y+Space, NULL, 1.0, FPlane(1.,1.,1.,0), FPlane(0,0,0,0), 0 );
					if( Size>=64 )
					{
						FString Temp = Texture->GetName();
						if( Size>=128 )
							Temp += FString::Printf( TEXT(" (%ix%i)"), Texture->USize, Texture->VSize );

						Frame->Viewport->Canvas->Color = FColor(255,255,255);
						Frame->Viewport->Canvas->SetClip( X+Space, Y+Space+Size, Size, Frame->Y-Y-Size-Space-1 );
						Frame->Viewport->Canvas->CurX = Size/2;
						Frame->Viewport->Canvas->CurY = 0;
						Frame->Viewport->Canvas->WrappedPrintf( Frame->Viewport->Canvas->SmallFont, 1, TEXT("%s"), *Temp );
						Frame->Viewport->Canvas->SetClip( 0, 0, Frame->X, Frame->Y );
					}
					POP_HIT(Frame);
				}
			}
		}
		GLastScroll = Max(0,(512*(YL-Frame->Y))/Frame->Y);
	}
	Mark.Pop();
	unguard;
}

/*-----------------------------------------------------------------------------
   Buttons.
-----------------------------------------------------------------------------*/

// Menu toggle button.
struct HMenuToggleButton : public HHitProxy
{
	void Click( const FHitCause& Cause )
	{
		Cause.Viewport->Actor->ShowFlags ^= SHOW_Menu;
		Cause.Viewport->UpdateWindowFrame();
	}
};

// Player control button.
struct HPlayerControlButton : public HHitProxy
{
	void Click( const FHitCause& Cause )
	{
		Cause.Viewport->Actor->ShowFlags ^= SHOW_PlayerCtrl;
		Cause.Viewport->Logf( NAME_Log, TEXT("Player controls are %s"), (Cause.Viewport->Actor->ShowFlags&SHOW_PlayerCtrl) ? TEXT("On") : TEXT("Off") );
	}
};

// warren-ui
/*
// Draw an onscreen mouseable button.
static INT DrawButton( FSceneNode* Frame, UTexture* Texture, INT X, INT Y )
{
	guard(DrawButton);
	Frame->Viewport->Canvas->DrawIcon( Texture, X+0.5, Y+0.5, Texture->USize, Texture->VSize, NULL, 0.0, FPlane(0.9,0.9,0.9,0), FPlane(0,0,0,0), 0 );
	return Texture->USize+2;
	unguard;
}

// Draw all buttons.
static void DrawButtons( FSceneNode* Frame )
{
	guard(DrawButtons);

	INT ButtonX=2;

	// Menu toggle button.
	PUSH_HIT(Frame,HMenuToggleButton());
	ButtonX += DrawButton( Frame, (Frame->Viewport->Actor->ShowFlags&SHOW_Menu) ? GEditor->MenuUp : GEditor->MenuDn, ButtonX, 2 );
	POP_HIT(Frame);

	// Player control button.
	PUSH_HIT(Frame,HPlayerControlButton());
	if( !Frame->Viewport->IsOrtho() )
		ButtonX += DrawButton( Frame, (Frame->Viewport->Actor->ShowFlags&SHOW_PlayerCtrl) ? GEditor->PlyrOn : GEditor->PlyrOff, ButtonX, 2 );
	POP_HIT(Frame);

	unguard;
}
*/
// warren-ui

/*-----------------------------------------------------------------------------
   Viewport frame drawing.
-----------------------------------------------------------------------------*/

#if 1
//!! hack to avoid modifying EditorEngine.uc, and rebuilding Editor.u
// (see UnEdRend.cpp for further details).
extern FLOAT EdClipZ;
#endif

//
// Draw the camera view.
//
void UEditorEngine::Draw( UViewport* Viewport, UBOOL Blit, BYTE* HitData, INT* HitCount )
{
	FVector			OriginalLocation;
	FRotator		OriginalRotation;
	DWORD			ShowFlags=0;
	guard(UEditorEngine::Draw);
	APlayerPawn* ViewportActor = Viewport->Actor;
	ShowFlags = ViewportActor->ShowFlags;

	// Lock the camera.
	DWORD LockFlags = 0;
	FPlane ScreenClear(0,0,0,0);
	if( ViewportActor->RendMap==REN_MeshView || ViewportActor->RendMap==REN_ParticleView )
	{
		LockFlags |= LOCKR_ClearScreen;
	}
	if(	Viewport->IsOrtho()
	||	Viewport->IsWire()
	|| !(Viewport->Actor->ShowFlags & SHOW_Backdrop) )
	{
		ScreenClear = Viewport->IsOrtho() ? C_OrthoBackground.Plane() : C_WireBackground.Plane();
		LockFlags |= LOCKR_ClearScreen;
	}
	if( !Viewport->Lock(FVector(.5,.5,.5),FVector(0,0,0),ScreenClear,LockFlags,HitData,HitCount) )
	{
		return;
	}

#if UNDYING_MEM
	FMemMark MemMark(GMem);
	FMemMark DynMark(GDynMem);
	FMemMark SceneMark(GSceneMem);
#endif

	FSceneNode* Frame = Render->CreateMasterFrame( Viewport, Viewport->Actor->Location, Viewport->Actor->ViewRotation, NULL );
	Render->PreRender( Frame );
	Viewport->Canvas->Update( Frame );
	switch( ViewportActor->RendMap )
	{
		case REN_TexView:
		{
			guard(REN_TexView);
			check(Viewport->MiscRes!=NULL);
			ViewportActor->bHiddenEd = 1;
			ViewportActor->bHidden   = 1;
			UTexture* Texture = (UTexture*)Viewport->MiscRes;
			PUSH_HIT(Frame,HTextureView(Texture,Frame->X,Frame->Y));
			Viewport->Canvas->DrawIcon( Texture->Get(Viewport->CurrentTime), 0, 0, Frame->X, Frame->Y, NULL, 1.0f, FPlane(1,1,1,0), FPlane(0,0,0,0), PF_TwoSided );
			POP_HIT(Frame);
			unguard;
			break;
		}
		case REN_TexBrowser:
		{
			guard(REN_TexBrowser);
			ViewportActor->bHiddenEd = 1;
			ViewportActor->bHidden   = 1;
			DrawViewerBackground( Frame );
			DrawTextureBrowser( Frame );
			unguard;
			break;
		}

		case REN_MatineeIP:
		{
			guard(REN_MatineeIP);
			Viewport->Actor->bHiddenEd = 1;
			Viewport->Actor->bHidden   = 1;
			matDrawMatineeIP( Frame );
			unguard;
			break;
		}

		case REN_MeshView:
		{
			guard(REN_MeshView);
			FLOAT DeltaTime = Viewport->CurrentTime - Viewport->LastUpdateTime;

			// Rotate the view.
			FVector NewLocation = Viewport->Actor->ViewRotation.Vector() * (-ViewportActor->Location.Size());
			if( FDist(ViewportActor->Location, NewLocation) > 0.05 )
				ViewportActor->Location = NewLocation;

			// Get animation.
			UMesh* Mesh = (UMesh*)Viewport->MiscRes;
						
			if(!Mesh) break;

			const FMeshAnimSeq* Seq = Mesh->GetAnimSeq( ViewportActor->Misc1 );
			if( ViewportActor->AnimSequence != Seq->Name )
			{
				DWI::AnimChannelSpec Spec;
				Spec.bLoop = true; // (Mesh->Flags & 6) == 0;
				Spec.bModulate = false; // (Mesh->Flags & 8) != 0;
				Spec.bStack = false; // (Mesh->Flags & 4) != 0;
				Spec.move = MOVE_None; // (Mesh->Flags & 1) != 0 ? MOVE_Anim : MOVE_None
				ViewportActor->PlayAnim(Seq->Name, Spec);
			}

			// Auto rotate if wanted.
			if( ViewportActor->ShowFlags & SHOW_Brush )
				ViewportActor->ViewRotation.Yaw += Clamp(DeltaTime,0.f,0.2f) * 8192.0;

			// Do coordinates.
			Frame->ComputeRenderCoords( Viewport->Actor->Location, Viewport->Actor->ViewRotation );
			PUSH_HIT(Frame,HCoords(Frame));

			// Remember.
			OriginalLocation		= ViewportActor->Location;
			OriginalRotation		= ViewportActor->ViewRotation;
			ViewportActor->Location			= FVector(0,0,0);
			ViewportActor->bHiddenEd		= 1;
			ViewportActor->bHidden			= 1;
			ViewportActor->bSelected        = 0;
			ViewportActor->bMeshCurvy       = 0;
			ViewportActor->DrawType			= DT_Mesh;
			ViewportActor->Mesh				= Mesh;
			ViewportActor->Region			= FPointRegion(NULL,INDEX_NONE,0);
			ViewportActor->bCollideWorld	= 0;
			ViewportActor->bCollideActors	= 0;
			ViewportActor->AmbientGlow      = 255;	

			// Update mesh.
			DWI::AnimSequence* Info = Seq ? Seq->GetInfo() : NULL;
			INT NumFrames = Info ? Info->numFrames : 1;
			//INT FrameIndex = ViewportActor->Misc2 < 0 ? NumFrames + ViewportActor->Misc2%NumFrames : ViewportActor->Misc2;
			UAnimState* AnimState = ViewportActor->GetAnimState();
			DWI::AnimChannel* Channel = AnimState->FindCommand<DWI::AnimChannel>();
			FLOAT AnimFrame = Channel ? Channel->ElapsedTime : 0.0;
	


			if     ( ShowFlags & SHOW_Frame  )	Viewport->Actor->RendMap = REN_Wire;
			else if( ShowFlags & SHOW_Coords )	Viewport->Actor->RendMap = REN_Polys;
			else								Viewport->Actor->RendMap = REN_PlainTex;

			// Draw it.
#if STATS && UNREAL_MESH_CODE
			GStat.MeshSubCount = GStat.MeshVertCount = 0;
#endif
			Render->DrawActor( Frame, Viewport->Actor );
			Viewport->Canvas->CurX = Frame->X/2;
			Viewport->Canvas->CurY = Frame->Y-12.0f;
			Viewport->Canvas->Color = FColor(255,255,255);
			Viewport->Canvas->WrappedPrintf
			(
				Viewport->Canvas->MedFont,
				1,
				TEXT("%s, Seq %i, Frame %i/%i, Verts %i/%i"),
				Viewport->MiscRes->GetName(),
				Viewport->Actor->Misc1,
				(INT)(AnimFrame* NumFrames),
				NumFrames,
#if STATS && UNREAL_MESH_CODE
				GStat.MeshSubCount,
				GStat.MeshVertCount
#else
				0,
				0
#endif
			);
			Viewport->Actor->RendMap = REN_MeshView;
			ViewportActor->Location		   = OriginalLocation;
			ViewportActor->DrawType		   = DT_None;
			POP_HIT(Frame);
			unguard;
			break;
		}

		case REN_ParticleView:
		{
			guard(REN_ParticleView);
			FLOAT DeltaTime = Viewport->CurrentTime - Viewport->LastUpdateTime;

			Viewport->Actor->bHiddenEd = 1;
			Viewport->Actor->bHidden   = 1;

			edDrawAxisIndicator(Frame);

			// Rotate the view.
			FVector NewLocation = Viewport->Actor->ViewRotation.Vector() * (-ViewportActor->Location.Size());
			if( FDist(ViewportActor->Location, NewLocation) > 0.05 )
				ViewportActor->Location = NewLocation;

			// Get particle.
			AParticleFX* Particle = (AParticleFX*)Viewport->MiscRes;
			if(!Particle) break;

			// Auto rotate if wanted.
			if( ViewportActor->ShowFlags & SHOW_Brush )
				ViewportActor->ViewRotation.Yaw += Clamp(DeltaTime,0.f,0.2f) * 8192.0;

			// Do coordinates.
			Frame->ComputeRenderCoords( Viewport->Actor->Location, Viewport->Actor->ViewRotation );
			PUSH_HIT(Frame,HCoords(Frame));

			// Remember.
			Particle->Location			= FVector(0,0,0);
			Particle->Rotation			= FRotator(16384,0,0);
			Particle->bHiddenEd			= 1;
			Particle->bHidden			= 1;
			Particle->bSelected			= 0;
			Particle->bMeshCurvy		= 0;
			Particle->Region			= FPointRegion(NULL,INDEX_NONE,0);
			Particle->bCollideWorld		= 0;
			Particle->bCollideActors	= 0;
			Particle->AmbientGlow		= 255;	

			if     ( ShowFlags & SHOW_Frame  )	Viewport->Actor->RendMap = REN_Wire;
			else if( ShowFlags & SHOW_Coords )	Viewport->Actor->RendMap = REN_Polys;
			else								Viewport->Actor->RendMap = REN_PlainTex;

			// Direction arrow.
			if( 0 )
			{
				PUSH_HIT(Frame,HActor(ViewportActor->GetHitActor()));
				FVector V = Particle->Location, A(0,0,0), B(0,0,0);
				FCoords C = GMath.UnitCoords / Particle->Rotation;
				Viewport->RenDev->Draw3DLine( Frame, C_ActorArrow.Plane(), LINE_None, V + C.XAxis * 48, V );
				Viewport->RenDev->Draw3DLine( Frame, C_ActorArrow.Plane(), LINE_None, V + C.XAxis * 48, V + C.XAxis * 16 + C.YAxis * 16 );
				Viewport->RenDev->Draw3DLine( Frame, C_ActorArrow.Plane(), LINE_None, V + C.XAxis * 48, V + C.XAxis * 16 - C.YAxis * 16 );
				Viewport->RenDev->Draw3DLine( Frame, C_ActorArrow.Plane(), LINE_None, V + C.XAxis * 48, V + C.XAxis * 16 + C.ZAxis * 16 );
				Viewport->RenDev->Draw3DLine( Frame, C_ActorArrow.Plane(), LINE_None, V + C.XAxis * 48, V + C.XAxis * 16 - C.ZAxis * 16 );
				POP_HIT(Frame);
			}

			// Draw it.
			Render->DrawActor( Frame, Particle );
			Viewport->Actor->RendMap = REN_ParticleView;
			POP_HIT(Frame);
			unguard;
			break;
		}
		default:
		{
			ViewportActor->bHiddenEd = Viewport->IsOrtho();

			// Draw background.
			if
			(	Viewport->IsOrtho()
				||	Viewport->IsWire()
				|| !(Viewport->Actor->ShowFlags & SHOW_Backdrop) )
			{
				DrawWireBackground( Frame );
				// Clearing the ZBuffer makes the world grid lines in the 3D view ALWAYS appear
				// behind world geometry ... this is to make other renderers (which have ZBuffers) 
				// act like the software renderer in this respect.
				if( !Viewport->IsOrtho() )
					Viewport->RenDev->ClearZ( Frame );
			}

			PUSH_HIT(Frame,HCoords(Frame));

			// Draw the level.
			UBOOL bStaticBrushes = Viewport->IsWire();
			UBOOL bMovingBrushes = (Viewport->Actor->ShowFlags & SHOW_MovingBrushes)!=0;
			UBOOL bActiveBrush   = (Viewport->Actor->ShowFlags & SHOW_Brush)!=0;
			if( !Viewport->IsWire() )
				Render->DrawWorld( Frame );
			//if( bStaticBrushes || bMovingBrushes || bActiveBrush )
			DrawLevelBrushes( Frame, bStaticBrushes, bMovingBrushes, bActiveBrush );

			// Draw all paths.
			if( (Viewport->Actor->ShowFlags&SHOW_Paths) && Viewport->Actor->GetLevel()->ReachSpecs.Num() )
			{
				for( INT i=0; i<Viewport->Actor->GetLevel()->ReachSpecs.Num(); i++ )
				{
					FReachSpec& ReachSpec = Viewport->Actor->GetLevel()->ReachSpecs( i );
					if( ReachSpec.Start && ReachSpec.End && !ReachSpec.bPruned )
					{
						Viewport->RenDev->Draw3DLine
						(
							Frame,
							ReachSpec.MonsterPath() ? C_GroundHighlight.Plane() : C_ActorArrow.Plane(),
							LINE_DepthCued,
							ReachSpec.Start->Location, 
							ReachSpec.End->Location
						);
					}
				}
			}

			// Draw actors.
			if( Viewport->Actor->ShowFlags & SHOW_Actors )
			{
				// Draw actor extras.
				for( INT iActor=0; iActor<Viewport->Actor->GetLevel()->Actors.Num(); iActor++ )
				{
					AActor* Actor = Viewport->Actor->GetLevel()->Actors(iActor);
					if( Actor && !Actor->bHiddenEd )
					{
#if 1
						// if far-plane (Z) clipping is enabled, consider aborting this actor
						if( EdClipZ > 0.0 && !Frame->Viewport->IsOrtho() )
						{
							FVector	Temp = Actor->Location - Frame->Coords.Origin;
							Temp     = Temp.TransformVectorBy( Frame->Coords );
							FLOAT Z  = Temp.Z; if (Abs (Z)<0.01f) Z+=0.02f;

							if( Z < 1.0f || Z > EdClipZ )
								continue;
						}
#endif



	//milestone 4 
						
#if DRAW_LABELS
						if(Actor->IsA(APawn::StaticClass()))
						{
							if(GPawnsLabel)
							{

								float X,Y;
								int iX,iY;
								 FName tagName;
								 TCHAR* tName;
								FPlane ControlPointColor;
								if(Actor->bSelected)
									ControlPointColor= FPlane(0.0,.6,0,0);
								else
									ControlPointColor= FPlane(0.9,.9,0,0);
								FVector ext=Actor->GetPrimitive()->GetCollisionExtent(Actor);

								FVector Location=Actor->Location;
								Location=Location+(ext*2);
								Render->Project(Frame,Location,X,Y,NULL);
								iX=(int)X;
								iY=(int)Y;
								tagName=Actor->Tag;
								tName=tagName.GetEntry(tagName.GetIndex())->Name;
								if(appStrcmp(tName,TEXT(""))!=0)
									Viewport->DrawString(0, Frame->Viewport->Canvas->SmallFont, iX, iY,tName, ControlPointColor);
								else
									Viewport->DrawString(0, Frame->Viewport->Canvas->SmallFont, iX, iY,Actor->GetName(), ControlPointColor);
							}
							

						}

						if(Actor->bLabel)
						{
							if(GSlabelLabel)
							{

								float X,Y;
								int iX,iY;
								 FName tagName;
								 TCHAR* tName;
								FPlane ControlPointColor;
								if(Actor->bSelected)
									ControlPointColor= FPlane(0.0,.6,0,0);
								else
									ControlPointColor= FPlane(0.9,.9,0,0);
								FVector ext=Actor->GetPrimitive()->GetCollisionExtent(Actor);

								FVector Location=Actor->Location;
								Location=Location+(ext*2);
								Render->Project(Frame,Location,X,Y,NULL);
								iX=(int)X;
								iY=(int)Y;
								tagName=Actor->Tag;
								tName=tagName.GetEntry(tagName.GetIndex())->Name;
								if(appStrcmp(tName,TEXT(""))!=0)
									Viewport->DrawString(0, Frame->Viewport->Canvas->SmallFont, iX, iY,tName, ControlPointColor);
								else
									Viewport->DrawString(0, Frame->Viewport->Canvas->SmallFont, iX, iY,Actor->GetName(), ControlPointColor);
							}
							

						}

						if(Actor->IsA(ATriggers::StaticClass()))
						{
							if(GTriggersLabel)
							{

								float X,Y;
								int iX,iY;
								 FName tagName;
								 TCHAR* tName;
								FPlane ControlPointColor;
								if(Actor->bSelected)
									ControlPointColor= FPlane(0.0,.6,0,0);
								else
									ControlPointColor= FPlane(0.9,.9,0,0);
								FVector ext=Actor->GetPrimitive()->GetCollisionExtent(Actor);

								FVector Location=Actor->Location;
								Location=Location+(ext*2);
								Render->Project(Frame,Location,X,Y,NULL);
								iX=(int)X;
								iY=(int)Y;
								tagName=Actor->Tag;
								tName=tagName.GetEntry(tagName.GetIndex())->Name;
								if(appStrcmp(tName,TEXT(""))!=0)
									Viewport->DrawString(0, Frame->Viewport->Canvas->SmallFont, iX, iY,tName, ControlPointColor);
								else
									Viewport->DrawString(0, Frame->Viewport->Canvas->SmallFont, iX, iY,Actor->GetName(), ControlPointColor);
							}
							

						}


						if(Actor->IsA(ALight::StaticClass()))
						{
							if(GLightsLabel)
							{

								float X,Y;
								int iX,iY;
								 FName tagName;
								 TCHAR* tName;
								FPlane ControlPointColor;
								if(Actor->bSelected)
									ControlPointColor= FPlane(0.0,.6,0,0);
								else
									ControlPointColor= FPlane(0.9,.9,0,0);
								FVector ext=Actor->GetPrimitive()->GetCollisionExtent(Actor);

								FVector Location=Actor->Location;
								Location=Location+(ext*2);
								Render->Project(Frame,Location,X,Y,NULL);
								iX=(int)X;
								iY=(int)Y;
								tagName=Actor->Tag;
								tName=tagName.GetEntry(tagName.GetIndex())->Name;
							//	if(wcscmp(tName,TEXT(""))!=0)
							//		Viewport->DrawString(0, Frame->Viewport->Canvas->SmallFont, iX, iY,tName, ControlPointColor);
							//	else
									Viewport->DrawString(0, Frame->Viewport->Canvas->SmallFont, iX, iY,Actor->GetName(), ControlPointColor);
							}
							

						}

						if(Actor->IsA(AMover::StaticClass()))
						{
							if(GMoversLabel)
							{

								float X,Y;
								int iX,iY;
								 FName tagName;
								 TCHAR* tName;
								FPlane ControlPointColor;
								if(Actor->bSelected)
									ControlPointColor= FPlane(0.0,.6,0,0);
								else
									ControlPointColor= FPlane(0.9,.9,0,0);
								FVector ext=Actor->GetPrimitive()->GetCollisionExtent(Actor);

								FVector Location=Actor->Location;
								Location=Location+(ext*2);
								Render->Project(Frame,Location,X,Y,NULL);
								iX=(int)X;
								iY=(int)Y;
								tagName=Actor->Tag;
								tName=tagName.GetEntry(tagName.GetIndex())->Name;
								if(appStrcmp(tName,TEXT(""))!=0)
									Viewport->DrawString(0, Frame->Viewport->Canvas->SmallFont, iX, iY,tName, ControlPointColor);
								else
									Viewport->DrawString(0, Frame->Viewport->Canvas->SmallFont, iX, iY,Actor->GetName(), ControlPointColor);
							}
							

						}
#endif



	//end milestone 4 changes


						PUSH_HIT(Frame,HActor(Actor->GetHitActor()));
						// If this actor is an event source, draw event lines connecting it to
						// all corresponding event sinks.
						if
						(	Actor->Event!=NAME_None
						&&	Viewport->IsWire() )//SHOW_Events!!
						{
							for( INT iOther=0; iOther<Viewport->Actor->GetLevel()->Actors.Num(); iOther++ )
							{
								AActor* OtherActor = Viewport->Actor->GetLevel()->Actors( iOther );
								if
								(	(OtherActor)
								&&	(OtherActor->Tag == Actor->Event) 
								&&	(GIsEditor ? !Actor->bHiddenEd : !Actor->bHidden)
								&&  (!Actor->bOnlyOwnerSee || Actor->IsOwnedBy(Viewport->Actor))
								&&	(!Actor->IsOwnedBy(Frame->Viewport->Actor) || !Actor->bOwnerNoSee || (Actor->IsOwnedBy(Frame->Viewport->Actor) && Frame->Viewport->Actor->bBehindView)) )
								{
									Viewport->RenDev->Draw3DLine( Frame, C_ActorArrow.Plane(), LINE_None, Actor->Location, OtherActor->Location );
								}
							}
						}

						// Radii.
						if( (Viewport->Actor->ShowFlags & SHOW_ActorRadii) && Actor->bSelected )
						{
							if( Actor->bCollideActors )
							{
								FCylinder Cylinder(-Actor->CollisionHeight, Actor->CollisionHeight, Actor->CollisionRadius);
								Render->DrawCylinder( Frame, C_BrushWire.Plane(), LINE_Transparent, Cylinder, FCoords(Actor->Location) );
							}

							// Show light radius.
							if( Actor->LightType!=LT_None && Actor->bSelected && GIsEditor && Actor->LightBrightness && Actor->LightRadius )
								Render->DrawCircle( Frame, C_ActorArrow.Plane(), LINE_None, Actor->Location, Actor->WorldLightRadius() );

							// Show light radius.
							if( Actor->LightType!=LT_None && Actor->bSelected && GIsEditor && Actor->VolumeBrightness && Actor->VolumeRadius )
								Render->DrawCircle( Frame, C_Mover.Plane(), LINE_None, Actor->Location, Actor->WorldVolumetricRadius() );

							// Show sound radius.
							if( Actor->AmbientSound && Actor->bSelected && GIsEditor )
								Render->DrawCircle( Frame, C_GroundHighlight.Plane(), LINE_None, Actor->Location, Actor->WorldSoundRadius() );
						}

						// Direction arrow.
						if
						(	Viewport->IsOrtho()
						&&	Actor->bDirectional
						&&	(Cast<ACamera>(Actor) || Actor->bSelected) )
						{
							PUSH_HIT(Frame,HActor(Actor->GetHitActor()));
							FVector V = Actor->Location, A(0,0,0), B(0,0,0);
							FCoords C = GMath.UnitCoords / Actor->Rotation;
							Viewport->RenDev->Draw3DLine( Frame, C_ActorArrow.Plane(), LINE_None, V + C.XAxis * 48, V );
							Viewport->RenDev->Draw3DLine( Frame, C_ActorArrow.Plane(), LINE_None, V + C.XAxis * 48, V + C.XAxis * 16 + C.YAxis * 16 );
							Viewport->RenDev->Draw3DLine( Frame, C_ActorArrow.Plane(), LINE_None, V + C.XAxis * 48, V + C.XAxis * 16 - C.YAxis * 16 );
							Viewport->RenDev->Draw3DLine( Frame, C_ActorArrow.Plane(), LINE_None, V + C.XAxis * 48, V + C.XAxis * 16 + C.ZAxis * 16 );
							Viewport->RenDev->Draw3DLine( Frame, C_ActorArrow.Plane(), LINE_None, V + C.XAxis * 48, V + C.XAxis * 16 - C.ZAxis * 16 );
							POP_HIT(Frame);
						}

						if( Viewport->IsOrtho() && Cast<AClipMarker>(Actor) )
							Render->DrawCircle( Frame, C_BrushWire.Plane(), LINE_None, Actor->Location, 8, 1 );

						POP_HIT(Frame);

						// Draw him.
						if( Viewport->IsWire() )
							Render->DrawActor( Frame, Actor );
						// Draw actors that are outside of the level.
						//else if( Frame->ZoneNumber == 0 && Actor->Region.ZoneNumber == 0 && !Actor->IsBrush() && !Viewport->IsOrtho() 
						//	&& Level->Model->FastLineCheck(Actor->Location, Frame->Coords.Origin) )
						//	Render->DrawActor( Frame, Actor );
					}
				}
			}

			// Show pivot.
#if 1 //U2Ed -- always show pivot, regardless of SHOW_Actor setting
			if( GPivotShown )
#else
			if( (Viewport->Actor->ShowFlags & SHOW_Actors) && GPivotShown )
#endif
			{
				FLOAT X, Y;
				FVector Location = GSnappedLocation;
				if( Render->Project( Frame, Location, X, Y, NULL ) )
				{
					PUSH_HIT(Frame,HGlobalPivot(Location));
         			Viewport->RenDev->Draw2DPoint( Frame, C_BrushWire.Plane(), LINE_None, X-1, Y-1, X+1, Y+1, Location.Z );
        			Viewport->RenDev->Draw2DPoint( Frame, C_BrushWire.Plane(), LINE_None, X,   Y-4, X,   Y+4, Location.Z );
         			Viewport->RenDev->Draw2DPoint( Frame, C_BrushWire.Plane(), LINE_None, X-4, Y,   X+4, Y,   Location.Z );
					POP_HIT(Frame);
				}
			}

			edDrawAxisIndicator(Frame);

			// warren-ui
			/*
			// Draw buttons.
			if( !(Viewport->Actor->ShowFlags & SHOW_NoButtons) && !Viewport->IsFullscreen() )
				DrawButtons( Frame );
			*/
			// warren-ui

#if 1 //Interpolation Paths (from Ion Storm, Austin) added by Legend on 4/12/2000
			//
			// DEUS_EX CNN - Draw the spline curves for InterpolationPoints
			//
			
			// Draw the paths with tags that match those of selected points
			InterpolationPathCheckList.Empty( 16 );
			FName matchTag = NAME_None;
			for( INT iActor=0; iActor<Viewport->Actor->GetLevel()->Actors.Num() ; iActor++ )
			{
				AActor* Actor = Viewport->Actor->GetLevel()->Actors(iActor);
				if( Actor && Actor->IsA(AInterpolationPoint::StaticClass()) && (Actor->bSelected || (GEditor->Mode == EM_Matinee && matAlwaysShowPath)) )
				{
					matchTag = Actor->Tag;
					DrawInterpolationPath( Frame, matchTag );		// DPL KW; Sept 2, 2002
				}
			}

			//
			// DEUS_EX CNN - end changes
			//
			
#endif
#if 1 //U2Ed
			// If the user is doing a box selection in this viewport, draw the current selection box.
			//
			if( Viewport->IsOrtho() && GbIsBoxSel )
				Render->DrawBox( Frame, C_BrushWire.Plane(), LINE_None, GBoxSelStart, GBoxSelEnd );

			// If the user is brush clipping, draw lines to show them what's going on.
			//
			TArray<AActor*> ClipMarkers;

			// Gather a list of all the ClipMarkers in the level.
			//
			for( INT i = 0 ; i < GEditor->Level->Actors.Num() ; i++ )
			{
				AActor* pActor = GEditor->Level->Actors(i);
				if( pActor && pActor->IsA(AClipMarker::StaticClass()) )
					ClipMarkers.AddItem( pActor );
			}

			if( ClipMarkers.Num() > 1 )
			{
				// Draw a connecting line between them all.
				//
				for( int x = 1 ; x < ClipMarkers.Num() ; x++ )
					Viewport->RenDev->Draw3DLine(Frame, C_BrushWire.Plane(), LINE_None, ClipMarkers(x - 1)->Location, ClipMarkers(x)->Location);

				// Draw an arrow that shows the direction of the clipping plane.  This arrow should
				// appear halfway between the first and second markers.
				//
				FVector vtx1, vtx2, vtx3;
				FPoly NormalPoly;
				UBOOL bDrawOK = 1;

				vtx1 = ClipMarkers(0)->Location;
				vtx2 = ClipMarkers(1)->Location;

				if( ClipMarkers.Num() == 3 )
				{
					// If we have 3 points, just grab the third one to complete the plane.
					//
					vtx3 = ClipMarkers(2)->Location;
				}
				else
				{
					// If we only have 2 points, we will assume the third based on the viewport.
					// (With only 2 points, we can only render into the ortho viewports)
					//
					vtx3 = vtx1;
					if( Viewport->IsOrtho() )
					{
						switch( Viewport->Actor->RendMap )
						{
							case REN_OrthXY:
								vtx3.Z -= 64;
								break;

							case REN_OrthXZ:
								vtx3.Y -= 64;
								break;

							case REN_OrthYZ:
								vtx3.X -= 64;
								break;
						}
					}
					else
						bDrawOK = 0;
				}

				NormalPoly.NumVertices = 3;
				NormalPoly.Vertex[0] = vtx1;
				NormalPoly.Vertex[1] = vtx2;
				NormalPoly.Vertex[2] = vtx3;

				if( bDrawOK && !NormalPoly.CalcNormal(1) )
				{
					FVector Start = vtx1 + (( vtx2 - vtx1 ) / 2);
					Viewport->RenDev->Draw3DLine( Frame, C_BrushWire.Plane(), LINE_None, Start, Start + (NormalPoly.Normal * 48 ));
				}
			}
#endif
			POP_HIT(Frame);
			break;
		}
	}

	Render->PostRender( Frame );
	Viewport->Unlock( Blit );
	Render->FinishMasterFrame();

#if UNDYING_MEM
	MemMark.Pop();
	DynMark.Pop();
	SceneMark.Pop();
#endif

	unguardf(( TEXT("(Cam=%s,Flags=%i"), Viewport->GetName(), ShowFlags ));
}

/*-----------------------------------------------------------------------------
   Viewport mouse click handling.
-----------------------------------------------------------------------------*/

//
// Handle a mouse click in the camera window.
//
void UEditorEngine::Click
(
	UViewport*	Viewport, 
	DWORD		Buttons,
	FLOAT		MouseX,
	FLOAT		MouseY
)
{
	guard(UEditorEngine::Click);

#if ADDITIONS_IMPROVEMENTS
	// Define the hitbox size.
	INT HitboxSize = 8; // maximum is HIT_SIZE (8)

	// Set hit-test location.
	Viewport->HitX = Clamp(appFloor(MouseX - HitboxSize / 2.0f), 0, Viewport->SizeX - HitboxSize);
	Viewport->HitY = Clamp(appFloor(MouseY - HitboxSize / 2.0f), 0, Viewport->SizeY - HitboxSize);

	// Set hit-test dimensions.
	Viewport->HitXL = HitboxSize;
	Viewport->HitYL = HitboxSize;
#else
	// Set hit-test location.
	Viewport->HitX  = Clamp(appFloor(MouseX)-2,0,Viewport->SizeX);
	Viewport->HitY  = Clamp(appFloor(MouseY)-2,0,Viewport->SizeY);
	Viewport->HitXL = Clamp(appFloor(MouseX)+3,0,Viewport->SizeX) - Viewport->HitX;
	Viewport->HitYL = Clamp(appFloor(MouseY)+3,0,Viewport->SizeY) - Viewport->HitY;
#endif


	// Draw with hit-testing.
	BYTE HitData[1024];
	INT HitCount=ARRAY_COUNT(HitData);
	Draw( Viewport, 0, HitData, &HitCount );

	// Update buttons.
	if( Viewport->Input->KeyDown(IK_Shift) )
		Buttons |= MOUSE_Shift;
	if( Viewport->Input->KeyDown(IK_Ctrl) )
		Buttons |= MOUSE_Ctrl;
	if( Viewport->Input->KeyDown(IK_Alt) )
		Buttons |= MOUSE_Alt;

	// Perform hit testing.
	FEditorHitObserver Observer;
	Viewport->ExecuteHits( FHitCause(&Observer,Viewport,Buttons,MouseX,MouseY), HitData, HitCount );

	unguard;
}

// A convenience function so that Viewports can set the click location manually.
void UEditorEngine::edSetClickLocation( FVector& InLocation )
{
	guard(UEditorEngine::edSetClickLocation);
	ClickLocation = InLocation;
	unguard;
}

/*-----------------------------------------------------------------------------
   Editor camera mode.
-----------------------------------------------------------------------------*/

//
// Set the editor mode.
//
void UEditorEngine::edcamSetMode( int InMode )
{
	guard(UEditorEngine::edcamSetMode);

	// Clear old mode.
	if( Mode != EM_None )
		for( INT i=0; i<Client->Viewports.Num(); i++ )
			MouseDelta( Client->Viewports(i), MOUSE_ExitMode, 0, 0 );

	// Set new mode.
	Mode = InMode;
	if( Mode != EM_None )
		for( INT i=0; i<Client->Viewports.Num(); i++ )
			MouseDelta( Client->Viewports(i), MOUSE_SetMode, 0, 0 );

	EdCallback( EDC_CamModeChange, 1 );
	EdCallback( EDC_RedrawAllViewports, 0 );

	RedrawLevel( Level );

	unguard;
}

//
// Return editor camera mode given Mode and state of keys.
// This handlers special keyboard mode overrides which should
// affect the appearance of the mouse cursor, etc.
//
int UEditorEngine::edcamMode( UViewport* Viewport )
{
	guard(UEditorEngine::edcamMode);
	check(Viewport);
	check(Viewport->Actor);
	switch( Viewport->Actor->RendMap )
	{
		case REN_TexView:    return EM_TexView;
		case REN_TexBrowser: return EM_TexBrowser;
		case REN_MeshView:   return EM_MeshView;
	}
	return Mode;
	unguard;
}

/*-----------------------------------------------------------------------------
	Selection.
-----------------------------------------------------------------------------*/

//
// Selection change.
//
void UEditorEngine::NoteSelectionChange( ULevel* Level )
{
	guard(UEditorEngine::NoteSelectionChange);

	// Notify the editor.
	EdCallback( EDC_SelChange, 0 );

	// Pick a new common pivot, or not.
	INT Count=0;
	AActor* SingleActor=NULL;
	for( INT i=0; i<Level->Actors.Num(); i++ )
	{
		if( Level->Actors(i) && Level->Actors(i)->bSelected )
		{
			SingleActor=Level->Actors(i);
			Count++;
		}
	}
	if( Count==0 ) ResetPivot();
	else if( Count==1 ) SetPivot( SingleActor->Location, 0, 0 );

	// Update properties window.
	UpdatePropertiesWindows();

#if 1 //U2Ed
	vertexedit_Refresh();
#endif

	unguard;
}

//
// Select none.
//
void UEditorEngine::SelectNone( ULevel *Level, UBOOL Notify )
{
	guard(UEditorEngine::SelectNone);

	if( Mode == EM_VertexEdit )
		VertexHitList.Empty();

	BezierControlPointList.Empty();


	// Unselect all actors.
	for( INT i=0; i<Level->Actors.Num(); i++ )
	{
		AActor* Actor = Level->Actors(i);
		if( Actor && Actor->bSelected )
		{
			// We don't do this in certain modes.  This allows the user to select
			// the brushes they want and not have them get deselected while trying to
			// work with them.
			if( Actor->IsA(ABrush::StaticClass())
					&& ( Mode == EM_BrushClip || Mode == EM_FaceDrag ) )
				continue;

			Actor->Modify();
			Actor->bSelected = 0;
		}
	}

	// Unselect all surfaces.
	for( INT i=0; i<Level->Model->Surfs.Num(); i++ )
	{
		FBspSurf& Surf = Level->Model->Surfs(i);
		if( Surf.PolyFlags & PF_Selected )
		{
			Level->Model->ModifySurf( i, 0 );
			Surf.PolyFlags &= ~PF_Selected;
		}
	}

	if( Notify )
		NoteSelectionChange( Level );
	unguard;
}

/*-----------------------------------------------------------------------------
	Ed link topic function.
-----------------------------------------------------------------------------*/

AUTOREGISTER_TOPIC(TEXT("Ed"),EdTopicHandler);
void EdTopicHandler::Get( ULevel* Level, const TCHAR* Item, FOutputDevice& Ar )
{
	guard(EdTopicHandler::Get);

	if		(!appStricmp(Item,TEXT("LASTSCROLL")))	Ar.Logf(TEXT("%i"),GLastScroll);
	else if (!appStricmp(Item,TEXT("CURTEX")))		Ar.Log(GEditor->CurrentTexture ? GEditor->CurrentTexture->GetName() : TEXT("None"));

	unguard;
}
void EdTopicHandler::Set( ULevel* Level, const TCHAR* Item, const TCHAR* Data )
{}

/*-----------------------------------------------------------------------------
	The End.
-----------------------------------------------------------------------------*/
