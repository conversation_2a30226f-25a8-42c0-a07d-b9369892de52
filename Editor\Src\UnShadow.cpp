/*=============================================================================
	UnLight.cpp: Bsp light mesh illumination builder code
	Copyright 1997-1999 Epic Games, Inc. All Rights Reserved.

	Revision history:
		* Created by <PERSON>
=============================================================================*/

#include "EditorPrivate.h"
#include "UnRender.h"

/*---------------------------------------------------------------------------------------
   Globals.
---------------------------------------------------------------------------------------*/

enum {UNITS_PER_METER = 32};  /* Number of world units per meter. */
#define UNALIGNED_LIGHT 1

// Snap V to down to Grid.
inline FLOAT SnapTo( FLOAT V, FLOAT Grid )
{
	return appFloor( V / Grid ) * Grid;
}

//
// Class used for storing all globally-accessible light generation parameters:
//
class FMeshIlluminator
{
public:
	// Variables.
	ULevel*			Level;
	INT				NumLights, PolysLit, ActivePolys, RaysTraced, Pairs, Oversample;
	typedef TArray<AActor*> FActorArray;
	TArray<FActorArray> Lights;

	// Functions.
	void ComputeLightVisibility( UViewport* Viewport, AActor* Actor );
	INT ComputeAllLightVisibility( UBOOL Selected, UBOOL bVisibleOnly );
	void LightBspSurf( AMover* Mover, INT iSurf, INT iPoly );
	void BuildSurfList( INT iNode );
	void SetupIndex( FLightMapIndex* Index, DWORD PolyFlags, FLOAT MinU, FLOAT MinV, FLOAT MaxU, FLOAT MaxV );
};

/*---------------------------------------------------------------------------------------
   Setup.
---------------------------------------------------------------------------------------*/

void FMeshIlluminator::SetupIndex( FLightMapIndex* Index, DWORD PolyFlags, FLOAT MinU, FLOAT MinV, FLOAT MaxU, FLOAT MaxV )
{
	guard(FMeshIlluminator::SetupIndex);
	
	// make sure lumosAffected polys are illuminated at the default scale
#if 0
	if( PolyFlags & PF_LumosAffected )
	{
		Index->UScale = Index->VScale =	32;		
	}
	else
	{
#endif
		Index->UScale = Index->VScale
		=	((PolyFlags&(PF_HighShadowDetail|PF_LowShadowDetail))==(PF_HighShadowDetail|PF_LowShadowDetail)) ? 128
		:	(PolyFlags&PF_HighShadowDetail) ? 16
		:	(PolyFlags&PF_LowShadowDetail) ? 64
		:	32;
#if 0
	}
#endif
	
	if( PolyFlags&PF_Gouraud )
	{
		Index->UScale *= 0.5;
		Index->VScale *= 0.5;
	}

	Index->DataOffset = 0;
	Index->Pan.Z = 0.0;
#if UNALIGNED_LIGHT
	for( ;; )
	{
		FLOAT UExtent   = MaxU - MinU;
		Index->UClamp	= Max( 2, 1 + appFloor((UExtent-0.25)/Index->UScale) );
		FLOAT UScale    = (UExtent+0.25)/(Index->UClamp-1);
		if( PolyFlags&PF_Gouraud )
			Index->UClamp += 2;
		Index->Pan.X	= MinU - 0.125;
		if( Index->UClamp<=256 )
		{
			Index->UScale = UScale;
			break;
		}
		Index->UScale *= 2;
	}
	for( ;; )
	{
		FLOAT VExtent   = MaxV - MinV; 
		Index->VClamp	= Max( 2, 1 + appFloor((VExtent-0.25)/Index->VScale) );
		FLOAT VScale    = (VExtent+0.25)/(Index->VClamp-1);
		if( PolyFlags&PF_Gouraud )
			Index->VClamp += 2;
		Index->Pan.Y	= MinV - 0.125;
		if( Index->VClamp<=256 )
		{
			Index->VScale = VScale;
			break;
		}
		Index->VScale *= 2;
	}

	if( PolyFlags&PF_Gouraud )
	{
		Index->Pan.X -= Index->UScale;
		Index->Pan.Y -= Index->VScale;
	}
#else
	MinU -= 0.05; MaxU += 0.05;
	MinV -= 0.05; MaxV += 0.05;
	for( ;; )
	{
		FLOAT UExtent   = MaxU - MinU;
		FLOAT VExtent   = MaxV - MinV; 

		Index->UClamp	= 2 + appFloor((UExtent+0.25)/Index->UScale);
		Index->VClamp	= 2 + appFloor((VExtent+0.25)/Index->VScale);

		Index->Pan.X	= MinU - 0.125;
		Index->Pan.Y	= MinV - 0.125;
		Index->Pan.Z    = 0.0;

		if( Index->UClamp<=128 && Index->VClamp<=128 )
			break;
		Index->UScale *= 2;
		Index->VScale *= 2;
	}
#endif
	unguard;
}

/*---------------------------------------------------------------------------------------
   Light visibility computation.
---------------------------------------------------------------------------------------*/

//
// Compute visibility between each light in the world and each polygon.
// Returns number of lights to be applied.
//
INT FMeshIlluminator::ComputeAllLightVisibility( UBOOL Selected, UBOOL bVisibleOnly )
{
	guard(FMeshIlluminator::ComputeAllLightVisibility);

	// Open a temporary (non-visible) camera.
	UViewport* Viewport = GEditor->Client->NewViewport( NAME_None );
	Level->SpawnViewActor( Viewport );
	Viewport->Input->Init( Viewport );
	Viewport->Actor->ShowFlags = SHOW_PlayerCtrl;
	Viewport->Actor->RendMap = REN_PlainTex;
	Viewport->OpenWindow( 0, 1, 128, 128, INDEX_NONE, INDEX_NONE );
	Viewport->Lock( FPlane(0,0,0,0), FPlane(0,0,0,0), FPlane(0,0,0,0), 0 );

	// Compute all light visibility.
	INT n=0;	
	FTime Time = appSeconds();
	for( INT i=0; i<Level->Actors.Num(); i++ )
	{
		if( (i&15)==0 )
			GWarn->StatusUpdatef( i, Level->Actors.Num(), TEXT("%s"), TEXT("Computing visibility") ); //Fix added by Legend on 4/12/2000
		AActor* Actor = Level->Actors(i);
		if( Actor )
			Actor->bLightChanged = 0;
		if( Actor && Actor->LightType!=LT_None && (Actor->bStatic||Actor->bNoDelete) )
		{
			Level->SetActorZone(Actor);
			int DoLight = (Actor->bSelected || !Selected);
			int Visible = (!Actor->bHiddenEd || !bVisibleOnly);
			if( !DoLight && Actor->Region.Zone )
				DoLight = Actor->Region.Zone->bSelected;
			if( DoLight && Visible )
			{
				// Mark this actor as undeletable, so it can't be deleted at playtime, causing a
				// dangling light pointer.
				Actor->bNoDelete = 1;
				Viewport->Actor->Location = Actor->Location;
				n++;

				// Process all surfaces hit by this light.
				TArray<INT> iSurfs;
				GEditor->Render->GetVisibleSurfs( Viewport, iSurfs );

				for( INT i=0; i<iSurfs.Num(); i++ )
				{
					check(iSurfs(i)>=0);
					check(iSurfs(i)<Level->Model->Surfs.Num());
					check(Lights.Num()==Level->Model->Surfs.Num());
					FBspSurf& Poly = Level->Model->Surfs( iSurfs(i) );
					FPlane Plane( Level->Model->Points(Poly.pBase), Level->Model->Vectors(Poly.vNormal) );
					if
					(	Poly.iLightMap!=INDEX_NONE
					&&	(Actor->bSpecialLit ? (Poly.PolyFlags&PF_SpecialLit) : !(Poly.PolyFlags&PF_SpecialLit))
					&&	Abs(Plane.PlaneDot(Actor->Location))<=Actor->WorldLightRadius() )
					{
						Lights(iSurfs(i)).AddItem( Actor );
						NumLights++;
						Pairs++;
					}
				}
			}
		}
	}
	Time = appSeconds() - Time;
	debugf( NAME_Log, TEXT("Occluded %i views in %f sec (%f msec per light)"), n*6, Time, Time*(1000.0/n/6) );

	// Delete viewport.
	Viewport->Unlock(0);
	delete Viewport;

	return n;
	unguard;
}

/*---------------------------------------------------------------------------------------
   Polygon lighting.
---------------------------------------------------------------------------------------*/

//
// Apply all lights to one poly, generating its lighting mesh and updating
// the tables:
//
void FMeshIlluminator::LightBspSurf( AMover* Mover, INT iSurf, INT iPoly )
{
	guard(FMeshIlluminator::LightBspSurf );
	FBspSurf& Surf = Level->Model->Surfs(iSurf);
	check(Surf.iLightMap!=INDEX_NONE);
	UModel* Model = Mover ? Mover->Brush : Level->Model;
	FLightMapIndex* Index = &Model->LightMap( Surf.iLightMap );

	// Get numbers.
	FVector	Base      =  Level->Model->Points (Surf.pBase);
	FVector	Normal    =  Level->Model->Vectors(Surf.vNormal);
	FVector	TextureU  =  Level->Model->Vectors(Surf.vTextureU);
	FVector	TextureV  =  Level->Model->Vectors(Surf.vTextureV);

	// Find extent of static world surface from all of the Bsp polygons that use the surface.
	FLOAT MinU = +10000000.0;
	FLOAT MinV = +10000000.0;
	FLOAT MaxU = -10000000.0;
	FLOAT MaxV = -10000000.0;
	if( Mover )
	{
		// Compute extent.
		FPoly& Poly = Mover->Brush->Polys->Element( iPoly );
		for( INT k=0; k<Poly.NumVertices; k++ )
		{
			// Find extent in untransformed brush space.
			FVector Vertex	= Poly.Vertex[k] - Poly.Base;
			FLOAT	U		= Vertex | Poly.TextureU;
			FLOAT	V		= Vertex | Poly.TextureV;
			MinU=Min(U,MinU); MaxU=Max(U,MaxU);
			MinV=Min(V,MinV); MaxV=Max(V,MaxV);
		}
		SetupIndex( Index, Poly.PolyFlags | (Mover->bDynamicLightMover ? PF_LowShadowDetail : 0), MinU, MinV, MaxU, MaxV );
		if( Mover->bDynamicLightMover )
			return;
	}
	else
	{
		guard(SetupNormalExtent);
		for( INT i=0; i<Level->Model->Nodes.Num(); i++ )
		{
			FBspNode& Node = Level->Model->Nodes(i);
			if( Node.iSurf==iSurf && Node.NumVertices>0 )
			{
				FVert *VertPool = &Level->Model->Verts(Node.iVertPool);
				for( BYTE B=0; B < Node.NumVertices; B++ )
				{
					FVector Vertex	= Level->Model->Points(VertPool[B].pVertex) - Base;
					FLOAT	U		= Vertex | TextureU;
					FLOAT	V		= Vertex | TextureV;
					MinU            = Min(U,MinU);
					MaxU            = Max(U,MaxU);
					MinV            = Min(V,MinV);
					MaxV            = Max(V,MaxV);
				}
			}
		}
		unguard;
		SetupIndex( Index, Surf.PolyFlags, MinU, MinV, MaxU, MaxV );
	}

	// Calculate coordinates.
	FVector		NewBase		 = Level->Model->Points(Surf.pBase) + Normal * 4.0;
	INT			ByteSize	 = ((Index->UClamp+7)>>3) * Index->VClamp;
	FCoords		TexCoords    = FCoords( FVector(0,0,0), TextureU, TextureV, Normal ).Inverse().Transpose();

	// Raytrace each lightsource.
	if( Lights(iSurf).Num() )
	{
		// Setup index.
		Index->DataOffset   = Model->LightBits.Num();
		Index->iLightActors = INDEX_NONE;

		// Perform raytracing.
		TArray<BYTE> DarkCornersData(ByteSize);
		appMemzero(DarkCornersData.GetData(),ByteSize);

		TArray<BYTE> Data(ByteSize);
		for( INT i=0; i<Lights(iSurf).Num(); i++ )
		{
			// Prepare.
			AActor* Actor       = Lights(iSurf)(i);
			UBOOL   DidHit      = 0;
			FLOAT	SqRadius	= Square(Actor->WorldLightRadius());
			FLOAT   UScale      = Index->UScale;
			FLOAT   VScale      = Index->VScale;
			FVector	Vertex0		= NewBase + TexCoords.XAxis*Index->Pan.X + TexCoords.YAxis*Index->Pan.Y;
#if UNALIGNED_LIGHT
			//if( Surf.PolyFlags & PF_BrightCorners )
			//{
			//	UScale  -= 0.5 / (Index->UClamp-1);
			//	VScale  -= 0.5 / (Index->VClamp-1);
			//	Vertex0 += TexCoords.XAxis*0.25 + TexCoords.YAxis*0.25;
			//}
#endif
			FVector VertexDU	= TexCoords.XAxis * UScale;
			FVector VertexDV	= TexCoords.YAxis * VScale;

			// Perform raytracing.
			guard(Raytrace);
			INT Count=0;
			FCheckResult Hit(0.0);
			BYTE NodeFlags = NF_NotVisBlocking;
			//if( Surf.PolyFlags & PF_BrightCorners )
			//	NodeFlags |= NF_BrightCorners;
			if( Surf.PolyFlags & PF_Gouraud )
				Surf.PolyFlags &= ~PF_DarkCorners;
			for( INT VCounter=0; VCounter<Index->VClamp; VCounter++ )
			{
				FVector Vertex=Vertex0;
				for( INT UCounter=0; UCounter<Index->UClamp; UCounter+=8 )
				{
					BYTE B = 0;
					BYTE D = 0;
					BYTE M = 1;
					for( INT SubU=UCounter; SubU<Index->UClamp && M; SubU++,M=M<<1 )
					{
						// todo: pEdgesToSurfs PF_Gouraud
						if( FDistSquared(Actor->Location,Vertex) < SqRadius )
						{
							if( (Surf.PolyFlags & PF_DarkCorners) != 0 || Level->Model->PointCheck( Hit, NULL, Vertex, FVector(0,0,0), NodeFlags ) )
							{
								if( Level->Model->LineCheck( Hit, NULL, Actor->Location, Vertex, FVector(0,0,0), NodeFlags ) )
								//if( (Prev=Level->SingleLineCheck( Hit, Actor, Actor->Location, Vertex, TRACE_Movers|TRACE_Level, FVector(0,0,0), NodeFlags))!=0 )
									{B |= M; DidHit=1;}
								else
									{D |= M;}
							}
						}
						RaysTraced++;
						Vertex += VertexDU;
					}
					if( B & (M>>1) )
						B |= (256-M);
					DarkCornersData(Count) = D;
					Data(Count) = B;
					Count++;
				}
				Vertex0 += VertexDV;
			}
			unguard;

			// If any hit, add data.
			guard(SaveData);
			if( DidHit )
			{
				if( Surf.PolyFlags & PF_DarkCorners )
				{
					// todo: DarkCornersData
					for( INT i=0; i<ByteSize; i++ )
					{
						//DarkCornersData(i);
					}
				}
				if( Index->iLightActors == INDEX_NONE )
					Index->iLightActors = Model->Lights.Num();
				Model->Lights.AddItem( Actor );
				for( INT i=0; i<ByteSize; i++ )
					Model->LightBits.AddItem( Data(i) );
			}
			unguard;
		}
		if( Index->iLightActors != INDEX_NONE )
			Model->Lights.AddItem( NULL );
	}
	unguard;
}

/*---------------------------------------------------------------------------------------
   Index building
---------------------------------------------------------------------------------------*/

//
// Recursively go through the Bsp nodes and build a list of active Bsp surfs,
// allocating their light map indices.
//
void FMeshIlluminator::BuildSurfList( INT iNode )
{
	guard(FMeshIlluminator::BuildSurfList);
	FBspNode& Node = Level->Model->Nodes(iNode);
	FBspSurf& Surf = Level->Model->Surfs(Node.iSurf);
	if( Node.NumVertices )
	{
		if( !(Surf.PolyFlags & PF_NoShadows) )
		{
			if( Surf.iLightMap==INDEX_NONE )
			{
				Surf.iLightMap = Level->Model->LightMap.Add();
				PolysLit++;
			}
		}
	}
	if( Node.iFront != INDEX_NONE ) BuildSurfList(Node.iFront);
	if( Node.iBack  != INDEX_NONE ) BuildSurfList(Node.iBack);
	if( Node.iPlane != INDEX_NONE ) BuildSurfList(Node.iPlane);
	unguard;
}

/*---------------------------------------------------------------------------------------
   High-level lighting routine
---------------------------------------------------------------------------------------*/

void UEditorEngine::shadowIlluminateBsp( ULevel* Level, INT Selected, UBOOL bVisibleOnly )
{
	guard(UEditorEngine::shadowIlluminateBsp);
	FMeshIlluminator Illum;

	// Init.
	Flush(0);
	Trans->Reset( TEXT("Rebuilding lighting") );
	GWarn->BeginSlowTask( TEXT("Raytracing"), 1, 0 );

	Illum.Level = Level;
	if( Illum.Level->Model->Nodes.Num() != 0 )
	{
		// Empty lighting.
		Level->Model->LightMap.Empty();
		Level->Model->LightBits.Empty();

		// Init stats.
		Illum.PolysLit			= 0;
		Illum.RaysTraced		= 0;
		Illum.ActivePolys		= 0;
		Illum.Pairs				= 0;

		// Clear all surface light mesh indices.
		guard(ClearSurfs);
		for( INT i=0; i<Level->Model->Surfs.Num(); i++ )
			Level->Model->Surfs(i).iLightMap = INDEX_NONE;
		unguard;

		// Tell all actors that we're about to raytrace the world.
		// This enables movable brushes to set their positions for raytracing.
		guard(PreRaytrace);
		for( INT i=0; i<Level->Actors.Num(); i++ )
		{
			if( (i&15)==0 )
				GWarn->StatusUpdatef( i, Illum.Level->Actors.Num(), TEXT("%s"), TEXT("Allocating meshes") );
			AMover* Actor = Cast<AMover>( Level->Actors(i) );
			if( Actor )
			{
				Actor->PreRaytrace();
				Actor->SetWorldRaytraceKey();
				UModel* Model = Actor->Brush;
				if( Model && Model->Polys )
				{
					Model->LightMap.Empty();
					for( INT j=0; j<Model->Polys->Element.Num(); j++ )
					{
						FPoly& Poly = Model->Polys->Element(j);
						if( !(Poly.PolyFlags & PF_NoShadows) )
						{
							// Add light map index.
							Poly.iBrushPoly = Model->LightMap.Add();
							FLightMapIndex* Index = &Model->LightMap(Poly.iBrushPoly);
							Index->iLightActors = INDEX_NONE;
						}
						else Poly.iBrushPoly = INDEX_NONE;
					}
				}
			}
		}
		unguard;

		// Compute light visibility and update index with it.
		Level->BrushTracker = GNewBrushTracker( Level );
		Illum.BuildSurfList( 0 );
		for( INT i=0; i<Level->Model->Surfs.Num(); i++ )
			new(Illum.Lights)TArray<AActor*>;
		Illum.NumLights = Illum.ComputeAllLightVisibility( Selected, bVisibleOnly );

		// Lock the level with collision.
		//Level->SetActorCollision( 1 );

		// Make light list.
		for( INT i=0; i<Level->Model->LightMap.Num(); i++ )
			Level->Model->LightMap(i).iLightActors=INDEX_NONE;

		// Count raytraceable surfs.
		INT n=0, c=0;
		for( INT i=0; i<Level->Model->Surfs.Num(); i++ )
			n += (Level->Model->Surfs(i).iLightMap != INDEX_NONE);

		// Raytrace each world surface.
		for( INT i=0; i<Level->Model->Surfs.Num(); i++ )
		{
			if
			(	Level->Model->Surfs(i).iLightMap!=INDEX_NONE
			&&	!Level->BrushTracker->SurfIsDynamic(i) )
			{
				GWarn->StatusUpdatef( c++, n, TEXT("%s"), TEXT("Raytracing") );
				Illum.LightBspSurf( NULL, i, 0 );
			}
		}

		// Raytrace the movers.
		guard(RaytraceMoverSurfs);
		for( INT i=0; i<Level->Actors.Num(); i++ )
		{
			AMover* Actor = Cast<AMover>( Level->Actors(i) );
			if( Actor && Actor->Brush && Actor->Brush->Polys )
			{
				Actor->SetBrushRaytraceKey();
				Level->BrushTracker->Flush( Actor );
				Actor->Brush->Lights.Empty();
				Actor->Brush->LightBits.Empty();
				for( INT j=0; j<Actor->Brush->Polys->Element.Num(); j++ )
				{
					FPoly& Poly = Actor->Brush->Polys->Element(j);
					if( Poly.iLink!=INDEX_NONE && Poly.iBrushPoly!=INDEX_NONE )
						Illum.LightBspSurf( Actor, Poly.iLink, j );
				}
				Actor->SetWorldRaytraceKey();
			}
		}
		delete Level->BrushTracker;
		Level->BrushTracker = NULL;
		unguard;

		// Tell all actors that we're done raytracing the world.
		guard(PostRaytrace);
		for( INT i=0; i<Level->Actors.Num(); i++ )
		{
			AActor* Actor = Illum.Level->Actors(i);
			if( Actor )
				Actor->bDynamicLight = 0;
			if( Cast<AMover>(Actor) )
				Cast<AMover>(Actor)->PostRaytrace();
		}
		unguard;
		debugf( NAME_Log, TEXT("%i Lights, %i Polys, %i Pairs, %i Rays"), Illum.NumLights, Illum.PolysLit, Illum.Pairs, Illum.RaysTraced );

		// Finish up.
		//Level->SetActorCollision( 0 );
	}

	GWarn->EndSlowTask();
	Flush(0);

	unguard;
}

/*---------------------------------------------------------------------------------------
   Light link topic handler
---------------------------------------------------------------------------------------*/

AUTOREGISTER_TOPIC(TEXT("Light"),LightTopicHandler);
void LightTopicHandler::Get( ULevel* Level, const TCHAR* Item, FOutputDevice& Ar )
{
	guard(LightTopicHandler::Get);
	int Meshes=0,MeshPts=0,Size=0,MaxSize=0,CacheSize=0,Meters=0,LightCount=0,SelCount=0;

	for( int i=0; i<Level->Actors.Num(); i++ )
	{
		AActor *Actor = Level->Actors(i);
		if( Actor && Actor->LightBrightness )
		{
			LightCount++;
			if( Actor->bSelected )
				SelCount++;
		}
	}

	if( !Level || !Level->Model->LightMap.Num() )
	{
		Meshes = 0;
	}
	else
	{
		for( INT i=0; i<Level->Model->LightMap.Num(); i++ )
		{
			Size       = (int)Level->Model->LightMap(i).UClamp * (int)Level->Model->LightMap(i).VClamp;
			MeshPts   += Size;
	  		CacheSize += Size * Level->Model->LightMap(i).UScale * Level->Model->LightMap(i).UScale;
			if( Size > MaxSize ) MaxSize = Size;
		}
		Meters = CacheSize / (UNITS_PER_METER * UNITS_PER_METER);
	}
    if      (appStricmp(Item,TEXT("Meshes"))==0) 	Ar.Logf(TEXT("%i"),Meshes);
    else if (appStricmp(Item,TEXT("MeshPts"))==0) 	Ar.Logf(TEXT("%i"),MeshPts);
    else if (appStricmp(Item,TEXT("MaxSize"))==0) 	Ar.Logf(TEXT("%i"),MaxSize);
    else if (appStricmp(Item,TEXT("Meters"))==0) 	Ar.Logf(TEXT("%i"),Meters);
    else if (appStricmp(Item,TEXT("Count"))==0) 	Ar.Logf(TEXT("%i (%i)"),LightCount,SelCount);
    else if (appStricmp(Item,TEXT("AvgSize"))==0) 	Ar.Logf(TEXT("%i"),MeshPts/Max(1,Meshes));
    else if (appStricmp(Item,TEXT("CacheSize"))==0)	Ar.Logf(TEXT("%iK"),CacheSize/1000);

	unguard;
}
void LightTopicHandler::Set( ULevel* Level, const TCHAR* Item, const TCHAR* Data )
{
	guard(LightTopicHandler::Set);
	unguard;
};

/*---------------------------------------------------------------------------------------
   The End
---------------------------------------------------------------------------------------*/
