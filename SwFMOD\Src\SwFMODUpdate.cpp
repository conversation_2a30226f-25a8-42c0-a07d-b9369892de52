/* ============================================================================
	SwFMODUpdate.cpp:
	Copyright 2007 Roman Switch` Dzieciol. All Rights Reserved.
===============================================================================
	This library is free software; you can redistribute it and/or
	modify it under the terms of the GNU Lesser General Public
	License as published by the Free Software Foundation; either
	version 2.1 of the License, or (at your option) any later version.
============================================================================ */


// Includes.
#include "SwFMOD.h"

/*------------------------------------------------------------------------------------
	USwFMOD
------------------------------------------------------------------------------------*/

void USwFMOD::OnLevelChanged(ULevel* NewLevel)
{
	guard(USwFMOD::OnLevelChanged);

	// Load geometry
	if (bOcclusion && !GIsEditor)
		LoadGeometry(NewLevel);

#if 0
	// Stop any sound sources from wrong level.
	FMOD_RESULT result;
	for (FSwChannelEnumerator it(System); it; ++it)
	{
		FMOD::Channel* channel = *it;
		if (!IsChannelValid(channel))
			continue;

		// Channel data
		FMOD::Sound* sample = GetChannelSample(channel);
		if (!sample)
			continue;

		FMOD::ChannelGroup* chG;
		SWF_FMOD_CALL(channel->getChannelGroup(&chG));
		if (chG == MusicChannels) // Don't stop music.
			continue;

		FSwSoundId& id = GetChannelId(channel);
		AActor* actor = id.GetActor();

		if (!actor || actor->XLevel != NewLevel)
		{
			StopSound(channel, &id);
		}
	}
#else
	// Stop sounds
	if( System )
	{
		for( FSwChannelEnumerator it(System); it; ++it )
		{
			FMOD::Channel* channel = *it;
			if( IsChannelValid(channel) )
			{
				StopSound(channel);
			}
		}
	}

	// had to clear entire ChannelUserData, otherwise we would crash when loading a save
	// this also means we need to clear all channels' UserData above ^
	appMemzero(ChannelUserData, MaxUserDataChannels * sizeof(FSwSoundId));
#endif

	unguard;
}

// called when a map is loaded, during LoadMap
void USwFMOD::CleanUp()
{
	OnLevelChanged(GEngine->GLevel);
}

void USwFMOD::Update( FPointRegion Region, FCoords& Coords )
{
	guard(USwFMOD::Update);
	FMOD_RESULT result;
	//SWF_LOG( NAME_DevSound, TEXT("%ls >> %ls :: [%ls],[%ls]"), SWF_PLOG, *ToStr(Region), *ToStr(Coords) );

	if( !Viewport || !Viewport->IsValid() || !Viewport->Actor || !Viewport->Actor->IsValid() )
		return;

	// Get viewactor
	AActor* ViewActor = GetViewActor();
	AActor* WeaponActor = (ViewActor->bIsPawn ? ((APawn*)ViewActor)->Weapon : NULL);
	ULevel* ViewLevel = ViewActor->XLevel;

	// Time passes...
	FLOAT DeltaTime = appSeconds() - LastTime;
	LastTime += DeltaTime;
	DeltaTime = Clamp( DeltaTime, 0.f, 1.f );

	// Is viewport realtime ?
	UBOOL Realtime = Viewport->IsRealtime() && !Viewport->Actor->Level->Pauser.Len();

	//
	// Update listener
	//

	// Set viewport coords
	FVector TempForward=Coords.ZAxis;
	if(!b3DCameraSounds)
		TempForward.Z=0.f;

	const FVector& location = Coords.Origin;
	const FVector& velocity = ViewActor->Velocity;
	const FVector& forward = TempForward;
	const FVector& up = -Coords.YAxis;
	BYTE bFirstPerson = !Viewport->Actor->bBehindView;


	// verify
#if 0
	SWF_VERIFY_FVECTOR(location);
	SWF_VERIFY_FVECTOR(velocity);
	SWF_VERIFY_FVECTOR_NORMAL(forward);
	SWF_VERIFY_FVECTOR_NORMAL(up);
#endif

	// Set listener coords
	FMOD_VECTOR fm_pos = ToFMODVector(location);
	FMOD_VECTOR fm_vel = ToFMODVector(velocity);
	FMOD_VECTOR fm_fwd = ToFMODNormal(forward);
	FMOD_VECTOR fm_up = ToFMODNormal(up);

	// verify
#if 0
	SWF_VERIFY_FMODVECTOR(fm_pos);
	SWF_VERIFY_FMODVECTOR(fm_vel);
	SWF_VERIFY_FMODVECTOR_NORMAL(fm_fwd);
	SWF_VERIFY_FMODVECTOR_NORMAL(fm_up);
#endif

	guard(USwFMODAudio::Update::UpdateListener);
	// Update
	SWF_FMOD_CALL( System->set3DListenerAttributes( 0, &fm_pos, &fm_vel, &fm_fwd, &fm_up ) );
	unguard;


	//
	// Zone effects
	//
	guard(UpdateZone);
#if 0

	// Default zone properties
	UBOOL bWaterZone = 0;
	UBOOL bReverbZone = 0;
	UBOOL bRaytraceReverb = 0;
	BYTE MasterGain = 100;
	INT CutoffHz = 6000;
	BYTE Delay[6] = {20,34,0,0,0,0};
	BYTE Gain[6] = {150,70,0,0,0,0};

	// Get zone properties
	if( Region.Zone && Region.Zone->IsValid() )
	{
		bWaterZone = Region.Zone->bWaterZone;
		bReverbZone = Region.Zone->bReverbZone;
		bRaytraceReverb = Region.Zone->bRaytraceReverb;
		MasterGain = Region.Zone->MasterGain;
		CutoffHz = Region.Zone->CutoffHz;
		appMemcpy(Delay, Region.Zone->Delay, 6*sizeof(BYTE));
		appMemcpy(Gain, Region.Zone->Gain, 6*sizeof(BYTE));
	}
#endif
	AZoneInfo* Z = Viewport->Actor->Region.Zone; // todo: or HeadRegion?
	if( Z && CurrentEFXAmb!=/*Z->EFXAmbients*/REVERB_PRESET_NONE )
	{
		CurrentEFXAmb = /*Z->EFXAmbients*/REVERB_PRESET_NONE;
		System->setReverbProperties(&EFXPresets[CurrentEFXAmb]);
		bIsEmulatingReverb = 0;
		OldZone = NULL;
	}
	if( Z!=OldZone )
	{
		OldZone = Z;
		if( bEmulateReverb && Z && CurrentEFXAmb==REVERB_PRESET_NONE && Z->bReverbZone )
		{
			bIsEmulatingReverb = 1;
			FLOAT ReverbVolume	= Clamp((Z->MasterGain/255.0f), 0.001f, 1.f);
			// CombFilter to EFX
			FLOAT MaxDelay=0.f;
			FLOAT MaxGain=0.f;
			FLOAT MinDelay=0.f;
			FLOAT AvgDelay=0.f;
			FLOAT T=0.f;

			//Calculate reverb time from six comb filters
			for ( BYTE i=0;i<ARRAY_COUNT(Z->Delay);i++)
			{
				FLOAT DelayTime = Clamp(Z->Delay[i]/500.0f, 0.001f, 0.340f);
				FLOAT DelayGain = Clamp(Z->Gain[i]/255.0f, 0.001f, 0.999f);
				if ((DelayTime*DelayGain)>(MaxDelay*MaxGain))
				{
					MaxDelay=DelayTime;
					MaxGain=DelayGain;
				}
				if (DelayTime<MinDelay)
					MinDelay=DelayTime;
				AvgDelay+=(DelayTime/6.0f);
				T+=DelayTime;
			}
			FLOAT RoomSize = 3.0f*(AvgDelay-MinDelay)/(MaxDelay-MinDelay);
			BYTE PresetNum = 0;
			if(RoomSize<0.4)
				PresetNum = REVERB_PRESET_DUSTYROOM;
			else if(RoomSize<0.8)
				PresetNum = REVERB_PRESET_CASTLE_SMALLROOM;
			else if(RoomSize<1.2)
				PresetNum = REVERB_PRESET_ICEPALACE_ALCOVE;
			else if(RoomSize<1.6)
				PresetNum = REVERB_PRESET_CASTLE_SHORTPASSAGE;
			else if(RoomSize<2.0)
				PresetNum = REVERB_PRESET_ICEPALACE_SHORTPASSAGE;
			else if(RoomSize<2.4)
				PresetNum = REVERB_PRESET_CASTLE_MEDIUMROOM;
			else if(RoomSize<4)
				PresetNum = REVERB_PRESET_CASTLE_LONGPASSAGE;
			else if(RoomSize<6)
				PresetNum = REVERB_PRESET_SEWERPIPE;
			else if(RoomSize<8)
				PresetNum = REVERB_PRESET_OUTDOORS_DEEPCANYON;
			else PresetNum = REVERB_PRESET_QUARRY;

			FMOD_REVERB_PROPERTIES Revb = EFXPresets[PresetNum];
			Revb.DecayTime = Clamp<FLOAT>(RoomSize*2.f,0.1f,20.f);
			Revb.DecayLFRatio = Clamp<FLOAT>(RoomSize*0.3f,0.1f,2.f);
			Revb.DecayHFRatio = Clamp<FLOAT>(RoomSize*0.15f,0.1f,2.f);
			Revb.Diffusion*=ReverbVolume;
			Revb.EnvDiffusion*=ReverbVolume;
			Revb.Density*=ReverbVolume;
			Revb.DecayTime*=ReverbVolume;
			Revb.DecayHFRatio*=ReverbVolume;
			Revb.DecayLFRatio*=ReverbVolume;
			Revb.ReflectionsDelay*=ReverbVolume;
			Revb.ModulationDepth*=ReverbVolume;
			System->setReverbProperties(&Revb);
		}
		else if( bIsEmulatingReverb )
		{
			System->setReverbProperties(&EFXPresets[REVERB_PRESET_NONE]);
			bIsEmulatingReverb = 0;
		}
	}
	unguard;

	//
	// Update sounds.
	//
	guard(USwFMODAudio::Update::UpdateSounds);
	URenderBase* Rend = Viewport->GetOuterUClient()->Engine->Render;

	// Iterate through all channels
	for( FSwChannelEnumerator it(System); it; ++it )
	{
		FMOD::Channel* channel = *it;
		if( !IsChannelValid(channel) )
			continue;

		// Channel data
		FMOD::Sound* sample = GetChannelSample(channel);
		if( !sample )
			continue;

		USound* sound = Cast<USound>(GetSampleObject(sample));
		if( !sound )
			continue;

		FSwSoundId& id = GetChannelId(channel);
		AActor* actor = id.GetActor();

		FMOD_MODE fmode;
		SWF_FMOD_CALL( channel->getMode(&fmode) );
		UBOOL bIs3D = HasFlag(fmode,FMOD_3D);

		// Sample defaults
		FLOAT deffrequency;
		FLOAT defvolume;
		FLOAT defpanning;
		INT defpriority;
		SWF_FMOD_CALL( sample->getDefaults( &deffrequency, &defvolume, &defpanning, &defpriority ) );

		// Currently set
		FLOAT currentfrequency = deffrequency;
		FLOAT currentvolume = defvolume;

		if (actor && !actor->IsPendingKill())
		{
			if (actor->XLevel != ViewLevel)
			{
				// Stop wrong level sound source.
				StopSound(channel, &id);
				continue;
			}

			// Ambient sounds
			// was: if (id.GetSlot() == SLOT_Ambient)
			if (id.GetFlags() & SOUND_AMBIENT)
			{
				if (actor->AmbientSound != sound
					|| (location - actor->Location).SizeSquared() > Square(actor->WorldSoundRadius() + AmbientHysteresis)
#if !ADDITIONS_IMPROVEMENTS // don't restart ambient sounds when pausing and unpausing
					||  !Realtime
#endif
					|| ((actor->bScryeOnly || (id.GetFlags() & SOUND_SCRYEONLY)!=0 ) && Viewport->Actor->ScryeFraction(actor) == 0.0) )
				{
					// Stop ambient sound
					//SWF_LOG( NAME_DevSound, TEXT("%ls -- %ls :: Ambient OUT [%d]"), SWF_PLOG, *ToStr(id) );
					StopSound(channel, &id);
					continue;
				}
				else
				{
					// Update ambient sound properties.
					currentvolume = (actor->SoundVolume / 255.0f) * defvolume; // other audio driers multiply volume by 2.0 here
					currentfrequency = (actor->SoundPitch / 64.0f) * deffrequency;

					if (actor->LightType != LT_None)
					{
						static FPlane Color;
						currentvolume *= actor->LightBrightness / 255.0;
						Rend->GlobalLighting((Viewport->Actor->ShowFlags & SHOW_PlayerCtrl) != 0, actor, currentvolume, Color);
					}

					//SWF_VERIFY_FLOAT(currentvolume);
					//SWF_VERIFY_FLOAT(currentfrequency);

					SWF_FMOD_CALL(channel->setVolume(currentvolume));
					SWF_FMOD_CALL(channel->setFrequency(currentfrequency));

					if (bIs3D)
					{
						// Update 3D sound properties
						FLOAT mindist = ToFMODFloat(actor->SoundRadiusInner*actor->SoundRadiusInner);
						FLOAT radius = ToFMODFloat(actor->WorldSoundRadius());
						TSwSortMinMax(mindist, radius);

						SWF_VERIFY_FLOAT(radius);
						SWF_VERIFY_FLOAT(mindist);

						SWF_FMOD_CALL(channel->set3DMinMaxDistance(mindist, radius));
					}
				}
			}

			// Update 3D sound properties
			if (bIs3D)
			{
				// account for weapons, spells, player modifiers, etc...
				// if not using b3DCameraSounds, sounds will be 2D
				// was: if (bFirstPerson && (actor == ViewActor || actor == WeaponActor))
				if (b3DCameraSounds && bFirstPerson && (actor == ViewActor || (actor->IsBasedOn(ViewActor) && actor->IsOwnedBy(ViewActor))))
				{
					SWF_FMOD_CALL(channel->set3DAttributes(&fm_pos, &fm_vel));
				}
				else
				{
					FMOD_VECTOR snd_pos = ToFMODVector(actor->Location);
					FMOD_VECTOR snd_vel = ToFMODVector(actor->Velocity);

					//SWF_VERIFY_FMODVECTOR(snd_pos);
					//SWF_VERIFY_FMODVECTOR(snd_vel);

					SWF_FMOD_CALL(channel->set3DAttributes(&snd_pos, &snd_vel));
				}
			}
		}
		// was: else if (id.GetSlot() == SLOT_Ambient) // Stop sound if too far off screen...
		else if (id.GetFlags() & SOUND_AMBIENT) // Stop sound if too far off screen...
		{
			if( bIs3D )
			{
				FLOAT radius;
				FMOD_VECTOR snd_pos;
				SWF_FMOD_CALL(channel->get3DMinMaxDistance(NULL,&radius) );
				SWF_FMOD_CALL(channel->get3DAttributes(&snd_pos,NULL));

				if( (ToUEVector(snd_pos)-Coords.Origin).SizeSquared()>Square(ToUEFloat(radius)) )
				{
					StopSound(channel, &id);
				}
			}
			else // Never keep looping 2D sounds around with unknown source.
			{
				StopSound(channel, &id);
			}
		}

		//if( (id.GetFlags() & SOUND_NOPANNING) && bIs3D )
		//{
		//	// todo:
		//}

#if 0 // original undying attenuation
		// Compute attenuation.
		FLOAT Attenuation;
		if( (id.GetFlags()&SOUND_NOFALLOFF)!=0 )
		{
			Attenuation = 1.0;
		}
		else if( !id.GetActor() || !id.GetActor()->AmbientSound || id.GetActor()->AmbientSound != id.Sound || id.GetActor()->SoundRadiusInner == 0.0 )
		{
			// Compute the spatialization.
			FVector Location = id.Location.TransformPointBy( Coords );
			FLOAT Size        = Location.Size();

			Attenuation = Clamp(1.f-Size/id.Radius,0.f,1.f);
		}
		else
		{
			FLOAT SoundRadiusInnerSquared = Square((FLOAT)id.GetActor()->SoundRadiusInner);
			FLOAT SoundRadiusSquared = Square((FLOAT)id.GetActor()->SoundRadius);
			if( (id.GetActor()->Location - ViewActor->Location).SizeSquared() > SoundRadiusInnerSquared * SoundRadiusInnerSquared )
				Attenuation = 1.0 - ((id.GetActor()->Location - ViewActor->Location).Size() - SoundRadiusInnerSquared) / (SoundRadiusSquared - SoundRadiusInnerSquared);
			else
				Attenuation = 1.0;				
		}
#endif

		// scrye and water volume/pitch
		FLOAT VolumeMult = 1.0;
		FLOAT PitchMult = 1.0;

		APlayerPawn* ViewPlayerPawn = Cast<APlayerPawn>(ViewActor);
		if( ViewPlayerPawn )
		{
			FLOAT ScryeFraction = ViewPlayerPawn->ScryeFraction(NULL);

			if( (id.GetActor() && id.GetActor()->bScryeOnly) || (id.GetFlags() & SOUND_SCRYEONLY)!=0 )
			{
				VolumeMult = ScryeFraction;
				PitchMult = ScryeFraction;
			}
			else
			{
				if( (id.GetFlags() & SOUND_NOSCRYEVOLUME)==0 )
					VolumeMult = 1.0 - ScryeFraction * 0.75;
				if( (id.GetFlags() & SOUND_NOSCRYEPITCH)==0 )
					PitchMult = 1.0 - ScryeFraction * 0.25;
			}
		}
		else
		{
			if( (id.GetActor() && id.GetActor()->bScryeOnly) || (id.GetFlags() & SOUND_SCRYEONLY)!=0 )
			{
				VolumeMult = 0.0;
				PitchMult = 0.0;
			}
		}

		if( Viewport->Actor->HeadRegion.Zone->bWaterZone )
		{
			if( (id.GetFlags() & SOUND_NOWATERVOLUME)==0 )
				VolumeMult *= 0.5;
			if( (id.GetFlags() & SOUND_NOWATERPITCH)==0 )
				PitchMult *= 0.85;
		}

		PitchMult = Clamp(PitchMult, 0.25f, 1.0f);

		if( (id.GetFlags() & SOUND_NOSCRYEVOLUME)!=0 )
			VolumeMult = Clamp(VolumeMult, 0.5f, 1.0f);
		else
			VolumeMult = Min(VolumeMult, 1.0f);

		FLOAT Attenuation = 1.0;

		// since set3DPanLevel is actually set3DLevel, we have to manually update attenuation
		// this also attenuates 2D sounds with SOUND_NOPANNING (keep for now)
		if( !bIs3D && !(id.GetFlags() & SOUND_NOFALLOFF) )
		{
			if( !actor || !actor->AmbientSound || actor->AmbientSound != id.Sound || actor->SoundRadiusInner == 0.0 )
			{
				FVector Location  = actor ? actor->Location.TransformPointBy(Coords) : id.Location.TransformPointBy(Coords);
				FLOAT Size        = Location.Size();

				Attenuation = Clamp(1.f-Size/id.Radius,0.f,1.f);
			}
			else
			{
				FLOAT SoundRadiusInnerSquared = Square((FLOAT)actor->SoundRadiusInner);
				FLOAT SoundRadiusSquared = Square((FLOAT)actor->SoundRadius);
				if( (actor->Location - ViewActor->Location).SizeSquared() > SoundRadiusInnerSquared * SoundRadiusInnerSquared )
					Attenuation = 1.0 - ((actor->Location - ViewActor->Location).Size() - SoundRadiusInnerSquared) / (SoundRadiusSquared - SoundRadiusInnerSquared);
			}
		}

		SWF_FMOD_CALL(channel->setVolume(currentvolume * VolumeMult * Attenuation));
		SWF_FMOD_CALL(channel->setFrequency(currentfrequency * PitchMult * Viewport->Actor->Level->TimeDilation)); // might as well use TimeDilation here

		// sound pausing
		bool bPauseSound = (id.GetFlags() & SOUND_NOPAUSE)==0 && Viewport->Actor->Level->Pauser!=TEXT("");
		channel->setPaused(bPauseSound);
	}
	unguard;


	//
	// Play ambient sounds
	//
	if( Realtime )
	{
		guard(USwFMODAudio::Update::PlayAmbient);
		for (INT i = 0; i < ViewLevel->Actors.Num(); ++i)
		{
			AActor* Actor = ViewLevel->Actors(i);
			if(	Actor && Actor->AmbientSound && Actor->SoundRadius && Actor->SoundVolume
			&&	(location-Actor->Location).SizeSquared() <= Square(Actor->WorldSoundRadius())
			&&	((!Actor->bScryeOnly && (Actor->SoundFlags & SOUND_SCRYEONLY)==0 ) || Viewport->Actor->ScryeFraction(Actor) > 0.0) )
			{
				INT Flags = Actor->SoundFlags | SOUND_AMBIENT;
				if( Actor->bSoundLocked )
					Flags |= SOUND_LOCKED;

				//FSwSoundId ambientid(-1, Flags, SLOT_Ambient, Actor);
				//SWF_LOG( NAME_DevSound, TEXT("%ls -- %ls :: Ambient TEST IN [%ls]"), SWF_PLOG, *ToStr(ambientid) );

				// Find this sound in currently playing ones
				FMOD::Channel* ambientchannel = NULL;
				for( FSwChannelEnumerator it(System,AmbientChannels); it; ++it )
				{
					FMOD::Channel* channel = *it;
					if( !IsChannelValid(channel) )
						continue;

					FSwSoundId& Id = GetChannelId(channel);
					if( Id.GetActor() == Actor && Id.GetFlags() & SOUND_AMBIENT ) // SOUND_BACKGROUND sounds can be here too
					{
						//SWF_LOG( NAME_DevSound, TEXT("%ls -- %ls :: Ambient FOUND IN [%ls]"), SWF_PLOG, *ToStr(GetChannelId(channel)) );
						ambientchannel = channel;
						break;
					}
				}

				// If not found play ambient
				if( !ambientchannel )
				{
					//SWF_LOG( NAME_DevSound, TEXT("%ls -- %ls :: Ambient PLAY IN [%ls][%ls]"), SWF_PLOG, *ToStr(ambientid), *ToStr(Actor->AmbientSound) );
					PlaySound( Actor, Flags, Actor->AmbientSound, Actor->Location, Actor->SoundVolume/255.0f, Actor->WorldSoundRadius(), Actor->SoundPitch/64.0f );
				}
			}
		}
		unguard;
	}


	//
	// Music
	//
	guard(UpdateMusic)

/*	REQUIREMENTS

	SongSection is updated at realtime by audio sys
	MTRAN_Fade* only fade out, not in
	music changes caused by transition only
	ttransition reset on change
	MTRAN_None = don't change
	MTRAN_Instant = instant change
	MTRAN_Segue = seamless?
	MTRAN_Fade = 1s fade
	MTRAN_FastFade = 1/3s fade
	MTRAN_SlowFade = 5s fade
*/
	// find music channel
	FMOD::Channel* musicchannel = NULL;
	for( FSwChannelEnumerator it(System,MusicChannels); it; ++it )
	{
		FMOD::Channel* channel = *it;
		if( !IsChannelValid(channel) )
			continue;

		if( !musicchannel )
		{
			musicchannel = channel;
		}
		else
		{
			// there can be only one music
//			SWF_LOG( NAME_DevSound, TEXT("%ls :: %ls :: StopMusic %ls"), SWF_PLOG, *PrintChannel(channel) );
			StopSound(channel);
		}
	}

	if( Viewport->Actor->Transition != MTRAN_None )
	{
		PlayMusic( Viewport->Actor->Song, Viewport->Actor->SongSection, static_cast<EMusicTransition>(Viewport->Actor->Transition) );
		Viewport->Actor->Transition = MTRAN_None;
	}
	else if( bSwitchMusicTrack )
	{
		if( !bNewTrackStarted )
		{
			if( (MusicFadeOutTime+=DeltaTime)>=MusicFadeOut )
			{
				bNewTrackStarted = 1;
				SWF_FMOD_CALL( musicchannel->setVolume(0.f) );
				StartMusic();
				if( !IsChannelValid(musicchannel,1) )
					bSwitchMusicTrack = 0; // No need for fade in as music stopped.
			}
			else SWF_FMOD_CALL( musicchannel->setVolume( (1.f-(MusicFadeOutTime / MusicFadeOut)) * MusicVolumeModifier) );
		}
		else if( (MusicFadeTime+=DeltaTime)>=MusicFade )
		{
			SWF_FMOD_CALL( musicchannel->setVolume(MusicVolumeModifier) );
			bSwitchMusicTrack = 0;
		}
		else SWF_FMOD_CALL( musicchannel->setVolume( (MusicFadeTime / MusicFade) * MusicVolumeModifier) );
	}

	unguard;

	// Update FMOD
	guard(UpdateFMOD);
	SWF_FMOD_CALL( System->update() );
	unguard;

	//SWF_LOG( NAME_DevSound, TEXT("%ls << %ls :: [%ls],[%ls]"), SWF_PLOG, *ToStr(Region), *ToStr(Coords) );
	unguard;
}



/* ----------------------------------------------------------------------------
	The End.
---------------------------------------------------------------------------- */
