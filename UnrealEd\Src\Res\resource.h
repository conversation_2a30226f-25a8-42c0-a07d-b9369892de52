//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by UnrealEd.rc
//
#define IDPB_OKALL                      3
#define IDPB_GO_LINE_NUM                3
#define IDPB_SKIP                       4
#define IDDIALOG_UD_ABOUTBOX            100
#define IDB_Logo                        101
#define IDMENU_MainMenu                 101
#define IDD_ImportDialog                104
#define IDD_CreateDialog                105
#define IDMENU_BackdropPopup            106
#define IDMENU_ActorPopup               107
#define IDMENU_SurfPopup                108
#define IDR_ACCELERATOR1                109
#define IDDIALOG_NewObject              110
#define IDMENU_ViewportPopup            111
#define IDBM_VIEWPORT_CFG0              116
#define IDBM_VF_TOOLBAR                 118
#define IDDIALOG_Splash                 119
#define IDDIALOG_2DShapeEditor_Extrude  120
#define IDB_BB_LOG_WND                  120
#define IDDIALOG_2DShapeEditor_Revolve  121
#define IDBM_FILENEW                    121
#define IDPB_TWEN                       123
#define IDD_TWENRATE                    124
#define IDB_BITMAP1                     125
#define IDB_BITMAP2                     126
#define IDB_BITMAP3                     127
#define IDICON_Mainframe1               128
#define IDMENU_UD_MAINFRAME             128
#define IDR_MAINFRAME                   128
#define IDB_BITMAP4                     128
#define IDB_BITMAP5                     129
#define IDMENU_UD_UDNTYPE               130
#define IDR_UDNTYPE                     130
#define IDB_BITMAP6                     130
#define IDB_BITMAP7                     131
#define IDMENU_UD_VIEW_CONTEXT          132
#define IDB_BITMAP8                     132
#define IDB_BITMAP9                     133
#define IDDIALOG_UD_GRID_SETTINGS       134
#define IDB_BITMAP10                    134
#define IDDIALOG_UD_DETAIL              135
#define IDB_BITMAP11                    135
#define IDDIALOG_UD_COORDINATES         136
#define IDB_BITMAP12                    136
#define IDDIALOG_UD_IMAGE               137
#define IDB_BITMAP13                    137
#define IDDIALOG_UD_ANGLE               138
#define IDB_BITMAP14                    138
#define IDDIALOG_UD_SCALE               139
#define IDB_BITMAP15                    139
#define IDB_BITMAP16                    140
#define IDB_BITMAP17                    141
#define IDB_BITMAP18                    142
#define IDB_BITMAP19                    143
#define IDB_BITMAP20                    144
#define IDB_BITMAP21                    145
#define IDB_BITMAP22                    146
#define IDB_BITMAP23                    147
#define IDB_BITMAP24                    148
#define IDB_BITMAP25                    149
#define IDB_BITMAP26                    150
#define IDB_BITMAP27                    151
#define IDB_BITMAP28                    152
#define IDB_BITMAP29                    153
#define IDPP_SP_ALIGNMENT               154
#define IDB_BITMAP30                    154
#define IDPP_SP_FLAGS1                  155
#define IDB_BITMAP31                    155
#define IDDIALOG_IMPORT_SOUND           156
#define IDMENU_VF_CONTEXT               156
#define IDB_BITMAP32                    156
#define IDDIALOG_IMPORT_TEXTURE         157
#define IDB_BITMAP33                    157
#define IDDIALOG_TEX_PROP               158
#define IDB_BITMAP34                    158
#define IDDIALOG_BUILD_OPTIONS          159
#define IDB_BITMAP35                    159
#define IDDIALOG_NEW_TEXTURE            160
#define IDB_BITMAP36                    160
#define IDDIALOG_ADD_SPECIAL            161
#define IDB_BITMAP37                    161
#define IDDIALOG_MAP_IMPORT             162
#define IDDIALOG_PROGRESS               162
#define IDB_BITMAP38                    162
#define IDDIALOG_NEW_CLASS              163
#define IDB_BITMAP39                    163
#define IDDIALOG_MESH_VIEWER            164
#define IDB_BITMAP40                    164
#define IDDIALOG_IMPORT_MUSIC           165
#define IDB_BITMAP41                    165
#define IDDIALOG_FINDREPLACE            166
#define IDB_BITMAP42                    166
#define IDDIALOG_IMPORT_BRUSH           167
#define IDB_BITMAP43                    167
#define IDPP_BUILD_OPTIONS              168
#define IDB_BITMAP44                    168
#define IDPP_BUILD_STATS                169
#define IDB_BITMAP45                    169
#define IDPP_SP_FLAGS2                  170
#define IDB_BITMAP46                    170
#define IDB_BITMAP47                    171
#define IDB_BITMAP48                    172
#define IDB_BITMAP49                    173
#define IDB_BITMAP50                    174
#define IDB_BITMAP51                    175
#define IDB_BITMAP52                    176
#define IDB_BITMAP53                    177
#define IDB_BITMAP54                    178
#define IDB_BITMAP55                    179
#define IDB_BITMAP56                    180
#define IDB_BITMAP57                    181
#define IDB_BITMAP58                    182
#define IDB_BITMAP59                    183
#define IDB_BITMAP60                    184
#define IDB_BITMAP61                    185
#define IDB_BITMAP62                    186
#define IDB_BITMAP63                    187
#define ID_ShowBrush                    212
#define ID_ShowBackdrop                 253
#define ID_ActorsShow                   255
#define ID_INDICATOR_VERTS              256
#define ID_INDICATOR_VERTS_FMT          257
#define ID_INDICATOR_TRIS               258
#define ID_INDICATOR_TRIS_FMT           259
#define IDB_TOOLBAR                     400
#define IDB_CodeFrame_TOOLBAR           401
#define SAVE_IDB_2DSE_TOOLBAR           402
#define IDBM_VIEWPORT_CFG1              403
#define IDBM_VIEWPORT_CFG2              404
#define IDBM_VIEWPORT_CFG3              405
#define IDB_BB_LOCK                     406
#define IDB_BB_SNAP_VTX                 407
#define IDB_BB_GRID                     408
#define IDB_BB_ROTATE_GRID              409
#define IDB_BB_ZOOMCENTER_ALL           410
#define IDB_BB_ZOOMCENTER               411
#define IDB_BB_MAXIMIZE                 412
#define IDB_Editor_Splash               413
#define IDB_BOTTOM_BAR                  413
#define IDBM_FILEOPEN                   414
#define IDBM_FILESAVE                   415
#define IDBM_EDITFIND                   416
#define IDBM_TEXTUREBROWSER             417
#define IDBM_ACTORBROWSER               418
#define IDBM_MUSICBROWSER               419
#define IDBM_SOUNDBROWSER               420
#define IDBM_MESHVIEWER                 421
#define IDBM_UNREALSCRIPT               422
#define IDBM_ACTORPROPERTIES            423
#define IDBM_SURFACEPROPERTIES          424
#define IDBM_BUILDGEOM                  425
#define IDBM_BUILDLIGHTING              426
#define IDBM_BUILDPATHS                 427
#define IDBM_BUILDOPTIONS               428
#define IDBM_PLAYMAP                    429
#define IDBM_UNDO                       430
#define IDBM_REDO                       431
#define IDBM_2DSE                       432
#define IDBM_DOWN_ARROW                 433
#define IDBM_UP_ARROW                   434
#define IDBM_CAMSPEED1                  435
#define IDBM_CAMSPEED2                  436
#define IDBM_CAMSPEED3                  437
#define IDBM_PARTICLEVIEWER				440
#define IDB_2DSE_TOOLBAR                450
#define IDC_EDIT_X                      1000
#define IDC_EDIT_IMAGE_WIDTH            1000
#define IDC_Class                       1001
#define IDC_EDIT_Y                      1001
#define IDC_EDIT_IMAGE_HEIGHT           1001
#define IDC_Name                        1002
#define IDC_EDIT_DETAIL                 1002
#define IDC_EDIT_IMAGE_POS_X            1002
#define IDC_EDIT_GRID_X                 1003
#define IDC_EDIT_IMAGE_POS_Y            1003
#define IDC_Outer                       1004
#define IDC_EDIT_GRID_Y                 1004
#define IDC_Filename                    1005
#define IDC_EDIT_QUANT_X                1005
#define IDC_COMBO_IMAGE_SIZE            1005
#define IDC_BrowseFiles                 1006
#define IDC_EDIT_QUANT_Y                1006
#define IDC_SPIN_DETAIL                 1007
#define IDC_CHECK_SNAP_TO_GRID          1008
#define IDC_CHECK_QUANTIZE              1009
#define IDC_CHECK_SHOW_GRID             1010
#define IDC_EDIT_ANGLE                  1011
#define IDC_SPIN_ANGLE                  1012
#define IDC_STATIC_ANGLE                1013
#define IDC_EDIT_SCALE_X                1014
#define IDC_EDIT_SCALE_Y                1015
#define IDC_Logo                        1024
#define IDC_TypeList                    1030
#define IDC_PropHolder                  1031
#define IDPB_ROT_90                     1035
#define IDPB_ROT_FLIP_U                 1036
#define IDPB_ROT_FLIP_V                 1037
#define IDPB_PAN_U_1                    1038
#define IDPB_ALIGN_FLOOR                1039
#define IDPB_PAN_U_4                    1040
#define IDPB_PAN_U_16                   1041
#define IDPB_PAN_U_64                   1042
#define IDPB_ROT_5                      1043
#define IDPB_ALIGN_WALLDIR              1047
#define IDPB_ALIGN_WALLPAN              1048
#define IDPB_ALIGN_UNALIGN              1049
#define IDEC_SCALE_U                    1051
#define IDEC_SCALE_V                    1052
#define IDPB_SCALE_APPLY                1053
#define IDPB_HIDE                       1054
#define IDPB_SCALE_APPLY2               1054
#define IDCK_MASKED                     1055
#define IDCK_INVISIBLE                  1056
#define IDEC_DEPTH                      1056
#define IDCK_MIPMAP                     1056
#define IDCK_TRANSPARENT                1056
#define IDCK_2SIDED                     1057
#define IDEC_SIDES                      1057
#define IDCK_ZONE_PORTAL                1057
#define IDEC_CAP_HEIGHT                 1057
#define IDCK_PORTAL                     1058
#define IDEC_TOTAL_SIDES                1058
#define IDCK_INVIS                      1058
#define IDCK_MIRROR                     1059
#define IDCK_TWO_SIDED                  1059
//#define IDCK_NOBOUNDREJECTION           1060
#define IDCK_GOURAUD					1060
#define IDCK_SPECIALLIT                 1061
#define IDCK_UNLIT                      1062
#define IDCK_HISHADOWDETAIL             1063
#define IDCK_LOWSHADOWDETAIL            1064
#define IDCK_DIRTYSHADOWS               1065
#define IDEC_NAME                       1065
#define IDCK_DARKCORNERS			    1066
#define IDEC_GROUP                      1066
#define IDCK_NOSMOOTH                   1067
#define IDEC_PACKAGE                    1067
#define IDCK_TRANSLUCENT                1068
#define IDSC_FILENAME                   1068
#define IDCK_MODULATED                  1069
#define IDCK_BORDERFILTERING            1070
#define IDCK_SPECIALPOLY                1070
#define IDPB_CLEAR                      1070
#define IDCK_SMALLWAVY                  1071
#define IDCK_FAKEBACKDROP               1072
#define IDPB_COMPILE_ALL                1072
#define IDPB_BUILD_SELECTED             1072
#define IDCK_FORCEVIEWZONE              1073
#define IDCK_GEOMETRY                   1073
#define IDCK_UPAN                       1074
#define IDCK_BSP                        1074
#define IDCK_VPAN                       1075
#define IDCK_LIGHTING                   1075
#define IDCK_NOPASS						1076
#define IDCK_PATH_DEFINE                1076
#define IDCK_ONLY_VISIBLE               1077
#define IDSC_PROPS                      1078
#define IDSC_TEXTURE                    1079
#define IDCB_CLASS                      1079
#define IDCB_WIDTH                      1080
#define IDSC_TEXTURE2                   1080
#define IDCB_HEIGHT                     1081
#define IDCB_PREFABS                    1081
#define IDRB_SOLID                      1083
#define IDRB_SEMI_SOLID                 1084
#define IDCK_NEW_MAP                    1084
#define IDCK_COMPRESS_MAP               1084
#define IDRB_NONSOLID                   1084
#define IDRB_NON_SOLID                  1085
#define IDPG_PROGRESS                   1087
#define IDSC_MSG                        1088
#define IDPB_CANCEL                     1090
#define IDCK_RELATIVE                   1091
#define IDSC_PARENT                     1092
#define IDLB_ANIMATIONS                 1093
#define IDCB_MESH                       1095
#define IDGP_CTRLS                      1097
#define IDSC_MESH                       1098
#define IDPB_FIND                       1099
#define IDPB_FIND_NEXT                  1100
#define IDPB_FIND_PREV                  1101
#define IDPB_REPLACE                    1102
#define IDPB_REPLACE_ALL                1103
#define IDEC_LINE_NUM                   1104
#define IDCK_MATCH_CASE                 1105
#define IDCB_FIND                       1106
#define IDCB_REPLACE                    1107
#define IDCK_MERGE_FACES                1108
#define IDSC_BRUSHES                    1111
#define IDSC_ZONES                      1112
#define IDSC_POLYS                      1113
#define IDSC_NODES                      1114
#define IDSC_RATIO                      1115
#define IDSC_LIGHTS                     1116
#define IDSC_MAX_DEPTH                  1117
#define IDSC_AVG_DEPTH                  1118
#define IDRB_LAME                       1120
#define IDRB_GOOD                       1121
#define IDRB_OPTIMAL                    1122
#define IDSL_BALANCE                    1123
#define IDSC_BALANCE                    1124
#define IDCK_OPT_GEOM                   1125
#define IDCK_BUILD_VIS_ZONES            1126
#define IDCK_SEL_LIGHTS_ONLY            1127
#define IDSC_OPTIMIZATION               1128
#define IDSC_BSP_1                      1129
#define IDSC_BSP_2                      1130
#define IDSL_PORTALBIAS                 1131
#define IDCK_FLAGS2_1                   1132
#define IDSC_BSP_3                      1132
#define IDCK_FLAGS2_2                   1133
#define IDSC_BSP_4                      1133
#define IDCK_FLAGS2_3                   1134
#define IDSC_PORTALBIAS                 1134
#define IDCK_FLAGS2_4                   1135
#define IDCK_FLAGS2_5                   1136
#define IDCK_FLAGS2_6                   1137
#define IDCK_FLAGS2_7                   1138
#define IDCK_FLAGS2_8                   1139
#define IDCK_FLAGS2_9                   1140
#define IDCK_FLAGS2_10                  1141
#define IDCK_FLAGS2_11                  1142
#define IDCK_FLAGS2_12                  1143
#define IDCK_FLAGS2_13                  1144
#define IDCK_FLAGS2_14                  1145
#define IDCK_FLAGS2_15                  1146
#define IDCK_FLAGS2_16                  1147
#define IDCK_FLAGS2_17                  1148
#define IDCK_FLAGS2_18                  1149
#define IDCK_FLAGS2_19                  1150
#define IDCK_FLAGS2_20                  1151
#define IDCK_FLAGS2_21                  1152
#define IDSC_FOUND						1153
#define IDCK_WHOLE_WORD                 1154
#define IDPB_CLOSE                      1155
#define IDTC_ACTORS                     1156
#define IDPB_FILTER                     1156
#define IDLB_NAMES                      1157
#define IDPB_SELECT                     1158
#define IDEC_EVENT                      1159
#define IDRB_LITERAL                    1159
#define IDEC_TAG                        1160
#define IDRB_PERCENTAGE                 1160
#define IDEC_VALUE                      1161
#define IDSC_RESOLUTION                 1164
#define IDSC_STATIC_LIGHTS              1166
#define IDSC_DYNAMIC_LIGHTS             1167
#define IDSC_MESHELS                    1168
#define IDSC_MESH_SIZE                  1169
#define IDRB_VCONFIG0                   1174
#define IDRB_VCONFIG1                   1177
#define IDC_ANIMATE1                    1177
#define IDRB_VCONFIG2                   1178
#define IDSC_TEXTURE1                   1178
#define IDRB_VCONFIG3                   1179
#define IDSC_TEX_NAME1                  1180
#define IDPB_SET1                       1181
#define IDC_TAB1                        1181
#define IDPB_SET2                       1182
#define IDC_SCROLLBAR1                  1182
#define IDSC_TEX_NAME2                  1183
#define IDPB_EXECUTE                    1184
#define IDPB_BACKWARD                   1185
#define IDPB_FORWARD                    1186
#define IDSC_VIEWPORTIP                 1187
#define IDCB_POS                        1188
#define IDCK_ALWAYS_SHOW_PATH           1189
#define IDCK_SHOW_PATH_ORIENTATION      1190
#define IDGP_OPTIONS                    1191
#define IDC_SLIDER1                     1194
#define IDC_POS                         1195
#define IDS_WARNING                     4096
#define IDS_ERROR                       4097
#define IDS_WARNING_NO_TRIANGLES        4100
#define IDS_WARNING_EXCEEDING_20_TRIANGLES 4101
#define IDS_TOOL_SELECT                 4112
#define IDS_TOOL_VERTEX_EDIT            4113
#define IDS_TOOL_SPLIT_1                4114
#define IDS_TOOL_SPLIT_2                4115
#define IDS_TOOL_MERGE_1                4116
#define IDS_TOOL_MERGE_2                4117
#define IDS_TOOL_MERGE_3                4118
#define IDS_TOOL_POLYGON                4119
#define IDS_TOOL_SHAPE                  4120
#define IDS_TOOL_SQUARE                 4121
#define IDS_TOOL_RECTANGLE              4122
#define IDS_TOOL_CIRCLE                 4123
#define IDS_TOOL_ELLIPSE                4124
#define IDS_TRIANGULATING               4128
#define IDS_EXPORT_FILTER               4144
#define IDS_IMAGE_FILTER                4145
#define SAVE_IDMENU_2DShapeEditor       15100
#define IDMENU_2DShapeEditor_Context    15102
#define IDMENU_BrowserSound             15103
#define IDMENU_BrowserActor             15104
#define IDMENU_BrowserTexture           15105
#define IDM_MainMenu                    15106
#define IDMENU_CodeFrame                15107
#define IDMENU_BrowserMusic             15108
#define IDMENU_BrowserTexture_Context   15109
#define IDMENU_VIEWPORT_FRAME           15110
#define IDMENU_BrowserActor_Context     15111
#define IDMENU_BrowserGroup             15112
#define IDMENU_BrowserMaster            15113
#define IDMENU_2DShapeEditor            15150
#define IDMENU_BrowserMesh              15151
#define IDMENU_BrowserParticle          15152
#define IDMN_2DSE_OPEN_IMAGE            16000
#define IDCB_SIMPLE_SCALE               18000
#define IDCB_PACKAGE                    18100
#define IDCB_GROUP                      18101
#define IDLB_SOUNDS                     18102
#define IDPB_PLAY                       18103
#define IDPB_STOP                       18104
#define IDCK_PKG_ALL                    18106
#define IDCK_GRP_ALL                    18107
#define IDST_FILTER                     18108
#define IDEC_FILTER                     18109
#define IDSB_SCROLLBAR                  18110
#define IDTV_TREEVIEW                   18111
#define IDPB_PREV_GRP                   18112
#define IDPB_NEXT_GRP                   18113
#define IDLB_FILES                      18114
#define IDPB_SAVE                       18115
#define IDLB_PACKAGES                   18116
#define IDMN_CLOSE                      18117
#define IDLB_MUSIC                      18118
#define IDCK_OBJECTS                    18119
#define IDLB_GROUPS                     18120
#define IDPB_NEW_GROUP                  18121
#define IDPB_DEL_GROUP                  18122
#define IDPB_ADD_TO_GROUP               18123
#define IDPB_DEL_FROM_GROUP             18124
#define IDPB_REFRESH_GROUPS             18125
#define IDGP_PLAYBACK                   18126
#define IDSL_POSITION                   18127
#define IDPB_PAN_V_1                    19000
#define ID_BrowserTexture               19001
#define ID_BrowserMaster                19002
#define IDPB_PAN_V_4                    19003
#define ID_BrowserActor                 19004
#define IDPB_PAN_V_16                   19005
#define ID_BrowserSound                 19006
#define IDPB_PAN_V_64                   19007
#define IDSC_PACKAGE                    19008
#define IDSC_BROWSER                    19009
#define IDCB_BROWSER                    19010
#define ID_BrowserParticle              19011
#define IDPS_MATINEE                    19026
#define IDPB_ROT_45                     19500
#define IDMN_AB_NEW_CLASS               19700
#define IDDIALOG_SEARCH                 19701
#define IDDIALOG_IMPORT_MAP             19702
#define IDDIALOG_SCALE_LIGHTS           19703
#define IDDIALOG_SAVE_MAP				19704
#define IDDIALOG_BRUSH_BUILDER          19750
#define IDPP_SP_STATS                   19800
#define IDDIALOG_VIEWPORT_CONFIG        19801
#define IDDIALOG_2DShapeEditor_ExtrudeToPoint 19802
#define IDDIALOG_2DShapeEditor_ExtrudeToBevel 19803
#define IDDIALOG_GROUP                  19804
#define IDDIALOG_RENAME                 19805
#define IDDIALOG_2DSE_CUSTOM_DETAIL     19806
#define IDDIALOG_TEX_REPLACE            19807
#define IDPP_MAT_GENERAL                19808
#define IDMN_MRU_SEP                    20000
#define IDMN_MRU1                       20001
#define IDMN_MRU2                       20002
#define IDMN_MRU3                       20003
#define IDMN_MRU4                       20004
#define IDMN_MRU5                       20005
#define IDMN_MRU6                       20006
#define IDMN_MRU7                       20007
#define IDMN_MRU8                       20008
#define IDE_SELECTED                    25000
#define IDE_PATH                        25001
#define IDPB_REFRESH                    28000
#define IDPB_BUILD                      28001
#define IDPB_BUILD_PATHS                28002
#define IDBM_CHECKBOX_OFF               29750
#define IDBM_CHECKBOX_ON                29751
#define IDBM_NEW_GROUP                  29752
#define IDBM_DELETE_GROUP               29753
#define IDBM_ADD_TO_GROUP               29754
#define IDBM_DELETE_FROM_GROUP          29755
#define IDBM_REFRESH_GROUPS             29756
#define IDBM_GROUPBROWSER               29757
#define IDB_BrowserSound_TOOLBAR        29758
#define IDB_BrowserMusic_TOOLBAR        29759
#define IDB_BrowserTexture_TOOLBAR      29760
#define IDB_BrowserMesh_TOOLBAR         29761
#define IDB_BrowserActor_TOOLBAR        29762
#define IDB_BrowserParticle_TOOLBAR     29763
#define IDB_BrowserGroup_TOOLBAR        29764
#define IDBM_BUILDALL                   29767
#define IDBM_FORWARD                    29768
#define IDBM_BACKWARD                   29769
#define IDBM_EXECUTE                    29770
#define IDBM_STOP                       29771
#define IDBM_TWEN                       29772
#define ID_FILE_EXPORT_SELECTION        32771
#define ID_VIEW_ZOOM_IN                 32773
#define ID_VIEW_ZOOM_OUT                32774
#define ID_VIEW_REFRESH                 32775
#define ID_VIEW_RESET                   32776
#define ID_VIEW_IMAGE_OPEN              32777
#define ID_VIEW_IMAGE_CLEAR             32778
#define ID_VIEW_IMAGE_SIZE              32779
#define ID_VIEW_GRID                    32781
#define ID_VIEW_PROPERTIES              32782
#define ID_SHAPE_TRIANGULATE            32783
#define ID_SHAPE_TOOLS_SELECT           32784
#define ID_SHAPE_TOOLS_VERTEX_EDIT      32785
#define ID_SHAPE_TOOLS_SPLIT            32786
#define ID_SHAPE_TOOLS_MERGE            32787
#define ID_SHAPE_TOOLS_POLYGON          32788
#define ID_SHAPE_TOOLS_SQUARE           32789
#define ID_SHAPE_TOOLS_RECTANGLE        32790
#define ID_SHAPE_TOOLS_CIRCLE           32791
#define ID_SHAPE_TOOLS_ELLIPSE          32792
#define ID_SEGMENT_INSERT               32793
#define ID_SEGMENT_REMOVE               32794
#define ID_SEGMENT_LINEAR               32795
#define ID_SEGMENT_BEZIER               32796
#define ID_SHAPE_SCALE                  32797
#define ID_SHAPE_ROTATE                 32798
#define ID_SHAPE_MIRROR                 32799
#define ID_SHAPE_DISPLACE               32800
#define ID_SHAPE_RECENTER               32801
#define ID_SHAPE_FLIP_ORDER             32802
#define ID_SHAPE_BRING_TO_FRONT         32803
#define ID_SHAPE_SEND_TO_BACK           32804
#define ID_EXTRAS_SHOW_WARNINGS         32805
#define ID_SEGMENT_DETAIL_CUSTOM        32806
#define ID_SEGMENT_DETAIL_0             32807
#define ID_SEGMENT_DETAIL_1             32808
#define ID_SEGMENT_DETAIL_2             32809
#define ID_SEGMENT_DETAIL_3             32810
#define ID_SEGMENT_DETAIL_4             32811
#define ID_SEGMENT_DETAIL_5             32812
#define ID_ViewTop                      32817
#define ID_SEGMENT_DETAIL_10            32817
#define ID_SEGMENT_DETAIL_15            32822
#define ID_SEGMENT_DETAIL_20            32827
#define ID_EDIT_CLONE                   32828
#define ID_Color16Bit                   32840
#define ID_Color32Bit                   32841
#define ID_ShowMovingBrushes            32846
#define ID_ActorsIcons                  32847
#define ID_ActorsRadii                  32848
#define ID_ActorsHide                   32849
#define ID_ShowPaths                    32850
#define ID_LoadPackage                  40001
#define ID_Exit                         40002
#define IDC_FileExit                    40003
#define ID_ShowCoords                   40003
#define ID_FileNew                      40004
#define ID_ShowLog                      40004
#define IDC_FileOpen                    40005
#define ID_FileOpen                     40005
#define ID_Refresh                      40005
#define IDC_FileClose                   40006
#define ID_FileClose                    40006
#define ID_SavePackage                  40006
#define IDC_FileSave                    40007
#define ID_FileSave                     40007
#define IDC_FileSaveAs                  40008
#define ID_FileSaveAs                   40008
#define IDC_FileSaveAll                 40009
#define ID_FileSaveAll                  40009
#define ID_ExportObject                 40009
#define IDC_WindowCloseAll              40010
#define ID_WindowCloseAll               40010
#define ID_ImportObject                 40010
#define IDC_WindowChild                 40011
#define ID_Filter_AllObjects            40011
#define ID_WindowCascade                40012
#define ID_Filter_AllClasses            40012
#define ID_WindowTileH                  40013
#define ID_Filter_AllActors             40013
#define ID_WindowTileV                  40014
#define ID_CreateObject                 40014
#define ID_BuildPlay                    40015
#define ID_DestroyObject                40015
#define ID_CollectGarbage               40016
#define ID_FileExport                   40017
#define ID_FILE_EXPORT                  40017
#define ID_EditCut                      40018
#define ID_EditUndo                     40019
#define ID_EditRedo                     40020
#define ID_EditCopy                     40021
#define ID_EditPaste                    40022
#define ID_EditDelete                   40023
#define ID_EditSelectAllActors          40024
#define ID_EditFind                     40025
#define ID_EditReplace                  40026
#define ID_EditDuplicate                40027
#define ID_EditSelectNone               40028
#define ID_HelpAbout                    40030
#define ID_HelpWeb                      40031
#define ID_HelpContents                 40032
#define ID_HelpSearch                   40033
#define ID_HelpSupport                  40035
#define ID_HelpWebProduct               40036
#define ID_BuildChanged                 40037
#define ID_BuildAll                     40038
#define ID_BuildRebuild                 40039
#define ID_ViewActorProp                40040
#define ID_ViewSurfaceProp              40041
#define ID_ViewLevelProp                40042
#define ID_ViewToggleStatusBar          40043
#define ID_ViewNewFree                  40044
#define ID_BrushAdd                     40046
#define ID_BrushSubtract                40047
#define ID_BrushIntersect               40048
#define ID_BrushDeintersect             40049
#define ID_BrushAddMover                40050
#define ID_BrushAddSpecial              40051
#define ID_BrushOpen                    40052
#define ID_BrushSaveAs                  40053
#define ID_BrushSave                    40054
#define ID_BrushFactorBox               40055
#define ID_BrushFactorCylinder          40056
#define ID_BrushFactorCone              40057
#define ID_BrushFactorLinearStair       40058
#define ID_BrushFactorSpiralStair       40059
#define ID_BrushFactorCurvedStair       40060
#define ID_BrushFactorSphere            40061
#define ID_BrushFactorPyramid           40062
#define ID_BrushFactorPrysm             40063
#define ID_ViewToggleUpdateAll          40064
#define ID_ToolsPrefs                   40065
#define ID_ToolsValidate                40066
#define ID_ToolsShowLinks               40067
#define ID_Tools2DEditor                40068
#define ID_ToolsLog                     40069
#define ID_HelpIndex                    40073
#define ID_FILE_IMPORT                  40074
#define ID_BackdropPopupAddClassHere    40075
#define ID_BackdropPopupAddLightHere    40076
#define ID_BackdropPopupGrid1           40078
#define ID_BackdropPopupGrid2           40079
#define ID_BackdropPopupGrid4           40080
#define ID_BackdropPopupGrid8           40081
#define ID_BackdropPopupGrid16          40082
#define ID_BackdropPopupGrid32          40083
#define ID_BackdropPopupGrid64          40084
#define ID_BackdropPopupGrid128         40085
#define ID_BackdropPopupGrid256         40086
#define ID_BackdropPopupGridDisabled    40087
#define ID_BackdropPopupGridCustom      40088
#define ID_BackdropPopupPivot           40089
#define ID_BackdropPopupPivotSnapped    40090
#define ID_BackdropPopupLevelProperties 40091
#define IDMENU_ActorPopupSetToDefault   40092
#define IDMENU_ActorPopupProperties     40093
#define IDMENU_ActorPopupSetAsDefault   40094
#define IDMENU_ActorPopupKey0           40095
#define IDMENU_ActorPopupKey1           40096
#define IDMENU_ActorPopupKey2           40097
#define IDMENU_ActorPopupKey3           40098
#define IDMENU_ActorPopupKey4           40099
#define IDMENU_ActorPopupKey5           40100
#define IDMENU_ActorPopupKey6           40101
#define IDMENU_ActorPopupKey7           40102
#define IDMENU_ActorPopupResetOrigin    40103
#define IDMENU_ActorPopupResetPivot     40104
#define IDMENU_ActorPopupResetRotation  40105
#define IDMENU_ActorPopupResetScaling   40106
#define IDMENU_ActorPopupResetAll       40107
#define IDMENU_ActorPopupMirrorX        40108
#define IDMENU_ActorPopupMirrorY        40109
#define IDMENU_ActorPopupMirrorZ        40110
#define IDMENU_ActorPopupPerm           40111
#define IDMENU_ActorPopupToFirst        40113
#define IDMENU_ActorPopupToLast         40114
#define IDMENU_ActorPopupToBrush        40115
#define IDMENU_ActorPopupFromBrush      40116
#define IDMENU_ActorPopupMakeSolid      40117
#define IDMENU_ActorPopupMakeSemisolid  40118
#define IDMENU_ActorPopupMakeNonSolid   40119
#define IDMENU_ActorPopupSelectAllClass 40120
#define IDMENU_ActorPopupSelectAll      40121
#define IDMENU_ActorPopupSelectNone     40122
#define IDMENU_ActorPopupSelectBrushesAdd 40123
#define IDMENU_ActorPopupSelectBrushesSubtract 40124
#define IDMENU_ActorPopupSubtractBrushesSemisolid 40125
#define IDMENU_ActorPopupSelectBrushesNonsolid 40126
#define IDMENU_ActorPopupDuplicate      40127
#define IDMENU_ActorPopupDelete         40128
#define IDMENU_ActorPopupEditScript     40129
#define ID_SurfProperties               40130
#define ID_SurfPopupAddClass            40131
#define ID_SurfPopupAddLight            40132
#define ID_SurfPopupAlignFloor          40133
#define ID_SurfPopupAlignOneTile        40134
#define ID_SurfPopupAlignWallDirection  40135
#define ID_SurfPopupAlignWallPanning    40136
#define ID_SurfPopupUnalign             40137
#define ID_SurfPopupReset               40138
#define ID_SurfPopupApplyTexture        40142
#define ID_SurfPopupSelectMatchingGroups 40143
#define ID_SurfPopupSelectMatchingItems 40144
#define ID_SurfPopupSelectMatchingBrush 40145
#define ID_SurfPopupSelectMatchingTexture 40146
#define ID_SurfPopupSelectAllAdjacents  40147
#define ID_SurfPopupSelectAdjacentCoplanars 40148
#define ID_SurfPopupSelectAdjacentWalls 40149
#define ID_SurfPopupSelectAdjacentFloors 40150
#define ID_SurfPopupSelectAdjacentSlants 40151
#define ID_SurfPopupSelectReverse       40152
#define ID_SurfPopupMemorize            40153
#define ID_SurfPopupRecall              40154
#define ID_SurfPopupOr                  40155
#define ID_SurfPopupAnd                 40156
#define ID_SurfPopupXor                 40157
#define ID_EditSelectAllSurfs           40159
#define ID_BuildGeometry                40160
#define ID_BuildBSP                     40161
#define ID_BuildLighting                40162
#define ID_BuildPaths                   40163
#define ID_ViewPrefs                    -25372
#define ID_ToolsLevelStats              40165
#define ID_ViewportPopupAnimate         40171
#define ID_ViewportPopupModeLit         40174
#define ID_ViewportPopupModeTextured    40175
#define ID_ViewportPopupModePolygons    40176
#define ID_ViewportPopupModeZones       40177
#define ID_ViewportPopupModeBsp         40178
#define ID_ViewportPopupModeMapOverhead 40179
#define ID_ViewportPopupModeMapXZ       40180
#define ID_ViewportPopupModeMapYZ       40181
#define ID_ViewportPopupModeMapPersp    40182
#define ID_ViewportPopupViewActiveBrush 40183
#define ID_ViewportPopupViewMovers      40184
#define ID_ViewportPopupViewPaths       40185
#define ID_ViewportPopupActorsFull      40186
#define ID_ViewportPopupActorsIcon      40187
#define ID_ViewportPopupActorsRadius    40188
#define ID_ViewportPopupActorsHide      40189
#define ID_ViewportPopupGrid1           40190
#define ID_ViewportPopupGrid2           40191
#define ID_ViewportPopupGrid4           40192
#define ID_ViewportPopupGrid8           40193
#define ID_ViewportPopupGrid16          40194
#define ID_ViewportPopupGrid32          40195
#define ID_ViewportPopupGrid64          40196
#define ID_ViewportPopupGrid128         40197
#define ID_ViewportPopupGrid256         40198
#define ID_ViewportPopupGridDisabled    40199
#define ID_ViewportPopupGridCustom      40200
#define IDMN_2DSE_FileOpen              40202
#define IDMN_2DSE_FilleSave             40203
#define IDMN_2DSE_FileSave              40203
#define IDMN_2DSE_FilleSaveAs           40204
#define IDMN_2DSE_FileSaveAs            40204
#define IDMN_2DSE_NEW                   40206
#define IDMN_2DSE_SPLIT_SIDE            40207
#define IDMN_2DSE_DELETE                40208
#define IDMN_2DSE_EXTRUDE               40209
#define IDMN_2DSE_PROCESS_SHEET         40209
#define IDMN_2DSE_PROCESS_EXTRUDE       40210
#define IDMN_2DSE_PROCESS_REVOLVE       40211
#define IDMN_GRID_1                     40213
#define IDMN_GRID_2                     40214
#define IDMN_GRID_4                     40215
#define IDMN_GRID_8                     40216
#define IDMN_GRID_16                    40217
#define IDMN_GRID_32                    40218
#define IDMN_GRID_64                    40219
#define IDMN_2DSE_DELETE_IMAGE          40220
#define IDMN_SB_FileOpen                40221
#define IDMN_SB_FilleSave               40222
#define IDMN_AB_FileSave                40222
#define IDMN_SB_FilleSaveAs             40223
#define IDMN_SB_DELETE                  40224
#define IDMN_SB_EXPORT_WAV              40225
#define IDMN_SB_IMPORT_WAV              40226
#define IDMN_SB_FileSave                40227
#define IDMN_TB_ZOOM_32                 40228
#define IDMN_TB_ZOOM_64                 40229
#define IDMN_TB_ZOOM_128                40230
#define IDMN_TB_ZOOM_256                40231
#define IDMN_TB_FileOpen                40232
#define IDMN_TB_FileSave                40233
#define IDMN_TB_EXPORT_PCX              40234
#define IDMN_TB_IMPORT_PCX              40235
#define IDMN_TB_DELETE                  40236
#define IDMN_AB_FileOpen                40237
#define IDMN_AB_DELETE                  40238
#define ID_BuildOptions                 40239
#define IDMN_TB_PROPERTIES              40240
#define IDMN_AB_DEF_PROP                40241
#define IDMN_AB_EDIT_SCRIPT             40242
#define IDMN_AB_RESET_PROP              40243
#define IDMN_AB_EXPORT                  40244
#define IDMN_AB_EXPORT_ALL              40245
#define IDMN_TB_NEW                     40246
#define ID_BRUSH_IMPORT                 40247
#define ID_BRUSH_EXPORT                 40248
#define IDMN_CF_FilleSave               40249
#define IDMN_CF_COMPILE                 40251
#define IDMN_CF_COMPILE_ALL             40252
#define IDMN_CODE_FRAME                 40253
#define IDMN_CF_EXPORT_CHANGED          40254
#define IDMN_CF_EXPORT_ALL              40255
#define ID_MeshViewer                   40256
#define IDMN_MB_EXPORT                  40257
#define IDMN_MB_FileOpen                40258
#define IDMN_MB_FileSave                40259
#define IDMN_MB_IMPORT                  40260
#define ID_BrowserMusic                 40262
#define IDMN_CF_FIND                    40263
#define IDMN_CF_FIND_NEXT               40264
#define IDMN_CF_FIND_PREV               40265
#define IDMN_2DSE_NEW_SHAPE             40267
#define ID_BrowserMesh                  40268
#define ID_SurfUnmerge                  40270
#define IDMN_2DSE_ROTATE90              40271
#define IDMN_2DSE_ROTATE45              40272
#define IDMN_2DSE_FLIP_HORIZ            40274
#define IDMN_2DSE_FLIP_VERT             40275
#define IDMENU_ActorPopupMakeCurrent    40276
#define IDMN_VIEWPORT_CLASSIC           40278
#define IDMN_EDIT_SEARCH                40280
#define IDMENU_ActorPopupMakeAdd        40281
#define IDMENU_ActorPopupMakeSubtract   40282
#define IDMN_EDIT_SCALE_LIGHTS          40283
#define IDMN_LOAD_BACK_IMAGE            40284
#define IDMN_CLEAR_BACK_IMAGE           40285
#define IDMN_BI_CENTER                  40286
#define IDMN_BI_TILE                    40287
#define IDMN_BI_STRETCH                 40288
#define IDMN_AB_SHOWPACKAGES            40289
#define IDMENU_ActorPopupMerge          40290
#define IDMENU_ActorPopupSeparate       40291
#define IDMN_VIEWPORT_CONFIG            40292
#define IDMN_VIEWPORT_FLOATING          40293
#define IDMN_VIEWPORT_FIXED             40294
#define IDMN_VIEWPORT_CLOSEALL          40295
#define IDMN_VF_REALTIME_PREVIEW        40296
#define IDMENU_ActorPopupJoinPolys      40297
#define ID_FileExit                     40300
#define ID_DropToFloor                  40301
#define IDMN_VF_MENU                    40306
#define ID_MapWire                      40307
#define ID_MapPolys                     40308
#define ID_MapPolyCuts                  40309
#define ID_MapPlainTex                  40310
#define ID_MapDynLight                  40311
#define ID_MapZones                     40312
#define ID_MapOverhead                  40313
#define ID_MapXZ                        40314
#define ID_MapYZ                        40315
#define ID_BrowserPrefabs               40316
#define ID_MapNoPass                    40317
#define ID_BrushClip                    40318
#define ID_BrushClipSplit               40319
#define ID_BrushClipFlip                40320
#define ID_BrushClipDelete              40321
#define IDMN_RD_SOFTWARE                40322
#define IDMN_RD_DIRECT3D                40323
#define IDMN_RD_VULKAN					40324
#define IDMN_2DSE_PROCESS_EXTRUDETOPOINT 40325
#define IDMN_2DSE_PROCESS_EXTRUDETOBEVEL 40326
#define ID_BrowserGroup                 40327
#define IDMN_GB_NEW_GROUP               40328
#define IDMN_GB_DELETE_GROUP            40329
#define IDMN_GB_ADD_TO_GROUP            40330
#define IDMN_GB_DELETE_FROM_GROUP       40331
#define IDMN_GB_RENAME_GROUP            40332
#define IDMN_MB_DOCK                    40333
#define IDMN_MB_DOCK_BROWSER            40333
#define IDMN_MB_DOCK_WINDOW             40334
#define IDMN_TB_RENAME                  40335
#define IDMN_FILE_NEW                   40336
#define IDMN_FILE_OPEN                  40337
#define IDMN_FILE_CLOSE                 40338
#define IDMN_FILE_SAVE                  40339
#define IDMN_FILE_EXIT                  40340
#define IDMN_SEGMENT_LINEAR             40341
#define IDMN_SEGMENT_BEZIER             40342
#define IDMN_DETAIL_1                   40343
#define IDMN_DETAIL_2                   40344
#define IDMN_DETAIL_3                   40345
#define IDMN_DETAIL_4                   40346
#define IDMN_DETAIL_5                   40347
#define IDMN_DETAIL_10                  40348
#define IDMN_DETAIL_20                  40349
#define IDMN_DETAIL_CUSTOM              40350
#define IDMN_DETAIL_15                  40351
#define IDMN_SB_PLAY                    40352
#define IDMN_SB_STOP                    40353
#define IDMN_MB_PLAY                    40354
#define IDMN_MB_STOP                    40355
#define IDMN_TB_NEXT_GRP                40357
#define IDMN_TB_PREV_GRP                40358
#define IDMN_GB_REFRESH                 40359
#define IDMN_2DSE_SCALE_UP              40360
#define IDMN_ActorPopupShowPolys        40360
#define IDMN_2DSE_SCALE_DOWN            40363
#define IDMN_GB_SELECT                  40364
#define IDMN_GB_DESELECT                40365
#define IDMN_VAR_25                     40366
#define IDMN_VAR_100                    40367
#define IDMN_VAR_200                    40368
#define IDMN_VAR_50                     40369
#define IDMN_EDIT_TEX_REPLACE           40370
#define IDMN_2DSE_ZOOM_IN               40371
#define IDMN_2DSE_ZOOM_OUT              40372
#define IDMN_2DSE_GET_IMAGE             40373
#define IDGP_GENERAL                    40374
#define ID_PurgeUnusedTex               40375
#define IDMN_2DSE_PER_PIXEL_AT_256X256  40376
#define IDMN_CENTER_VIEW                40377
#define IDMN_SAVE_VIEWPORT_AS_BMP       40378
#define ID_VIEW_LABELPAWNS              40379
#define ID_VIEW_LABELTRIGGERS           40380
#define ID_VIEW_LABELLIGHTS             40381
#define ID_VIEW_LABELMOVERS             40382
#define ID_BACKDROPPOPUP_Fav1           40389
#define ID_BACKDROPPOPUP_Fav2           40390
#define ID_BACKDROPPOPUP_Fav3           40391
#define ID_BACKDROPPOPUP_Fav4           40392
#define ID_BACKDROPPOPUP_Fav5           40393
#define ID_VIEW_LABELSPECIAL            40394
#define ID_SURFPOPUP_Fav1               40395
#define ID_SURFPOPUP_Fav2               40396
#define ID_SURFPOPUP_Fav3               40397
#define ID_SURFPOPUP_Fav4               40398
#define ID_SURFPOPUP_Fav5               40399
#define IDMN_MB_NEW                     40419
#define IDMN_MB_OPEN                    40420
#define IDMN_MB_SAVE                    40421
#define IDMN_MB_PROPS                   40422
#define IDMN_MB_DELETE                  40423
#define IDMN_MB_NEXT_FRAME              40424
#define IDMN_MB_PREV_FRAME              40425
#define IDGP_FLAGS1                     65535
#define IDMN_2DSEC_SET_ORIGIN           65535

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        188
#define _APS_NEXT_COMMAND_VALUE         40400
#define _APS_NEXT_CONTROL_VALUE         1196
#define _APS_NEXT_SYMED_VALUE           102
#endif
#endif
