// Microsoft Visual C++ generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "afxres.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// English (United States) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US
#pragma code_page(1252)

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE 
BEGIN
    "#include ""afxres.h""\r\n"
    "\0"
END

3 TEXTINCLUDE 
BEGIN
    "\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
IDICON_Mainframe1       ICON                    "..\\res\\UndEd2.ico"


/////////////////////////////////////////////////////////////////////////////
//
// Menu
//

IDMENU_MainMenu MENU
BEGIN
    POPUP "&File"
    BEGIN
        MENUITEM "&New...",                     ID_FileNew
        MENUITEM "&Open...",                    ID_FileOpen
        MENUITEM SEPARATOR
        MENUITEM "&Save",                       ID_FileSave
        MENUITEM "Save &As...",                 ID_FileSaveAs
        MENUITEM SEPARATOR
        MENUITEM "&Import...",                  ID_FILE_IMPORT
        MENUITEM "&Export...",                  ID_FILE_EXPORT
        MENUITEM SEPARATOR
    END
    POPUP "&Edit"
    BEGIN
        MENUITEM "&Undo",                       ID_EditUndo
        MENUITEM "&Redo",                       ID_EditRedo
        MENUITEM SEPARATOR
        MENUITEM "&Search for Actors...",       IDMN_EDIT_SEARCH
        MENUITEM SEPARATOR
        MENUITEM "Cu&t",                        ID_EditCut
        MENUITEM "&Copy",                       ID_EditCopy
        MENUITEM "&Paste",                      ID_EditPaste
        MENUITEM SEPARATOR
        MENUITEM "D&uplicate",                  ID_EditDuplicate
        MENUITEM "&Delete",                     ID_EditDelete
        MENUITEM SEPARATOR
        MENUITEM "Select &None",                ID_EditSelectNone
        MENUITEM "Select A&ll Actors",          ID_EditSelectAllActors
        MENUITEM "Select A&ll Surfaces",        ID_EditSelectAllSurfs
        POPUP "&Select Surfaces"
        BEGIN
            MENUITEM "Matching &Groups",            ID_SurfPopupSelectMatchingGroups
            MENUITEM "Matching &Items",             ID_SurfPopupSelectMatchingItems
            MENUITEM "Matching &Brush",             ID_SurfPopupSelectMatchingBrush
            MENUITEM "Matching &Texture",           ID_SurfPopupSelectMatchingTexture
            MENUITEM SEPARATOR
            MENUITEM "All Ad&jacents",              ID_SurfPopupSelectAllAdjacents
            MENUITEM "Adjacent &Coplanars",         ID_SurfPopupSelectAdjacentCoplanars
            MENUITEM "Adjacent &Walls",             ID_SurfPopupSelectAdjacentWalls
            MENUITEM "Adjacent &Floors/Ceilings",   ID_SurfPopupSelectAdjacentFloors
            MENUITEM "Adjacent &Slants",            ID_SurfPopupSelectAdjacentSlants
            MENUITEM SEPARATOR
            MENUITEM "Reverse",                     ID_SurfPopupSelectReverse
            MENUITEM SEPARATOR
            MENUITEM "&Memorize Set",               ID_SurfPopupMemorize
            MENUITEM "&Recall Memory",              ID_SurfPopupRecall
            MENUITEM "&Or With Memory",             ID_SurfPopupOr
            MENUITEM "&And With Memory",            ID_SurfPopupAnd
            MENUITEM "&Xor With Memory",            ID_SurfPopupXor
        END
    END
    POPUP "&View"
    BEGIN
        MENUITEM "&Log",                        ID_ToolsLog
        MENUITEM SEPARATOR
        MENUITEM "&Actor Class Browser...",     ID_BrowserActor
        MENUITEM "&Group Browser...",           ID_BrowserGroup
        MENUITEM "&Master Browser...",          ID_BrowserMaster
        MENUITEM "&Mesh Browser...",            ID_BrowserMesh
        MENUITEM "&Particle Browser...",        ID_BrowserParticle
        //MENUITEM "M&usic Browser...",           ID_BrowserMusic
        MENUITEM "Scri&pt Editor...",           IDMN_CODE_FRAME
        MENUITEM "&Sound Browser...",           ID_BrowserSound
        MENUITEM SEPARATOR
        MENUITEM "Actor Properties",            ID_ViewActorProp
        MENUITEM "Surface Properties",          ID_ViewSurfaceProp
        MENUITEM "Level Properties",            ID_ViewLevelProp
        MENUITEM "Advanced Options",            ID_ToolsPrefs
        MENUITEM SEPARATOR
        POPUP "&Viewports"
        BEGIN
            MENUITEM "&Floating",                   IDMN_VIEWPORT_FLOATING
            MENUITEM "Fi&xed",                      IDMN_VIEWPORT_FIXED
            MENUITEM SEPARATOR
            MENUITEM "New Viewport",                ID_ViewNewFree
            MENUITEM "Configure...",                IDMN_VIEWPORT_CONFIG
            MENUITEM "C&lose All",                  IDMN_VIEWPORT_CLOSEALL
        END
        POPUP "Background Image"
        BEGIN
            MENUITEM "&Open...",                    IDMN_LOAD_BACK_IMAGE
            MENUITEM "&Clear",                      IDMN_CLEAR_BACK_IMAGE
            MENUITEM SEPARATOR
            MENUITEM "&Center",                     IDMN_BI_CENTER
            MENUITEM "&Tile",                       IDMN_BI_TILE
            MENUITEM "&Stretch",                    IDMN_BI_STRETCH
        END
        MENUITEM "Label  Pawns",                ID_VIEW_LABELPAWNS
        MENUITEM "Label Triggers",              ID_VIEW_LABELTRIGGERS
        MENUITEM "Label Lights",                ID_VIEW_LABELLIGHTS
        MENUITEM "Label Movers",                ID_VIEW_LABELMOVERS
        MENUITEM "Label Special",               ID_VIEW_LABELSPECIAL
    END
    POPUP "&Brush"
    BEGIN
        POPUP "Brush Clip"
        BEGIN
            MENUITEM "&Clip",                       ID_BrushClip
            MENUITEM "&Split",                      ID_BrushClipSplit
            MENUITEM "&Flip Normal",                ID_BrushClipFlip
            MENUITEM "&Delete All Clip Markers",    ID_BrushClipDelete
        END
        POPUP "&Reset"
        BEGIN
            MENUITEM "&Move To Origin",             IDMENU_ActorPopupResetOrigin
            MENUITEM "Reset &Pivot",                IDMENU_ActorPopupResetPivot
            MENUITEM "Reset &Rotation",             IDMENU_ActorPopupResetRotation
            MENUITEM "Reset &Scaling",              IDMENU_ActorPopupResetScaling
            MENUITEM "&Reset All",                  IDMENU_ActorPopupResetAll
        END
        MENUITEM SEPARATOR
        MENUITEM "&Add",                        ID_BrushAdd
        MENUITEM "&Subtract",                   ID_BrushSubtract
        MENUITEM "&Intersect",                  ID_BrushIntersect
        MENUITEM "&Deintersect",                ID_BrushDeintersect
        MENUITEM SEPARATOR
        MENUITEM "&Add Mover",                  ID_BrushAddMover
        MENUITEM "&Add Special...",             ID_BrushAddSpecial
        MENUITEM SEPARATOR
        MENUITEM "&Open Brush",                 ID_BrushOpen
        MENUITEM "&Save Brush As...",           ID_BrushSaveAs
        MENUITEM SEPARATOR
        MENUITEM "&Import...",                  ID_BRUSH_IMPORT
        MENUITEM "&Export...",                  ID_BRUSH_EXPORT
    END
    POPUP "B&uild"
    BEGIN
        MENUITEM "&Play Level",                 ID_BuildPlay
        MENUITEM SEPARATOR
        MENUITEM "Rebuild &Geometry Only",      ID_BuildGeometry
        MENUITEM "Rebuild Lighting Only",       ID_BuildLighting
        MENUITEM "Rebuild AI Paths",            ID_BuildPaths
        MENUITEM "&Build All",                  ID_BuildAll
        MENUITEM SEPARATOR
        MENUITEM "Build Options...",            ID_BuildOptions
        MENUITEM "Purge &Unused Textures",      ID_PurgeUnusedTex
    END
    POPUP "&Tools"
    BEGIN
        MENUITEM "&2D Shape Editor",            ID_Tools2DEditor
        MENUITEM "&Scale Lights...",            IDMN_EDIT_SCALE_LIGHTS
        MENUITEM "&Replace Textures",           IDMN_EDIT_TEX_REPLACE
        MENUITEM SEPARATOR
        MENUITEM "&System Plug-ins",            65535, GRAYED
        POPUP "&Level Plug-ins"
        BEGIN
            MENUITEM "&Validate Level",             ID_ToolsValidate
            MENUITEM "&Show Links",                 ID_ToolsShowLinks
            MENUITEM "Level &Statistics",           ID_ToolsLevelStats
        END
        MENUITEM "&Viewport Plug-ins",          65535, GRAYED
    END
    POPUP "&Help"
    BEGIN
        MENUITEM "&Contents",                   ID_HelpContents, GRAYED
        MENUITEM "&Search",                     ID_HelpSearch, GRAYED
        MENUITEM "&Index",                      ID_HelpIndex, GRAYED
        MENUITEM SEPARATOR
        MENUITEM "&Technical Support",          ID_HelpSupport, GRAYED
        MENUITEM "UnrealEd &Web Site",          ID_HelpWebProduct, GRAYED
        MENUITEM "&Epic Games Web Site",        ID_HelpWeb, GRAYED
        MENUITEM SEPARATOR
        MENUITEM "&About UnrealEd",             ID_HelpAbout, GRAYED
    END
END

IDMENU_BackdropPopup MENU
BEGIN
    POPUP "BackdropPopup"
    BEGIN
        MENUITEM "&Add %s here",                ID_BackdropPopupAddClassHere
        MENUITEM "&Add Light here",             ID_BackdropPopupAddLightHere
        POPUP "Favorites"
        BEGIN
            MENUITEM "f1",                          ID_BACKDROPPOPUP_Fav1
            MENUITEM "f2",                          ID_BACKDROPPOPUP_Fav2
            MENUITEM "f3",                          ID_BACKDROPPOPUP_Fav3
            MENUITEM "f4",                          ID_BACKDROPPOPUP_Fav4
            MENUITEM "f5",                          ID_BACKDROPPOPUP_Fav5
        END
        MENUITEM SEPARATOR
        POPUP "&Grid"
        BEGIN
            MENUITEM "&1 unit",                     ID_BackdropPopupGrid1
            MENUITEM "&2 units",                    ID_BackdropPopupGrid2
            MENUITEM "&4 units",                    ID_BackdropPopupGrid4
            MENUITEM "&8 units",                    ID_BackdropPopupGrid8
            MENUITEM "&16 units",                   ID_BackdropPopupGrid16
            MENUITEM "&32 units",                   ID_BackdropPopupGrid32
            MENUITEM "&64 units",                   ID_BackdropPopupGrid64
            MENUITEM "128 units",                   ID_BackdropPopupGrid128
            MENUITEM "256 units",                   ID_BackdropPopupGrid256
        END
        POPUP "&Pivot"
        BEGIN
            MENUITEM "Place Pivot &Snapped Here",   ID_BackdropPopupPivotSnapped
            MENUITEM "&Place Pivot Here",           ID_BackdropPopupPivot
        END
        MENUITEM SEPARATOR
        MENUITEM "&Level Properties",           ID_BackdropPopupLevelProperties
    END
END

IDMENU_ActorPopup MENU
BEGIN
    POPUP "ActorPopup"
    BEGIN
        MENUITEM "%s &Properties (%i Selected)", IDMENU_ActorPopupProperties
        MENUITEM "Reset to Defaults",           IDMENU_ActorPopupSetToDefault
        MENUITEM "Set as %s Default",           IDMENU_ActorPopupSetAsDefault
        MENUITEM SEPARATOR
        POPUP "&Movers"
        BEGIN
            MENUITEM "&Show Polys",                 IDMN_ActorPopupShowPolys
            MENUITEM SEPARATOR
            MENUITEM "Key &0 (Base)",               IDMENU_ActorPopupKey0
            MENUITEM "Key &1",                      IDMENU_ActorPopupKey1
            MENUITEM "Key &2",                      IDMENU_ActorPopupKey2
            MENUITEM "Key &3",                      IDMENU_ActorPopupKey3
            MENUITEM "Key &4",                      IDMENU_ActorPopupKey4
            MENUITEM "Key &5",                      IDMENU_ActorPopupKey5
            MENUITEM "Key &6",                      IDMENU_ActorPopupKey6
            MENUITEM "Key &7",                      IDMENU_ActorPopupKey7
        END
        POPUP "&Reset"
        BEGIN
            MENUITEM "&Move To Origin",             IDMENU_ActorPopupResetOrigin
            MENUITEM "Reset &Pivot",                IDMENU_ActorPopupResetPivot
            MENUITEM "Reset &Rotation",             IDMENU_ActorPopupResetRotation
            MENUITEM "Reset &Scaling",              IDMENU_ActorPopupResetScaling
            MENUITEM "&Reset All",                  IDMENU_ActorPopupResetAll
        END
        POPUP "&Transform"
        BEGIN
            MENUITEM "Mirror About &X",             IDMENU_ActorPopupMirrorX
            MENUITEM "Mirror About &Y",             IDMENU_ActorPopupMirrorY
            MENUITEM "Mirror About &Z (vertical)",  IDMENU_ActorPopupMirrorZ
            MENUITEM "&Transform Permanently",      IDMENU_ActorPopupPerm
        END
        POPUP "&Order"
        BEGIN
            MENUITEM "To &First",                   IDMENU_ActorPopupToFirst
            MENUITEM "To &Last",                    IDMENU_ActorPopupToLast
        END
        POPUP "&Polygons"
        BEGIN
            MENUITEM "&To Brush",                   IDMENU_ActorPopupToBrush
            MENUITEM "&From Brush",                 IDMENU_ActorPopupFromBrush
            MENUITEM SEPARATOR
            MENUITEM "&Merge",                      IDMENU_ActorPopupMerge
            MENUITEM "&Separate",                   IDMENU_ActorPopupSeparate
            MENUITEM SEPARATOR
            MENUITEM "&Join Polygons",              IDMENU_ActorPopupJoinPolys
        END
        POPUP "&Solidity"
        BEGIN
            MENUITEM "&Solid",                      IDMENU_ActorPopupMakeSolid
            MENUITEM "S&emisolid",                  IDMENU_ActorPopupMakeSemisolid
            MENUITEM "&Nonsolid",                   IDMENU_ActorPopupMakeNonSolid
        END
        POPUP "&CSG"
        BEGIN
            MENUITEM "&Additive",                   IDMENU_ActorPopupMakeAdd
            MENUITEM "&Subtractive",                IDMENU_ActorPopupMakeSubtract
        END
        MENUITEM SEPARATOR
        MENUITEM "&Select All %s Actors",       IDMENU_ActorPopupSelectAllClass
        MENUITEM "Select &All",                 IDMENU_ActorPopupSelectAll
        MENUITEM "Select &None",                IDMENU_ActorPopupSelectNone
        POPUP "Select &Brushes"
        BEGIN
            MENUITEM "&Adds",                       IDMENU_ActorPopupSelectBrushesAdd
            MENUITEM "&Subtracts",                  IDMENU_ActorPopupSelectBrushesSubtract
            MENUITEM "S&emisolids",                 IDMENU_ActorPopupSubtractBrushesSemisolid
            MENUITEM "&Nonsolids",                  IDMENU_ActorPopupSelectBrushesNonsolid
        END
        MENUITEM SEPARATOR
        MENUITEM "D&uplicate",                  IDMENU_ActorPopupDuplicate
        MENUITEM "&Delete",                     IDMENU_ActorPopupDelete
        MENUITEM "&Edit Script",                IDMENU_ActorPopupEditScript
        MENUITEM "&Make Current",               IDMENU_ActorPopupMakeCurrent
    END
END

IDMENU_2DShapeEditor_Context MENU
BEGIN
    POPUP "Context"
    BEGIN
        MENUITEM "Set &Origin",                 IDMN_2DSEC_SET_ORIGIN
        MENUITEM SEPARATOR
        MENUITEM "&Split Side",                 IDMN_2DSE_SPLIT_SIDE
        MENUITEM "&Delete",                     IDMN_2DSE_DELETE
        MENUITEM SEPARATOR
        POPUP "&Grid"
        BEGIN
            MENUITEM "1",                           IDMN_GRID_1
            MENUITEM "2",                           IDMN_GRID_2
            MENUITEM "4",                           IDMN_GRID_4
            MENUITEM "8",                           IDMN_GRID_8
            MENUITEM "16",                          IDMN_GRID_16
            MENUITEM "32",                          IDMN_GRID_32
            MENUITEM "64",                          IDMN_GRID_64
        END
        POPUP "Se&gment"
        BEGIN
            MENUITEM "&Linear",                     IDMN_SEGMENT_LINEAR
            MENUITEM "&Bezier",                     IDMN_SEGMENT_BEZIER
            MENUITEM SEPARATOR
            POPUP "Detail Level"
            BEGIN
                MENUITEM "1",                           IDMN_DETAIL_1
                MENUITEM "2",                           IDMN_DETAIL_2
                MENUITEM "3",                           IDMN_DETAIL_3
                MENUITEM "4",                           IDMN_DETAIL_4
                MENUITEM "5",                           IDMN_DETAIL_5
                MENUITEM "10",                          IDMN_DETAIL_10
                MENUITEM "15",                          IDMN_DETAIL_15
                MENUITEM "20",                          IDMN_DETAIL_20
                MENUITEM "&Custom...",                  IDMN_DETAIL_CUSTOM
            END
        END
    END
END

IDMENU_SurfPopup MENU
BEGIN
    POPUP "SurfPopup"
    BEGIN
        MENUITEM "Surface &Properties (%i Selected)", ID_SurfProperties
        MENUITEM SEPARATOR
        MENUITEM "&Add %s Here",                ID_SurfPopupAddClass
        MENUITEM "Add &Light Here",             ID_SurfPopupAddLight
        POPUP "Favorites"
        BEGIN
            MENUITEM "f1",                          ID_SURFPOPUP_Fav1
            MENUITEM "f2",                          ID_SURFPOPUP_Fav2
            MENUITEM "f3",                          ID_SURFPOPUP_Fav3
            MENUITEM "f4",                          ID_SURFPOPUP_Fav4
            MENUITEM "f5",                          ID_SURFPOPUP_Fav5
        END
        MENUITEM SEPARATOR
        POPUP "&Align Selected"
        BEGIN
            MENUITEM "Align as &Floor/Ceiling",     ID_SurfPopupAlignFloor
            MENUITEM "Align Wall &Direction",       ID_SurfPopupAlignWallDirection
            MENUITEM "Align Wall &Panning",         ID_SurfPopupAlignWallPanning
            MENUITEM SEPARATOR
            MENUITEM "&Unalign back to default",    ID_SurfPopupUnalign
        END
        MENUITEM "&Reset",                      ID_SurfPopupReset
        MENUITEM SEPARATOR
        POPUP "&Select Surfaces"
        BEGIN
            MENUITEM "Matching &Groups",            ID_SurfPopupSelectMatchingGroups
            MENUITEM "Matching &Items",             ID_SurfPopupSelectMatchingItems
            MENUITEM "Matching &Brush",             ID_SurfPopupSelectMatchingBrush
            MENUITEM "Matching &Texture",           ID_SurfPopupSelectMatchingTexture
            MENUITEM SEPARATOR
            MENUITEM "All Ad&jacents",              ID_SurfPopupSelectAllAdjacents
            MENUITEM "Adjacent &Coplanars",         ID_SurfPopupSelectAdjacentCoplanars
            MENUITEM "Adjacent &Walls",             ID_SurfPopupSelectAdjacentWalls
            MENUITEM "Adjacent &Floors/Ceilings",   ID_SurfPopupSelectAdjacentFloors
            MENUITEM "Adjacent &Slants",            ID_SurfPopupSelectAdjacentSlants
            MENUITEM SEPARATOR
            MENUITEM "Reverse",                     ID_SurfPopupSelectReverse
            MENUITEM SEPARATOR
            MENUITEM "&Memorize Set",               ID_SurfPopupMemorize
            MENUITEM "&Recall Memory",              ID_SurfPopupRecall
            MENUITEM "&Or With Memory",             ID_SurfPopupOr
            MENUITEM "&And With Memory",            ID_SurfPopupAnd
            MENUITEM "&Xor With Memory",            ID_SurfPopupXor
        END
        MENUITEM "Select &All Surfaces",        ID_EditSelectAllSurfs
        MENUITEM "Select &None",              ID_EditSelectNone
        MENUITEM SEPARATOR
        MENUITEM "Apply &Texture",              ID_SurfPopupApplyTexture
    END
END

IDMENU_BrowserSound MENU
BEGIN
    POPUP "&File"
    BEGIN
        MENUITEM "&Open...",                    IDMN_SB_FileOpen
        MENUITEM "&Save...",                    IDMN_SB_FileSave
        MENUITEM SEPARATOR
        MENUITEM "&Import...",                  IDMN_SB_IMPORT_WAV
        MENUITEM "&Export...",                  IDMN_SB_EXPORT_WAV
        MENUITEM SEPARATOR
    END
    POPUP "&Edit"
    BEGIN
        MENUITEM "&Delete",                     IDMN_SB_DELETE
        MENUITEM SEPARATOR
        MENUITEM "&Play",                       IDMN_SB_PLAY
        MENUITEM "&Stop",                       IDMN_SB_STOP
    END
    POPUP "&View"
    BEGIN
        MENUITEM "&Docked",                     IDMN_MB_DOCK
    END
END

IDMENU_BrowserActor MENU
BEGIN
    POPUP "&File"
    BEGIN
        MENUITEM "&Open Package...",            IDMN_AB_FileOpen
        MENUITEM "&Save Selected Packages",     IDMN_AB_FileSave
        MENUITEM SEPARATOR
        MENUITEM "&Export Changed Scripts",     IDMN_AB_EXPORT
        MENUITEM "&Export All Scripts",         IDMN_AB_EXPORT_ALL
        MENUITEM SEPARATOR
    END
    POPUP "&View"
    BEGIN
        MENUITEM "&Docked",                     IDMN_MB_DOCK
        MENUITEM SEPARATOR
        MENUITEM "&Show Packages",              IDMN_AB_SHOWPACKAGES
    END
    POPUP "&Class"
    BEGIN
        MENUITEM "&New...",                     IDMN_AB_NEW_CLASS
        MENUITEM "&Edit Script...",             IDMN_AB_EDIT_SCRIPT
        MENUITEM SEPARATOR
        MENUITEM "&Default Properties...",      IDMN_AB_DEF_PROP
        MENUITEM "&Reset All Actors",           IDMN_AB_RESET_PROP
    END
END

IDMENU_BrowserTexture MENU
BEGIN
    POPUP "&File"
    BEGIN
        MENUITEM "&New...",                     IDMN_TB_NEW
        MENUITEM "&Open...",                    IDMN_TB_FileOpen
        MENUITEM "&Save",                       IDMN_TB_FileSave
        MENUITEM SEPARATOR
        MENUITEM "&Import...",                  IDMN_TB_IMPORT_PCX
        MENUITEM "&Export...",                  IDMN_TB_EXPORT_PCX
        MENUITEM SEPARATOR
    END
    POPUP "&Edit"
    BEGIN
        MENUITEM "&Properties",                 IDMN_TB_PROPERTIES
        MENUITEM "&Delete",                     IDMN_TB_DELETE
        MENUITEM "&Rename...",                  IDMN_TB_RENAME
        MENUITEM SEPARATOR
        MENUITEM "&Prev Group",                 IDMN_TB_PREV_GRP
        MENUITEM "&Next Group",                 IDMN_TB_NEXT_GRP
    END
    POPUP "&View"
    BEGIN
        MENUITEM "&Docked",                     IDMN_MB_DOCK
        MENUITEM SEPARATOR
        POPUP "Set Size"
        BEGIN
            MENUITEM "32",                          IDMN_TB_ZOOM_32
            MENUITEM "64",                          IDMN_TB_ZOOM_64
            MENUITEM "128",                         IDMN_TB_ZOOM_128
            MENUITEM "256",                         IDMN_TB_ZOOM_256
        END
        POPUP "Variable Size"
        BEGIN
            MENUITEM "200%",                        IDMN_VAR_200
            MENUITEM "100%",                        IDMN_VAR_100
            MENUITEM "50%",                         IDMN_VAR_50
            MENUITEM "25%",                         IDMN_VAR_25
        END
    END
END

IDMENU_CodeFrame MENU
BEGIN
    POPUP "&File"
    BEGIN
        MENUITEM "&Export Changed Scripts...",  IDMN_CF_EXPORT_CHANGED
        MENUITEM "&Export All Scripts...",      IDMN_CF_EXPORT_ALL
        MENUITEM SEPARATOR
        MENUITEM "&Close",                      IDMN_CLOSE
    END
    POPUP "&Tools"
    BEGIN
        MENUITEM "&Compile Changed",            IDMN_CF_COMPILE
        MENUITEM "&Compile All",                IDMN_CF_COMPILE_ALL
    END
END

IDMENU_BrowserMusic MENU
BEGIN
    POPUP "&File"
    BEGIN
        MENUITEM "&Open...",                    IDMN_MB_FileOpen
        MENUITEM "&Save...",                    IDMN_MB_FileSave
        MENUITEM SEPARATOR
        MENUITEM "&Import...",                  IDMN_MB_IMPORT
        MENUITEM "&Export...",                  IDMN_MB_EXPORT
        MENUITEM SEPARATOR
    END
    POPUP "&Edit"
    BEGIN
        MENUITEM "&Play",                       IDMN_MB_PLAY
        MENUITEM "&Stop",                       IDMN_MB_STOP
    END
    POPUP "&View"
    BEGIN
        MENUITEM "&Docked",                     IDMN_MB_DOCK
    END
END

IDMENU_BrowserTexture_Context MENU
BEGIN
    POPUP "Context"
    BEGIN
        MENUITEM "&Properties",                 IDMN_TB_PROPERTIES
        MENUITEM "&Delete",                     IDMN_TB_DELETE
        MENUITEM "&Rename...",                  IDMN_TB_RENAME
        MENUITEM SEPARATOR
        MENUITEM "&Export...",                  IDMN_TB_EXPORT_PCX
    END
END

IDMENU_2DShapeEditor MENU
BEGIN
    POPUP "&File"
    BEGIN
        MENUITEM "&New",                        IDMN_2DSE_NEW
        MENUITEM SEPARATOR
        MENUITEM "&Open...",                    IDMN_2DSE_FileOpen
        MENUITEM "&Save",                       IDMN_2DSE_FileSave
        MENUITEM "Save &As...",                 IDMN_2DSE_FileSaveAs
        MENUITEM SEPARATOR
        POPUP "&Image"
        BEGIN
            MENUITEM "&Open From Disk...",          IDMN_2DSE_OPEN_IMAGE
            MENUITEM "&Get From Current Texture",   IDMN_2DSE_GET_IMAGE
            MENUITEM SEPARATOR
            MENUITEM "&Delete",                     IDMN_2DSE_DELETE_IMAGE
        END
        MENUITEM SEPARATOR
        MENUITEM "E&xit",                       ID_FileExit
    END
    POPUP "&Edit"
    BEGIN
        MENUITEM "&Insert New Shape",           IDMN_2DSE_NEW_SHAPE
        MENUITEM "&Split Side",                 IDMN_2DSE_SPLIT_SIDE
        MENUITEM "&Delete",                     IDMN_2DSE_DELETE
        MENUITEM SEPARATOR
        MENUITEM "Scale &Up",                   IDMN_2DSE_SCALE_UP
        MENUITEM "Scale &Down",                 IDMN_2DSE_SCALE_DOWN
        MENUITEM SEPARATOR
        MENUITEM "Rotate &45",                  IDMN_2DSE_ROTATE45
        MENUITEM "Rotate &90",                  IDMN_2DSE_ROTATE90
        MENUITEM SEPARATOR
        MENUITEM "Flip &Horizontally",          IDMN_2DSE_FLIP_HORIZ
        MENUITEM "Flip &Vertically",            IDMN_2DSE_FLIP_VERT
        MENUITEM SEPARATOR
        POPUP "&Grid"
        BEGIN
            MENUITEM "1",                           IDMN_GRID_1
            MENUITEM "2",                           IDMN_GRID_2
            MENUITEM "4",                           IDMN_GRID_4
            MENUITEM "8",                           IDMN_GRID_8
            MENUITEM "16",                          IDMN_GRID_16
            MENUITEM "32",                          IDMN_GRID_32
            MENUITEM "64",                          IDMN_GRID_64
        END
        POPUP "Se&gment"
        BEGIN
            MENUITEM "&Linear",                     IDMN_SEGMENT_LINEAR
            MENUITEM "&Bezier",                     IDMN_SEGMENT_BEZIER
            MENUITEM SEPARATOR
            POPUP "Detail Level"
            BEGIN
                MENUITEM "1",                           IDMN_DETAIL_1
                MENUITEM "2",                           IDMN_DETAIL_2
                MENUITEM "3",                           IDMN_DETAIL_3
                MENUITEM "4",                           IDMN_DETAIL_4
                MENUITEM "5",                           IDMN_DETAIL_5
                MENUITEM "10",                          IDMN_DETAIL_10
                MENUITEM "15",                          IDMN_DETAIL_15
                MENUITEM "20",                          IDMN_DETAIL_20
                MENUITEM "&Custom...",                  IDMN_DETAIL_CUSTOM
            END
        END
    END
    POPUP "&View"
    BEGIN
        MENUITEM "Zoom &In",                    IDMN_2DSE_ZOOM_IN
        MENUITEM "Zoom &Out",                   IDMN_2DSE_ZOOM_OUT
        MENUITEM "&Pixel (1:1) at (256x256)",   IDMN_2DSE_PER_PIXEL_AT_256X256
        MENUITEM "&Save as Bitmap",             IDMN_SAVE_VIEWPORT_AS_BMP
    END
    POPUP "&Process"
    BEGIN
        MENUITEM "&Sheet",                      IDMN_2DSE_PROCESS_SHEET
        MENUITEM "&Revolve...",                 IDMN_2DSE_PROCESS_REVOLVE
        MENUITEM "&Extrude...",                 IDMN_2DSE_PROCESS_EXTRUDE
        MENUITEM "Extrude to &Point...",        IDMN_2DSE_PROCESS_EXTRUDETOPOINT
        MENUITEM "Extrude to Bevel...",         IDMN_2DSE_PROCESS_EXTRUDETOBEVEL
    END
END

IDMENU_BrowserMesh MENU
BEGIN
    POPUP "&File"
    BEGIN
        MENUITEM "New...",                      IDMN_MB_NEW
        MENUITEM "Open...",                     IDMN_MB_OPEN
        MENUITEM "Save",                        IDMN_MB_SAVE
    END
    POPUP "&Mesh"
    BEGIN
        MENUITEM "&Properties",                 IDMN_MB_PROPS
        MENUITEM "&Delete",                     IDMN_MB_DELETE
    END
    POPUP "&View"
    BEGIN
        MENUITEM "&Docked",                     IDMN_MB_DOCK
        MENUITEM SEPARATOR
        POPUP "WindowedRenderDevice"
        BEGIN
            MENUITEM "&Software",                   IDMN_RD_SOFTWARE
            MENUITEM "&Direct3D",                   IDMN_RD_DIRECT3D
            MENUITEM "&Vulkan",                     IDMN_RD_VULKAN
        END
    END
END

IDMENU_BrowserParticle MENU
BEGIN
    POPUP "&File"
    BEGIN
        MENUITEM "New...",                      IDMN_MB_NEW
        MENUITEM "Open...",                     IDMN_MB_OPEN
        MENUITEM "Save",                        IDMN_MB_SAVE
    END
    POPUP "&Particle"
    BEGIN
        MENUITEM "&Properties",                 IDMN_MB_PROPS
        MENUITEM "&Delete",                     IDMN_MB_DELETE
    END
    POPUP "&View"
    BEGIN
        MENUITEM "&Docked",                     IDMN_MB_DOCK
        MENUITEM SEPARATOR
        POPUP "WindowedRenderDevice"
        BEGIN
            MENUITEM "&Software",                   IDMN_RD_SOFTWARE
            MENUITEM "&Direct3D",                   IDMN_RD_DIRECT3D
            MENUITEM "&Vulkan",                     IDMN_RD_VULKAN
        END
    END
END

IDMENU_VIEWPORT_FRAME MENU
BEGIN
    POPUP "ViewportFrame"
    BEGIN
        MENUITEM "Menu",                        IDMN_VF_MENU
        MENUITEM SEPARATOR
        MENUITEM "Realtime Preview",            IDMN_VF_REALTIME_PREVIEW
        MENUITEM SEPARATOR
        MENUITEM "Top",                         ID_MapOverhead
        MENUITEM "Front",                       ID_MapXZ
        MENUITEM "Side",                        ID_MapYZ
        MENUITEM SEPARATOR
        MENUITEM "Perspective",                 ID_MapWire
        MENUITEM "Texture Usage",               ID_MapPolys
        MENUITEM "BSP Cuts",                    ID_MapPolyCuts
        MENUITEM "Textured",                    ID_MapPlainTex
        MENUITEM "Dynamic Lighting",            ID_MapDynLight
        MENUITEM "Zones/Portals",               ID_MapZones
        MENUITEM "No Pass",                     ID_MapNoPass
    END
END

IDMENU_VF_CONTEXT MENU
BEGIN
    POPUP "Context"
    BEGIN
        POPUP "&Mode"
        BEGIN
            MENUITEM "Top",                         ID_MapOverhead
            MENUITEM "Front",                       ID_MapXZ
            MENUITEM "Side",                        ID_MapYZ
            MENUITEM SEPARATOR
            MENUITEM "Perspective",                 ID_MapWire
            MENUITEM "Texture Usage",               ID_MapPolys
            MENUITEM "BSP Cuts",                    ID_MapPolyCuts
            MENUITEM "Textured",                    ID_MapPlainTex
            MENUITEM "Dynamic Lighting",            ID_MapDynLight
            MENUITEM "Zones/Portals",               ID_MapZones
            MENUITEM "No Pass",                     ID_MapNoPass
        END
        POPUP "&View"
        BEGIN
            MENUITEM "Show Active &Brush",          ID_ShowBrush
            MENUITEM "Show &Moving Brushes",        ID_ShowMovingBrushes
            MENUITEM "Show Bac&kdrop",              ID_ShowBackdrop
            MENUITEM "Show &Coordinates",           ID_ShowCoords
            MENUITEM "Show Paths",                  ID_ShowPaths
        END
        POPUP "&Actors"
        BEGIN
            MENUITEM "&Full Actor View",            ID_ActorsShow
            MENUITEM "&Icon View",                  ID_ActorsIcons
            MENUITEM "&Radii View",                 ID_ActorsRadii
            MENUITEM "&Hide Actors",                ID_ActorsHide
        END
        POPUP "&Window"
        BEGIN
            MENUITEM "16-Bit Color",                ID_Color16Bit
            MENUITEM "32-Bit Color",                ID_Color32Bit
        END
        MENUITEM SEPARATOR
        MENUITEM "&Software",                   IDMN_RD_SOFTWARE
        MENUITEM "&Direct3D",                   IDMN_RD_DIRECT3D
        MENUITEM "&Vulkan",                     IDMN_RD_VULKAN
    END
END

IDMENU_BrowserActor_Context MENU
BEGIN
    POPUP "Context"
    BEGIN
        MENUITEM "&New...",                     IDMN_AB_NEW_CLASS
        MENUITEM "&Edit Script...",             IDMN_AB_EDIT_SCRIPT
        MENUITEM SEPARATOR
        MENUITEM "&Default Properties...",      IDMN_AB_DEF_PROP
        MENUITEM "&Reset All Actors",           IDMN_AB_RESET_PROP
    END
END

IDMENU_BrowserGroup MENU
BEGIN
    POPUP "&Edit"
    BEGIN
        MENUITEM "&New Group",                  IDMN_GB_NEW_GROUP
        MENUITEM "&Rename Group",               IDMN_GB_RENAME_GROUP
        MENUITEM "&Delete Group",               IDMN_GB_DELETE_GROUP
        MENUITEM SEPARATOR
        MENUITEM "&Add Selected Actors to Group", IDMN_GB_ADD_TO_GROUP
        MENUITEM "&Add &Delete Selected Actors from Group", IDMN_GB_DELETE_FROM_GROUP
        MENUITEM SEPARATOR
        MENUITEM "&Select Actors",              IDMN_GB_SELECT
        MENUITEM "&Deselect Actors",            IDMN_GB_DESELECT
    END
    POPUP "&View"
    BEGIN
        MENUITEM "&Docked",                     IDMN_MB_DOCK
        MENUITEM SEPARATOR
        MENUITEM "&Refresh",                    IDMN_GB_REFRESH
    END
END

IDMENU_BrowserMaster MENU
BEGIN
    POPUP "&View"
    BEGIN
        MENUITEM "&Dock",                       IDMN_MB_DOCK
    END
END


/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDDIALOG_Splash DIALOGEX 0, 0, 246, 65
STYLE DS_SETFONT | DS_MODALFRAME | DS_SETFOREGROUND | DS_CENTER | WS_POPUP | WS_VISIBLE
EXSTYLE WS_EX_TOOLWINDOW
FONT 8, "MS Sans Serif", 0, 0, 0x1
BEGIN
    CONTROL         IDB_Logo,IDC_Logo,"Static",SS_BITMAP | SS_REALSIZEIMAGE,0,0,229,50
END

IDDIALOG_NewObject DIALOG 0, 0, 372, 231
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "New"
FONT 8, "MS Sans Serif"
BEGIN
    DEFPUSHBUTTON   "OK",IDOK,5,215,51,14
    PUSHBUTTON      "Cancel",IDCANCEL,70,215,50,14
    GROUPBOX        "Object Types",IDC_STATIC,5,5,130,205
    LISTBOX         IDC_TypeList,10,15,120,190,LBS_SORT | LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_TABSTOP
    GROUPBOX        "Creation Properties",IDC_STATIC,140,5,225,220
    CONTROL         "",IDC_PropHolder,"Static",SS_BLACKFRAME,145,15,215,205
END

IDDIALOG_2DShapeEditor_Extrude DIALOG 0, 0, 145, 45
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Extrude"
FONT 8, "MS Sans Serif"
BEGIN
    EDITTEXT        IDEC_DEPTH,37,18,40,12,ES_AUTOHSCROLL
    DEFPUSHBUTTON   "OK",IDOK,89,9,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,89,25,50,14
    GROUPBOX        "Input",IDC_STATIC,5,5,79,33
    LTEXT           "Depth :",IDC_STATIC,11,20,24,8
END

IDDIALOG_2DShapeEditor_Revolve DIALOG 0, 0, 146, 55
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Revolve"
FONT 8, "MS Sans Serif"
BEGIN
    EDITTEXT        IDEC_TOTAL_SIDES,43,18,40,12,ES_AUTOHSCROLL
    EDITTEXT        IDEC_SIDES,43,33,40,12,ES_AUTOHSCROLL
    DEFPUSHBUTTON   "OK",IDOK,93,9,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,93,25,50,14
    GROUPBOX        "Sides",IDC_STATIC,5,5,84,46
    LTEXT           "Per 360 :",IDC_STATIC,11,20,30,8
    LTEXT           "Use :",IDC_STATIC,11,32,18,8
END

IDPP_SP_FLAGS1 DIALOG 0, 0, 219, 125
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CLIPSIBLINGS | WS_CAPTION
CAPTION "Flags"
FONT 8, "MS Sans Serif"
BEGIN
    CONTROL         "Invisible",IDCK_INVISIBLE,"Button",BS_AUTO3STATE | WS_TABSTOP,6,3,41,10
    CONTROL         "Masked",IDCK_MASKED,"Button",BS_AUTO3STATE | WS_TABSTOP,6,14,41,10
    CONTROL         "Translucent",IDCK_TRANSLUCENT,"Button",BS_AUTO3STATE | WS_TABSTOP,6,25,53,10
    CONTROL         "Force View Zone",IDCK_FORCEVIEWZONE,"Button",BS_AUTO3STATE | WS_TABSTOP,6,36,70,10
    CONTROL         "Modulated",IDCK_MODULATED,"Button",BS_AUTO3STATE | WS_TABSTOP,6,47,49,10
    CONTROL         "Fake Backdrop",IDCK_FAKEBACKDROP,"Button",BS_AUTO3STATE | WS_TABSTOP,6,58,65,10
    CONTROL         "Two Sided",IDCK_2SIDED,"Button",BS_AUTO3STATE | WS_TABSTOP,6,69,50,10
    CONTROL         "U-Pan",IDCK_UPAN,"Button",BS_AUTO3STATE | WS_TABSTOP,6,80,36,10
    CONTROL         "V-Pan",IDCK_VPAN,"Button",BS_AUTO3STATE | WS_TABSTOP,6,91,35,10
    CONTROL         "High Shadow Detail",IDCK_HISHADOWDETAIL,"Button",BS_AUTO3STATE | WS_TABSTOP,6,102,79,10
    CONTROL         "Low Shadow Detail",IDCK_LOWSHADOWDETAIL,"Button",BS_AUTO3STATE | WS_TABSTOP,6,113,77,10
    CONTROL         "No Smooth",IDCK_NOSMOOTH,"Button",BS_AUTO3STATE | WS_TABSTOP,90,3,51,10
    CONTROL         "Border Filtering",IDCK_BORDERFILTERING,"Button",BS_AUTO3STATE | WS_TABSTOP,90,14,63,10 
    CONTROL         "Small Wavy",IDCK_SMALLWAVY,"Button",BS_AUTO3STATE | WS_TABSTOP,90,25,53,10
    CONTROL         "Dirty Shadows",IDCK_DIRTYSHADOWS,"Button",BS_AUTO3STATE | WS_TABSTOP,90,36,61,10
    CONTROL         "Dark Corners",IDCK_DARKCORNERS,"Button",BS_AUTO3STATE | WS_TABSTOP,90,47,60,10
    CONTROL         "Special Lit",IDCK_SPECIALLIT,"Button",BS_AUTO3STATE | WS_TABSTOP,90,58,49,10
    CONTROL         "Gouraud",IDCK_GOURAUD,"Button",BS_AUTO3STATE | WS_TABSTOP,90,69,74,10
    CONTROL         "Unlit",IDCK_UNLIT,"Button",BS_AUTO3STATE | WS_TABSTOP,90,80,30,10
    CONTROL         "Portal",IDCK_PORTAL,"Button",BS_AUTO3STATE | WS_TABSTOP,90,91,34,10
    CONTROL         "Mirror",IDCK_MIRROR,"Button",BS_AUTO3STATE | WS_TABSTOP,90,102,33,10
    CONTROL         "No Pass",IDCK_NOPASS,"Button",BS_AUTO3STATE | WS_TABSTOP,90,113,43,10
END

IDDIALOG_IMPORT_SOUND DIALOG 0, 0, 240, 82
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Import Sound"
FONT 8, "MS Sans Serif"
BEGIN
    EDITTEXT        IDEC_NAME,46,57,129,12,ES_AUTOHSCROLL
    DEFPUSHBUTTON   "OK",IDOK,185,10,50,14
    PUSHBUTTON      "OK A&ll",IDPB_OKALL,185,24,50,14
    PUSHBUTTON      "&Skip",IDPB_SKIP,185,39,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,185,53,50,14
    EDITTEXT        IDEC_PACKAGE,46,28,129,12,ES_AUTOHSCROLL
    EDITTEXT        IDEC_GROUP,46,43,129,12,ES_AUTOHSCROLL
    LTEXT           "",IDSC_FILENAME,46,14,129,12
    GROUPBOX        "Info",IDC_STATIC,5,5,174,70
    LTEXT           "Name:",IDC_STATIC,12,59,22,8
    LTEXT           "Group:",IDC_STATIC,12,45,22,8
    LTEXT           "Package:",IDC_STATIC,12,30,32,8
    LTEXT           "File :",IDC_STATIC,12,16,16,8
END

IDDIALOG_IMPORT_TEXTURE DIALOG 0, 0, 240, 122
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Import Texture"
FONT 8, "MS Sans Serif"
BEGIN
    EDITTEXT        IDEC_PACKAGE,46,28,129,12,ES_AUTOHSCROLL
    EDITTEXT        IDEC_GROUP,46,42,129,12,ES_AUTOHSCROLL
    EDITTEXT        IDEC_NAME,46,56,129,12,ES_AUTOHSCROLL
    CONTROL         "&Masked",IDCK_MASKED,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,12,91,41,10
    CONTROL         "&Generate MipMaps",IDCK_MIPMAP,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,63,91,76,10
    DEFPUSHBUTTON   "OK",IDOK,185,10,50,14
    PUSHBUTTON      "OK A&ll",IDPB_OKALL,185,24,50,14
    PUSHBUTTON      "&Skip",IDPB_SKIP,185,39,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,185,53,50,14
    LTEXT           "",IDSC_FILENAME,46,14,129,12
    GROUPBOX        "Info",IDC_STATIC,5,5,174,70
    LTEXT           "Name:",IDC_STATIC,12,58,22,8
    LTEXT           "Group:",IDC_STATIC,12,44,22,8
    LTEXT           "Package:",IDC_STATIC,12,30,32,8
    LTEXT           "File :",IDC_STATIC,12,16,16,8
    GROUPBOX        "Flags",IDC_STATIC,5,78,174,32
END

IDDIALOG_TEX_PROP DIALOG 0, 0, 447, 197
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Texture Properties"
FONT 8, "MS Sans Serif"
BEGIN
    DEFPUSHBUTTON   "Clear",IDPB_CLEAR,2,180,51,14
    GROUPBOX        "Texture",IDC_STATIC,2,2,182,174
    GROUPBOX        "Properties",IDC_STATIC,191,2,253,174
    CONTROL         "",IDSC_PROPS,"Static",SS_BLACKFRAME,196,12,244,159
    CONTROL         "",IDSC_TEXTURE,"Static",SS_BLACKFRAME | NOT WS_VISIBLE,8,13,83,60
END

IDD_CreateDialog DIALOG 0, 0, 207, 52
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Create Object"
FONT 8, "MS Sans Serif"
BEGIN
    DEFPUSHBUTTON   "OK",IDOK,150,7,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,150,24,50,14
    LTEXT           "Class",-1,7,7,38,8
    LTEXT           "Name",-1,7,20,38,8
    LTEXT           "Outer",-1,7,34,38,8
    COMBOBOX        IDC_Class,45,7,94,108,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    EDITTEXT        IDC_Name,45,21,94,13,ES_AUTOHSCROLL
    EDITTEXT        IDC_Outer,45,34,94,13,ES_AUTOHSCROLL | WS_DISABLED
END

IDD_ImportDialog DIALOG 0, 0, 207, 66
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Import Object"
FONT 8, "MS Sans Serif"
BEGIN
    DEFPUSHBUTTON   "OK",IDOK,150,7,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,150,24,50,14
    LTEXT           "Class",IDC_STATIC,7,7,38,8
    LTEXT           "Name",IDC_STATIC,7,20,38,8
    LTEXT           "Filename",IDC_STATIC,7,47,38,8
    LTEXT           "Outer",IDC_STATIC,7,34,38,8
    COMBOBOX        IDC_Class,45,7,94,108,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    EDITTEXT        IDC_Name,45,21,94,13,ES_AUTOHSCROLL
    EDITTEXT        IDC_Outer,45,34,94,13,ES_AUTOHSCROLL | WS_DISABLED
    EDITTEXT        IDC_Filename,45,47,94,12,ES_AUTOHSCROLL
    PUSHBUTTON      ".....",IDC_BrowseFiles,139,47,12,11
END

IDDIALOG_NEW_TEXTURE DIALOG 0, 0, 240, 97
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "New Texture"
FONT 8, "MS Sans Serif"
BEGIN
    EDITTEXT        IDEC_NAME,46,43,129,12,ES_AUTOHSCROLL
    COMBOBOX        IDCB_CLASS,46,57,129,217,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    COMBOBOX        IDCB_WIDTH,45,72,38,202,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    COMBOBOX        IDCB_HEIGHT,129,72,38,243,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    DEFPUSHBUTTON   "OK",IDOK,185,10,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,185,25,50,14
    EDITTEXT        IDEC_PACKAGE,46,15,129,12,ES_AUTOHSCROLL
    EDITTEXT        IDEC_GROUP,46,29,129,12,ES_AUTOHSCROLL
    GROUPBOX        "Info",IDC_STATIC,5,5,174,86
    LTEXT           "Name:",IDC_STATIC,12,45,22,8
    LTEXT           "Group:",IDC_STATIC,12,31,22,8
    LTEXT           "Package:",IDC_STATIC,12,17,32,8
    LTEXT           "Class:",IDC_STATIC,12,59,20,8
    LTEXT           "Width:",IDC_STATIC,11,74,22,8
    LTEXT           "Height:",IDC_STATIC,95,74,24,8
END

IDDIALOG_ADD_SPECIAL DIALOG 0, 0, 212, 98
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION
CAPTION "Add Special"
FONT 8, "MS Sans Serif"
BEGIN
    COMBOBOX        IDCB_PREFABS,41,15,108,196,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    CONTROL         "&Masked",IDCK_MASKED,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,12,32,41,10
    CONTROL         "&Transparent",IDCK_TRANSPARENT,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,12,43,54,10
    CONTROL         "&Zone Portal",IDCK_ZONE_PORTAL,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,12,55,53,10
    CONTROL         "&Invisible",IDCK_INVIS,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,12,66,41,10
    CONTROL         "&2 Sided",IDCK_TWO_SIDED,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,12,77,40,10
    CONTROL         "&Solid",IDRB_SOLID,"Button",BS_AUTORADIOBUTTON | WS_GROUP | WS_TABSTOP,85,43,31,10
    CONTROL         "&Semi-Solid",IDRB_SEMI_SOLID,"Button",BS_AUTORADIOBUTTON,85,55,49,10
    CONTROL         "&Non-Solid",IDRB_NON_SOLID,"Button",BS_AUTORADIOBUTTON,85,66,47,10
    DEFPUSHBUTTON   "OK",IDOK,159,10,50,14,WS_GROUP
    PUSHBUTTON      "&Close",IDPB_CLOSE,159,25,50,14
    GROUPBOX        "Flags",IDC_STATIC,5,5,149,86
    LTEXT           "Prefabs :",IDC_STATIC,11,17,29,8
    GROUPBOX        "Solidity",IDC_STATIC,81,34,55,45
END

IDDIALOG_PROGRESS DIALOG 0, 0, 295, 37
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION
CAPTION "Progress"
FONT 8, "MS Sans Serif"
BEGIN
    CONTROL         "",IDSC_MSG,"Static",SS_LEFTNOWORDWRAP | WS_GROUP,3,4,230,8
    CONTROL         "Progress1",IDPG_PROGRESS,"msctls_progress32",WS_BORDER,3,17,230,14
    PUSHBUTTON      "Cancel",IDPB_CANCEL,240,12,50,14,WS_DISABLED
END

IDDIALOG_NEW_CLASS DIALOG 0, 0, 240, 66
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "New Class"
FONT 8, "MS Sans Serif"
BEGIN
    EDITTEXT        IDEC_PACKAGE,46,29,129,12,ES_AUTOHSCROLL
    EDITTEXT        IDEC_NAME,46,43,129,12,ES_AUTOHSCROLL
    DEFPUSHBUTTON   "OK",IDOK,185,10,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,185,25,50,14
    GROUPBOX        "Info",-1,5,5,174,55
    LTEXT           "Name:",-1,12,45,22,8
    LTEXT           "Package:",-1,12,31,32,8
    LTEXT           "Parent:",-1,12,17,24,8
    LTEXT           "",IDSC_PARENT,47,17,129,12
END

IDDIALOG_IMPORT_MUSIC DIALOG 0, 0, 240, 71
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Import Music"
FONT 8, "MS Sans Serif"
BEGIN
    EDITTEXT        IDEC_NAME,46,26,129,12,ES_AUTOHSCROLL
    DEFPUSHBUTTON   "OK",IDOK,185,10,50,14
    PUSHBUTTON      "OK A&ll",IDPB_OKALL,185,24,50,14
    PUSHBUTTON      "&Skip",IDPB_SKIP,185,39,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,185,53,50,14
    LTEXT           "",IDSC_FILENAME,46,14,129,12
    GROUPBOX        "Info",-1,5,5,174,39
    LTEXT           "Name:",-1,12,28,22,8
    LTEXT           "File :",-1,12,16,16,8
END

IDDIALOG_FINDREPLACE DIALOGEX 0, 0, 301, 105
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION
EXSTYLE WS_EX_TOOLWINDOW
CAPTION "Find"
FONT 8, "MS Sans Serif", 0, 0, 0x1
BEGIN
    CONTROL         "&Match Case",IDCK_MATCH_CASE,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,9,59,54,10
    DEFPUSHBUTTON   "&Find",IDPB_FIND,247,5,50,14
    PUSHBUTTON      "&Next",IDPB_FIND_NEXT,247,19,50,14
    PUSHBUTTON      "&Prev",IDPB_FIND_PREV,247,34,50,14
    PUSHBUTTON      "&Replace",IDPB_REPLACE,247,52,50,14,NOT WS_VISIBLE
    PUSHBUTTON      "Replace &All",IDPB_REPLACE_ALL,247,67,50,14,NOT WS_VISIBLE
    PUSHBUTTON      "Close",IDPB_CLOSE,247,86,50,14
    GROUPBOX        "Input",IDC_STATIC,3,2,240,41
    LTEXT           "Find :",IDC_STATIC,9,13,18,8
    LTEXT           "Replace With:",IDC_STATIC,9,27,46,8,NOT WS_VISIBLE
    GROUPBOX        "Options",IDC_STATIC,3,46,68,31
    COMBOBOX        IDCB_FIND,58,11,180,101,CBS_DROPDOWN | WS_VSCROLL | WS_TABSTOP
    COMBOBOX        IDCB_REPLACE,58,25,180,99,CBS_DROPDOWN | NOT WS_VISIBLE | WS_VSCROLL | WS_TABSTOP
END

IDDIALOG_IMPORT_BRUSH DIALOG 0, 0, 240, 82
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Import Brush"
FONT 8, "MS Sans Serif"
BEGIN
    CONTROL         "&Merge faces?",IDCK_MERGE_FACES,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,13,17,59,10
    CONTROL         "Solid mesh (continuous mesh, no gaps or holes).",IDRB_SOLID,
                    "Button",BS_AUTORADIOBUTTON | WS_GROUP | WS_TABSTOP,12,47,164,10
    CONTROL         "Nonsolid (contains gaps or holes)",IDRB_NONSOLID,"Button",BS_AUTORADIOBUTTON,12,59,120,10
    DEFPUSHBUTTON   "OK",IDOK,185,10,50,14,WS_GROUP
    PUSHBUTTON      "Cancel",IDCANCEL,185,25,50,14
    GROUPBOX        "Options",IDC_STATIC,5,5,174,27
    GROUPBOX        "Solidity",IDC_STATIC,5,36,174,38
END

IDPP_BUILD_OPTIONS DIALOG 0, 0, 224, 265
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CLIPSIBLINGS | WS_CAPTION
CAPTION "Options"
FONT 8, "MS Sans Serif"
BEGIN
    GROUPBOX        "",IDC_STATIC,4,3,214,25
    CONTROL         "&Geometry",IDCK_GEOMETRY,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,8,2,46,10
    CONTROL         "&Only Rebuild Visible Actors?",IDCK_ONLY_VISIBLE,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,12,14,104,10
    GROUPBOX        "",IDC_STATIC,4,32,214,128
    CONTROL         "&BSP",IDCK_BSP,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,8,31,30,10
    GROUPBOX        "Optimization",IDSC_OPTIMIZATION,9,45,50,49
    CONTROL         "&Lame",IDRB_LAME,"Button",BS_AUTORADIOBUTTON | WS_GROUP | WS_TABSTOP,12,56,33,10
    CONTROL         "&Good",IDRB_GOOD,"Button",BS_AUTORADIOBUTTON,12,67,33,10
    CONTROL         "&Optimal",IDRB_OPTIMAL,"Button",BS_AUTORADIOBUTTON,12,79,39,10
    CONTROL         "&Optimize Geometry",IDCK_OPT_GEOM,"Button",BS_AUTOCHECKBOX | WS_GROUP | WS_TABSTOP,66,50,73,10
    CONTROL         "&Build Visibility Zones",IDCK_BUILD_VIS_ZONES,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,66,62,78,10
    CONTROL         "Slider1",IDSL_BALANCE,"msctls_trackbar32",WS_TABSTOP,13,101,193,15
    CONTROL         "Slider1",IDSL_PORTALBIAS,"msctls_trackbar32",WS_TABSTOP,13,130,193,15
    GROUPBOX        "",IDC_STATIC,4,163,213,28
    CONTROL         "&Lighting",IDCK_LIGHTING,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,8,162,41,10
    CONTROL         "&Apply selected lights/lights in selected zone descriptors only",IDCK_SEL_LIGHTS_ONLY,
                    "Button",BS_AUTOCHECKBOX | WS_TABSTOP,12,175,203,10
    GROUPBOX        "",IDC_STATIC,4,194,213,30
    CONTROL         "&Define Paths",IDCK_PATH_DEFINE,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,8,194,57,10
    DEFPUSHBUTTON   "&Create New Path Network",IDPB_BUILD_PATHS,4,246,97,14
    DEFPUSHBUTTON   "&Build",IDPB_BUILD,168,246,50,14
    LTEXT           "Minimize Cuts",IDSC_BSP_1,19,116,46,8
    LTEXT           "Balance Tree",IDSC_BSP_2,160,116,45,8
    CTEXT           "15",IDSC_BALANCE,74,116,76,8
    LTEXT           "Ignore Portals",IDSC_BSP_3,19,145,44,8
    LTEXT           "Portals Cut All",IDSC_BSP_4,156,145,48,8
    CTEXT           "70",IDSC_PORTALBIAS,74,145,76,8
END

IDPP_BUILD_STATS DIALOG 0, 0, 224, 265
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CLIPSIBLINGS | WS_CAPTION
CAPTION "Stats"
FONT 8, "MS Sans Serif"
BEGIN
    DEFPUSHBUTTON   "&Build",IDPB_BUILD,168,246,50,14
    GROUPBOX        "",IDC_STATIC,5,3,213,36
    GROUPBOX        "",IDC_STATIC,5,40,213,68
    GROUPBOX        "",IDC_STATIC,5,109,213,30
    GROUPBOX        "",IDC_STATIC,5,141,213,27
    LTEXT           "Geometry",IDC_STATIC,9,3,31,8
    LTEXT           "BSP",IDC_STATIC,13,40,15,8
    LTEXT           "Lighting",IDC_STATIC,9,109,24,8
    LTEXT           "Paths",IDC_STATIC,9,141,18,8
    LTEXT           "Brushes :",IDC_STATIC,13,17,30,8
    LTEXT           "",IDSC_BRUSHES,51,17,28,8
    LTEXT           "Zones :",IDC_STATIC,13,27,25,8
    LTEXT           "",IDSC_ZONES,51,27,28,8
    LTEXT           "Polys :",IDC_STATIC,13,52,22,8
    LTEXT           "",IDSC_POLYS,51,52,28,8
    LTEXT           "Nodes :",IDC_STATIC,13,62,26,8
    LTEXT           "",IDSC_NODES,51,62,28,8
    LTEXT           "Ratio :",IDC_STATIC,13,73,22,8
    LTEXT           "",IDSC_RATIO,51,73,28,8
    LTEXT           "Max Depth :",IDC_STATIC,13,84,40,8
    LTEXT           "",IDSC_MAX_DEPTH,51,84,28,8
    LTEXT           "Avg Depth :",IDC_STATIC,13,95,39,8
    LTEXT           "",IDSC_AVG_DEPTH,51,95,28,8
    LTEXT           "Lights :",IDC_STATIC,13,122,24,8
    LTEXT           "",IDSC_LIGHTS,51,122,28,8
    PUSHBUTTON      "&Refresh",IDPB_REFRESH,116,246,50,14
END

IDDIALOG_SEARCH DIALOGEX 0, 0, 333, 159
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION
EXSTYLE WS_EX_TOOLWINDOW
CAPTION "Search for Actors"
FONT 8, "MS Sans Serif", 0, 0, 0x1
BEGIN
    GROUPBOX        "Filter",IDC_STATIC,140,4,135,66
    LTEXT           "Name:",IDC_STATIC,143,15,23,8
    EDITTEXT        IDEC_NAME,170,13,98,12,ES_AUTOHSCROLL | WS_TABSTOP
    LTEXT           "Event:",IDC_STATIC,143,28,25,8
    EDITTEXT        IDEC_EVENT,170,27,98,12,ES_AUTOHSCROLL | WS_TABSTOP
    LTEXT           "Tag:",IDC_STATIC,143,43,17,8
    EDITTEXT        IDEC_TAG,170,41,98,12,ES_AUTOHSCROLL | WS_TABSTOP
    CONTROL         "&Whole Word",IDCK_WHOLE_WORD,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,170,56,90,10
    PUSHBUTTON      "&Select",IDPB_SELECT,279,8,50,14
    GROUPBOX        "Names",IDC_STATIC,3,2,135,150
    LISTBOX         IDLB_NAMES,7,13,125,133,LBS_SORT | LBS_NOINTEGRALHEIGHT | LBS_EXTENDEDSEL | LBS_USETABSTOPS | WS_VSCROLL | WS_TABSTOP
    PUSHBUTTON      "&Close",IDPB_CLOSE,279,23,50,14
    LTEXT           "",IDSC_FOUND,143,144,185,8
END

IDDIALOG_IMPORT_MAP DIALOG 0, 0, 162, 45
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Import Map"
FONT 8, "MS Sans Serif"
BEGIN
    CONTROL         "&Import into existing map?",IDCK_NEW_MAP,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,12,17,84,10
    DEFPUSHBUTTON   "OK",IDOK,108,9,50,14,WS_GROUP
    PUSHBUTTON      "Cancel",IDCANCEL,108,24,50,14
    GROUPBOX        "Options",-1,5,5,99,28
END

IDDIALOG_SAVE_MAP DIALOG 0, 0, 162, 45
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Save Map"
FONT 8, "MS Sans Serif"
BEGIN
    CONTROL         "&Compress map?",IDCK_COMPRESS_MAP,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,12,17,84,10
    DEFPUSHBUTTON   "OK",IDOK,108,9,50,14,WS_GROUP
    PUSHBUTTON      "Cancel",IDCANCEL,108,24,50,14
    GROUPBOX        "Options",-1,5,5,99,28
END

IDDIALOG_SCALE_LIGHTS DIALOGEX 0, 0, 190, 49
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION
EXSTYLE WS_EX_TOOLWINDOW
CAPTION "Scale Lights"
FONT 8, "MS Sans Serif", 0, 0, 0x1
BEGIN
    EDITTEXT        IDEC_VALUE,89,9,40,12,ES_AUTOHSCROLL
    DEFPUSHBUTTON   "OK",IDOK,135,6,50,14
    PUSHBUTTON      "Close",IDPB_CLOSE,135,21,50,14
    CONTROL         "&Literal Value",IDRB_LITERAL,"Button",BS_AUTORADIOBUTTON | WS_GROUP | WS_TABSTOP,8,14,52,10
    GROUPBOX        "Scale By",IDC_STATIC,3,2,60,41
    CONTROL         "&Percentage",IDRB_PERCENTAGE,"Button",BS_AUTORADIOBUTTON,8,25,51,10
    LTEXT           "Value :",IDC_STATIC,66,11,22,8
END

IDPP_SP_STATS DIALOG 0, 0, 219, 125
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CLIPSIBLINGS | WS_CAPTION
CAPTION "Stats"
FONT 8, "MS Sans Serif"
BEGIN
    GROUPBOX        "Lighting",IDC_STATIC,4,2,84,47
    LTEXT           "Lights:",IDC_STATIC,9,14,20,8
    LTEXT           "",IDSC_STATIC_LIGHTS,44,13,41,10
    LTEXT           "Meshels :",IDC_STATIC,9,25,30,8
    LTEXT           "",IDSC_MESHELS,44,24,41,10
    LTEXT           "Mesh Size :",IDC_STATIC,9,36,35,8
    LTEXT           "",IDSC_MESH_SIZE,44,35,41,10
END

IDDIALOG_VIEWPORT_CONFIG DIALOG 0, 0, 219, 62
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Viewport Configuration"
FONT 8, "MS Sans Serif"
BEGIN
    CONTROL         "CFG0",IDRB_VCONFIG0,"Button",BS_AUTORADIOBUTTON | BS_BITMAP | BS_PUSHLIKE | WS_GROUP | WS_TABSTOP,9,15,35,35
    CONTROL         "CFG1",IDRB_VCONFIG1,"Button",BS_AUTORADIOBUTTON | BS_BITMAP | BS_PUSHLIKE,45,15,35,35
    CONTROL         "CFG2",IDRB_VCONFIG2,"Button",BS_AUTORADIOBUTTON | BS_BITMAP | BS_PUSHLIKE,81,15,35,35
    CONTROL         "CFG3",IDRB_VCONFIG3,"Button",BS_AUTORADIOBUTTON | BS_BITMAP | BS_PUSHLIKE,117,15,35,35
    DEFPUSHBUTTON   "OK",IDOK,164,5,50,14,WS_GROUP
    PUSHBUTTON      "Cancel",IDCANCEL,164,21,50,14
    GROUPBOX        "Configuration",-1,3,2,155,54
END

IDDIALOG_BRUSH_BUILDER DIALOG 0, 0, 226, 157
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Brush Builder"
FONT 8, "MS Sans Serif"
BEGIN
    DEFPUSHBUTTON   "&Build",IDPB_BUILD,171,3,51,14
    GROUPBOX        "Properties",-1,2,0,165,153
    CONTROL         "",IDSC_PROPS,"Static",SS_BLACKFRAME,7,11,155,138
    PUSHBUTTON      "Close",IDCANCEL,171,18,51,14
END

IDDIALOG_2DShapeEditor_ExtrudeToPoint DIALOG 0, 0, 145, 45
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Extrude to Point"
FONT 8, "MS Sans Serif"
BEGIN
    EDITTEXT        IDEC_DEPTH,37,18,40,12,ES_AUTOHSCROLL
    DEFPUSHBUTTON   "OK",IDOK,89,9,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,89,25,50,14
    GROUPBOX        "Input",IDC_STATIC,5,5,79,33
    LTEXT           "Depth :",IDC_STATIC,11,20,24,8
END

IDDIALOG_2DShapeEditor_ExtrudeToBevel DIALOG 0, 0, 153, 56
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Extrude to Bevel"
FONT 8, "MS Sans Serif"
BEGIN
    EDITTEXT        IDEC_DEPTH,51,18,40,12,ES_AUTOHSCROLL
    EDITTEXT        IDEC_CAP_HEIGHT,51,31,40,12,ES_AUTOHSCROLL
    DEFPUSHBUTTON   "OK",IDOK,99,9,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,99,25,50,14
    GROUPBOX        "Input",IDC_STATIC,5,5,91,44
    LTEXT           "Depth :",IDC_STATIC,11,20,24,8
    LTEXT           "Cap Height :",IDC_STATIC,10,33,38,8
END

IDPP_SP_ALIGNMENT DIALOG 0, 0, 219, 125
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CLIPSIBLINGS | WS_CAPTION
CAPTION "Alignment"
FONT 8, "MS Sans Serif"
BEGIN
    GROUPBOX        "Pan",IDC_STATIC,4,2,82,43
    PUSHBUTTON      "1",IDPB_PAN_U_1,21,11,14,14
    LTEXT           "U :",IDC_STATIC,9,13,10,8
    PUSHBUTTON      "4",IDPB_PAN_U_4,36,11,14,14
    PUSHBUTTON      "16",IDPB_PAN_U_16,51,11,14,14
    PUSHBUTTON      "64",IDPB_PAN_U_64,66,11,14,14
    PUSHBUTTON      "1",IDPB_PAN_V_1,21,27,14,14
    LTEXT           "V :",IDC_STATIC,9,29,10,8
    PUSHBUTTON      "4",IDPB_PAN_V_4,36,27,14,14
    PUSHBUTTON      "16",IDPB_PAN_V_16,51,27,14,14
    PUSHBUTTON      "64",IDPB_PAN_V_64,66,27,14,14
    GROUPBOX        "Rotation",IDC_STATIC,91,2,64,43
    PUSHBUTTON      "45",IDPB_ROT_45,95,11,27,14
    PUSHBUTTON      "Flip U",IDPB_ROT_FLIP_U,95,27,27,14
    PUSHBUTTON      "Flip V",IDPB_ROT_FLIP_V,124,27,27,14
    GROUPBOX        "Alignment",IDC_STATIC,3,48,59,73
    PUSHBUTTON      "Align to Floor",IDPB_ALIGN_FLOOR,8,58,50,14
    PUSHBUTTON      "Wall Direction",IDPB_ALIGN_WALLDIR,8,73,50,14
    PUSHBUTTON      "Wall Pan",IDPB_ALIGN_WALLPAN,8,88,50,14
    PUSHBUTTON      "Unalign",IDPB_ALIGN_UNALIGN,8,103,50,14
    GROUPBOX        "Scaling",IDC_STATIC,67,48,137,65
    LTEXT           "Simple :",IDC_STATIC,73,61,26,8
    COMBOBOX        IDCB_SIMPLE_SCALE,106,59,48,158,CBS_DROPDOWN | WS_VSCROLL | WS_TABSTOP
    LTEXT           "U :",IDC_STATIC,73,77,10,8
    LTEXT           "V :",IDC_STATIC,73,92,10,8
    EDITTEXT        IDEC_SCALE_U,86,75,40,12,ES_AUTOHSCROLL
    EDITTEXT        IDEC_SCALE_V,86,90,40,12,ES_AUTOHSCROLL
    PUSHBUTTON      "Apply",IDPB_SCALE_APPLY,129,75,26,14
    CONTROL         "&Relative?",IDCK_RELATIVE,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,129,91,46,10
    PUSHBUTTON      "90",IDPB_ROT_90,124,11,27,14
    CTEXT           "Hold SHIFT to Pan/Rotate in the opposite direction.",IDC_STATIC,155,11,61,33
    PUSHBUTTON      "Apply",IDPB_SCALE_APPLY2,156,58,26,14
END

IDDIALOG_GROUP DIALOGEX 0, 0, 201, 44
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION
EXSTYLE WS_EX_TOOLWINDOW
CAPTION "???"
FONT 8, "MS Sans Serif", 0, 0, 0x1
BEGIN
    EDITTEXT        IDEC_NAME,37,14,98,12,ES_AUTOHSCROLL
    PUSHBUTTON      "OK",IDOK,146,7,50,14
    LTEXT           "Name :",-1,10,16,23,8
    GROUPBOX        "Group",-1,7,4,135,28
    PUSHBUTTON      "Cancel",IDCANCEL,146,22,50,14
END

IDDIALOG_RENAME DIALOGEX 0, 0, 201, 44
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION
EXSTYLE WS_EX_TOOLWINDOW
CAPTION "Rename"
FONT 8, "MS Sans Serif", 0, 0, 0x1
BEGIN
    EDITTEXT        IDEC_NAME,37,14,98,12,ES_AUTOHSCROLL
    PUSHBUTTON      "OK",IDOK,146,7,50,14
    LTEXT           "Name :",-1,10,16,23,8
    PUSHBUTTON      "Cancel",IDCANCEL,146,22,50,14
END

IDDIALOG_2DSE_CUSTOM_DETAIL DIALOGEX 0, 0, 131, 44
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION
EXSTYLE WS_EX_TOOLWINDOW
CAPTION "Detail Level"
FONT 8, "MS Sans Serif", 0, 0, 0x1
BEGIN
    EDITTEXT        IDEC_VALUE,37,14,29,12,ES_AUTOHSCROLL
    PUSHBUTTON      "OK",IDOK,74,7,50,14
    LTEXT           "Value :",-1,10,16,22,8
    PUSHBUTTON      "Cancel",IDCANCEL,74,22,50,14
END

IDDIALOG_TEX_REPLACE DIALOG 0, 0, 345, 183
STYLE DS_SETFONT | DS_MODALFRAME | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Find and Replace Textures"
FONT 8, "MS Sans Serif"
BEGIN
    PUSHBUTTON      "&Set",IDPB_SET1,10,158,50,14
    PUSHBUTTON      "&Set",IDPB_SET2,156,158,50,14
    DEFPUSHBUTTON   "&Replace",IDPB_REPLACE,291,9,50,14
    GROUPBOX        "Find ...",IDC_STATIC,4,2,139,176
    CONTROL         "",IDSC_TEXTURE1,"Static",SS_BLACKFRAME | NOT WS_VISIBLE,10,26,128,128
    GROUPBOX        "Replace With ...",IDC_STATIC,148,2,139,176
    CONTROL         "",IDSC_TEXTURE2,"Static",SS_BLACKFRAME | NOT WS_VISIBLE,153,26,128,128
    LTEXT           "",IDSC_TEX_NAME1,10,13,128,11,SS_CENTERIMAGE
    LTEXT           "",IDSC_TEX_NAME2,153,13,128,11,SS_CENTERIMAGE
END

IDPP_MAT_GENERAL DIALOG 0, 0, 294, 109
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CLIPSIBLINGS | WS_CAPTION
CAPTION "General"
FONT 8, "MS Sans Serif"
BEGIN
    GROUPBOX        "Interpolation Points",IDGP_GENERAL,4,2,286,62
    LTEXT           "",IDSC_VIEWPORTIP,8,11,275,32,NOT WS_VISIBLE | WS_BORDER
    SCROLLBAR       IDSB_SCROLLBAR,8,43,275,11
    COMBOBOX        IDCB_POS,235,52,48,311,CBS_DROPDOWNLIST | NOT WS_VISIBLE | WS_VSCROLL | WS_TABSTOP
    LTEXT           "Jump To:",IDC_STATIC,204,54,30,8,NOT WS_VISIBLE
    GROUPBOX        "Options",IDGP_OPTIONS,4,66,97,40
    CONTROL         "&Always show path?",IDCK_ALWAYS_SHOW_PATH,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,9,78,77,10
    CONTROL         "&Show path orientation?",IDCK_SHOW_PATH_ORIENTATION,
                    "Button",BS_AUTOCHECKBOX | WS_TABSTOP,9,89,86,10
    GROUPBOX        "Playback",IDGP_PLAYBACK,104,66,186,40
    CONTROL         "Slider1",IDSL_POSITION,"msctls_trackbar32",TBS_AUTOTICKS | WS_TABSTOP,107,81,179,19
    PUSHBUTTON      "X",IDPB_STOP,228,65,16,14,BS_BITMAP
    PUSHBUTTON      "!",IDPB_EXECUTE,211,65,16,14,BS_BITMAP
    CONTROL         "<",IDPB_BACKWARD,"Button",BS_AUTORADIOBUTTON | BS_BITMAP | BS_PUSHLIKE | WS_GROUP,251,65,16,14
    CONTROL         ">",IDPB_FORWARD,"Button",BS_AUTORADIOBUTTON | BS_BITMAP | BS_PUSHLIKE,268,65,16,14
END

IDD_TWENRATE DIALOG 0, 0, 62, 198
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION
CAPTION "Set Tween Rate"
FONT 8, "MS Sans Serif"
BEGIN
    DEFPUSHBUTTON   "OK",IDOK,7,168,50,13
    CONTROL         "Slider1",IDC_SLIDER1,"msctls_trackbar32",TBS_AUTOTICKS | TBS_VERT | TBS_BOTH | WS_BORDER | WS_TABSTOP,19,46,28,100
    CTEXT           "Slow Tween",IDC_STATIC,5,10,50,10
    LTEXT           "No Tweening",IDC_STATIC,7,148,45,10
    LTEXT           "Static",IDC_POS,15,26,32,12,NOT WS_GROUP
END


/////////////////////////////////////////////////////////////////////////////
//
// Bitmap
//

IDB_CodeFrame_TOOLBAR   BITMAP                  "..\\res\\toolbar1.bmp"

IDB_2DSE_TOOLBAR        BITMAP                  "..\\res\\cf_toolb.bmp"

IDBM_VIEWPORT_CFG0      BITMAP                  "..\\res\\idbm_vie.bmp"

IDBM_VIEWPORT_CFG1      BITMAP                  "..\\res\\bmp00001.bmp"

IDBM_VIEWPORT_CFG2      BITMAP                  "..\\res\\bmp00002.bmp"

IDBM_VIEWPORT_CFG3      BITMAP                  "..\\res\\bmp00003.bmp"

IDBM_VF_TOOLBAR         BITMAP                  "..\\res\\bmp00004.bmp"

IDB_BB_LOG_WND          BITMAP                  "..\\res\\bb_log_w.bmp"

IDB_BB_LOCK             BITMAP                  "..\\res\\bmp00005.bmp"

IDB_BB_SNAP_VTX         BITMAP                  "..\\res\\bb_lock1.bmp"

IDB_BB_GRID             BITMAP                  "..\\res\\bb_vtx_s.bmp"

IDB_BB_ROTATE_GRID      BITMAP                  "..\\res\\bb_rotat.bmp"

IDB_BB_ZOOMCENTER_ALL   BITMAP                  "..\\res\\bb_grid1.bmp"

IDB_BB_ZOOMCENTER       BITMAP                  "..\\res\\bb_zoomc.bmp"

IDB_BB_MAXIMIZE         BITMAP                  "..\\res\\bmp00006.bmp"

IDB_BOTTOM_BAR          BITMAP                  "..\\res\\bmp00007.bmp"

IDBM_2DSE               BITMAP                  "..\\res\\bmp00018.bmp"

IDBM_ACTORBROWSER       BITMAP                  "..\\res\\idbm_tex.bmp"

IDBM_ACTORPROPERTIES    BITMAP                  "..\\res\\idbm_unr.bmp"

IDBM_BUILDGEOM          BITMAP                  "..\\res\\idbm_sur.bmp"

IDBM_BUILDLIGHTING      BITMAP                  "..\\res\\idbm_bui.bmp"

IDBM_BUILDOPTIONS       BITMAP                  "..\\res\\bmp00015.bmp"

IDBM_BUILDPATHS         BITMAP                  "..\\res\\bmp00014.bmp"

IDBM_EDITFIND           BITMAP                  "..\\res\\bmp00010.bmp"

IDBM_FILENEW            BITMAP                  "..\\res\\idbm_fil.bmp"

IDBM_FILEOPEN           BITMAP                  "..\\res\\bmp00008.bmp"

IDBM_FILESAVE           BITMAP                  "..\\res\\bmp00009.bmp"

IDBM_MESHVIEWER         BITMAP                  "..\\res\\idbm_2ds.bmp"

IDBM_MUSICBROWSER       BITMAP                  "..\\res\\bmp00011.bmp"

IDBM_PLAYMAP            BITMAP                  "..\\res\\bmp00016.bmp"

IDBM_REDO               BITMAP                  "..\\res\\bmp00017.bmp"

IDBM_SOUNDBROWSER       BITMAP                  "..\\res\\bmp00012.bmp"

IDBM_SURFACEPROPERTIES  BITMAP                  "..\\res\\bmp00013.bmp"

IDBM_TEXTUREBROWSER     BITMAP                  "..\\res\\idbm_edi.bmp"

IDBM_UNDO               BITMAP                  "..\\res\\idbm_pla.bmp"

IDBM_UNREALSCRIPT       BITMAP                  "..\\res\\idbm_mes.bmp"

IDBM_DOWN_ARROW         BITMAP                  "..\\res\\idbm_dow.bmp"

IDBM_UP_ARROW           BITMAP                  "..\\res\\bmp00019.bmp"

IDBM_CAMSPEED1          BITMAP                  "..\\res\\bmp00020.bmp"

IDBM_CAMSPEED2          BITMAP                  "..\\res\\idbm_cam.bmp"

IDBM_CAMSPEED3          BITMAP                  "..\\res\\bmp00021.bmp"

IDBM_CHECKBOX_OFF       BITMAP                  "..\\res\\bmp00022.bmp"

IDBM_CHECKBOX_ON        BITMAP                  "..\\res\\idbm_che.bmp"

IDBM_PARTICLEVIEWER     BITMAP                  "..\\res\\idbm_2ds.bmp"

IDBM_GROUPBROWSER       BITMAP                  "..\\res\\idbm_mus.bmp"

IDB_BrowserSound_TOOLBAR BITMAP                  "..\\res\\bmp00025.bmp"

IDB_BrowserMusic_TOOLBAR BITMAP                  "..\\res\\browsers.bmp"

IDB_BrowserTexture_TOOLBAR BITMAP                  "..\\res\\bmp00026.bmp"

IDB_BrowserMesh_TOOLBAR BITMAP                  "..\\res\\browsert.bmp"

IDB_BrowserActor_TOOLBAR BITMAP                  "..\\res\\bmp00027.bmp"

IDB_BrowserParticle_TOOLBAR BITMAP               "..\\res\\browsert.bmp"

IDB_BrowserGroup_TOOLBAR BITMAP                  "..\\res\\bmp00028.bmp"

IDBM_BUILDALL           BITMAP                  "..\\res\\idbm_buildall.bmp"

IDBM_FORWARD            BITMAP                  "..\\res\\bmp00023.bmp"

IDBM_BACKWARD           BITMAP                  "..\\res\\idbm_for.bmp"

IDBM_EXECUTE            BITMAP                  "..\\res\\idbm_exe.bmp"

IDBM_STOP               BITMAP                  "..\\res\\idbm_bac.bmp"

IDPB_TWEN               BITMAP                  "..\\res\\idpb_twe.bmp"

IDB_BITMAP1             BITMAP                  "bb_grid1.bmp"

IDB_BITMAP2             BITMAP                  "bb_lock1.bmp"

IDB_BITMAP3             BITMAP                  "bb_log_w.bmp"

IDB_BITMAP4             BITMAP                  "bb_rotat.bmp"

IDB_BITMAP5             BITMAP                  "bb_vtx_s.bmp"

IDB_BITMAP6             BITMAP                  "bb_zoomc.bmp"

IDB_BITMAP7             BITMAP                  "bmp00001.bmp"

IDB_BITMAP8             BITMAP                  "bmp00002.bmp"

IDB_BITMAP9             BITMAP                  "bmp00003.bmp"

IDB_BITMAP10            BITMAP                  "bmp00004.bmp"

IDB_BITMAP11            BITMAP                  "bmp00005.bmp"

IDB_BITMAP12            BITMAP                  "bmp00006.bmp"

IDB_BITMAP13            BITMAP                  "bmp00007.bmp"

IDB_BITMAP14            BITMAP                  "bmp00008.bmp"

IDB_BITMAP15            BITMAP                  "bmp00009.bmp"

IDB_BITMAP16            BITMAP                  "bmp00010.bmp"

IDB_BITMAP17            BITMAP                  "bmp00011.bmp"

IDB_BITMAP18            BITMAP                  "bmp00012.bmp"

IDB_BITMAP19            BITMAP                  "bmp00013.bmp"

IDB_BITMAP20            BITMAP                  "bmp00014.bmp"

IDB_BITMAP21            BITMAP                  "bmp00015.bmp"

IDB_BITMAP22            BITMAP                  "bmp00016.bmp"

IDB_BITMAP23            BITMAP                  "bmp00017.bmp"

IDB_BITMAP24            BITMAP                  "bmp00018.bmp"

IDB_BITMAP25            BITMAP                  "bmp00019.bmp"

IDB_BITMAP26            BITMAP                  "bmp00020.bmp"

IDB_BITMAP27            BITMAP                  "bmp00021.bmp"

IDB_BITMAP28            BITMAP                  "bmp00022.bmp"

IDB_BITMAP29            BITMAP                  "bmp00023.bmp"

IDB_BITMAP30            BITMAP                  "bmp00024.bmp"

IDB_BITMAP31            BITMAP                  "bmp00025.bmp"

IDB_BITMAP32            BITMAP                  "bmp00026.bmp"

IDB_BITMAP33            BITMAP                  "bmp00027.bmp"

IDB_BITMAP34            BITMAP                  "bmp00028.bmp"

IDB_BITMAP35            BITMAP                  "browsers.bmp"

IDB_BITMAP36            BITMAP                  "browsert.bmp"

IDB_BITMAP37            BITMAP                  "cf_toolb.bmp"

IDB_BITMAP38            BITMAP                  "Icon.bmp"

IDB_BITMAP39            BITMAP                  "idbm_2ds.bmp"

IDB_BITMAP40            BITMAP                  "idbm_add.bmp"

IDB_BITMAP41            BITMAP                  "idbm_bac.bmp"

IDB_BITMAP42            BITMAP                  "idbm_bui.bmp"

IDB_BITMAP43            BITMAP                  "idbm_buildall.bmp"

IDB_BITMAP44            BITMAP                  "idbm_cam.bmp"

IDB_BITMAP45            BITMAP                  "idbm_che.bmp"

IDB_BITMAP46            BITMAP                  "idbm_del.bmp"

IDB_BITMAP47            BITMAP                  "idbm_dow.bmp"

IDB_BITMAP48            BITMAP                  "idbm_edi.bmp"

IDB_BITMAP49            BITMAP                  "idbm_exe.bmp"

IDB_BITMAP50            BITMAP                  "idbm_fil.bmp"

IDB_BITMAP51            BITMAP                  "idbm_for.bmp"

IDB_BITMAP52            BITMAP                  "idbm_mes.bmp"

IDB_BITMAP53            BITMAP                  "IDBM_MUS.BMP"

IDB_BITMAP54            BITMAP                  "idbm_new.bmp"

IDB_BITMAP55            BITMAP                  "idbm_pla.bmp"

IDB_BITMAP56            BITMAP                  "idbm_sur.bmp"

IDB_BITMAP57            BITMAP                  "idbm_tex.bmp"

IDB_BITMAP58            BITMAP                  "idbm_unr.bmp"

IDB_BITMAP59            BITMAP                  "idbm_vie.bmp"

IDB_BITMAP60            BITMAP                  "idpb_twe.bmp"

IDB_BITMAP61            BITMAP                  "Logo.bmp"

IDB_BITMAP62            BITMAP                  "TOOLBAR.BMP"

IDB_BITMAP63            BITMAP                  "toolbar1.bmp"


/////////////////////////////////////////////////////////////////////////////
//
// Accelerator
//

IDR_ACCELERATOR1 ACCELERATORS
BEGIN
    "A",            ID_EditSelectAllActors, VIRTKEY, CONTROL, NOINVERT
    "A",            ID_BrushAdd,            VIRTKEY, SHIFT, NOINVERT
    "A",            ID_EditSelectAllSurfs,  VIRTKEY, SHIFT, CONTROL, NOINVERT
    "B",            ID_ShowBrush,           VIRTKEY, NOINVERT
    "B",            ID_SurfPopupSelectMatchingBrush, VIRTKEY, SHIFT, NOINVERT
    "C",            ID_EditCopy,            VIRTKEY, CONTROL, NOINVERT
    "C",            ID_SurfPopupSelectAdjacentCoplanars, VIRTKEY, SHIFT, NOINVERT
    "D",            ID_EditDuplicate,       VIRTKEY, CONTROL, NOINVERT
    "D",            ID_BrushDeintersect,    VIRTKEY, SHIFT, NOINVERT
    "F",            ID_EditFind,            VIRTKEY, CONTROL, NOINVERT
    "F",            ID_SurfPopupSelectAdjacentFloors, VIRTKEY, SHIFT, NOINVERT
    "G",            ID_SurfPopupSelectMatchingGroups, VIRTKEY, SHIFT, NOINVERT
    "H",            ID_ActorsShow,          VIRTKEY, NOINVERT
    //"H",            ID_EditReplace,         VIRTKEY, CONTROL, NOINVERT
    //"H",            IDPB_HIDE_SELECTED,     VIRTKEY, CONTROL, NOINVERT
    //"H",            IDPB_SHOW_SELECTED,     VIRTKEY, CONTROL, ALT, NOINVERT
    //"H",            IDPB_SHOW_ALL,          VIRTKEY, SHIFT, CONTROL, NOINVERT
    //"H",            IDPB_HIDE_INVERT,       VIRTKEY, SHIFT, ALT, NOINVERT
    "I",            ID_SurfPopupSelectMatchingItems, VIRTKEY, CONTROL, NOINVERT
    "I",            ID_BrushIntersect,      VIRTKEY, SHIFT, NOINVERT
    "J",            ID_SurfPopupSelectAllAdjacents, VIRTKEY, SHIFT, NOINVERT
    "K",            ID_ShowBackdrop,        VIRTKEY, NOINVERT
    "M",            ID_SurfPopupMemorize,   VIRTKEY, SHIFT, NOINVERT
    "N",            ID_ShowPaths,           VIRTKEY, NOINVERT
    "N",            ID_FileNew,             VIRTKEY, CONTROL, NOINVERT
    "N",            IDMENU_ActorPopupSelectNone, VIRTKEY, SHIFT, NOINVERT
    "O",            ID_FileOpen,            VIRTKEY, CONTROL, NOINVERT
    "O",            ID_SurfPopupOr,         VIRTKEY, SHIFT, NOINVERT
    "P",            IDMN_VF_REALTIME_PREVIEW, VIRTKEY, NOINVERT
    "P",            ID_BuildPlay,           VIRTKEY, CONTROL, NOINVERT
    "Q",            ID_SurfPopupSelectReverse, VIRTKEY, SHIFT, NOINVERT
    "R",            ID_SurfPopupRecall,     VIRTKEY, SHIFT, NOINVERT
    "S",            ID_FileSave,            VIRTKEY, CONTROL, NOINVERT
    "S",            ID_BrushSubtract,       VIRTKEY, SHIFT, NOINVERT
    "S",            ID_FileSaveAs,          VIRTKEY, SHIFT, CONTROL, NOINVERT
    "S",            ID_SurfPopupSelectAdjacentSlants, VIRTKEY, SHIFT, NOINVERT
    "T",            ID_SurfPopupSelectMatchingTexture, VIRTKEY, SHIFT, NOINVERT
    "U",            ID_SurfPopupAnd,        VIRTKEY, SHIFT, NOINVERT
    "V",            ID_EditPaste,           VIRTKEY, CONTROL, NOINVERT
    VK_DELETE,      ID_EditDelete,          VIRTKEY, NOINVERT
    VK_F4,          ID_ViewActorProp,       VIRTKEY, NOINVERT
    VK_F5,          ID_SurfProperties,      VIRTKEY, NOINVERT
    VK_F6,          ID_ViewLevelProp,       VIRTKEY, NOINVERT
    VK_F7,          ID_ViewPrefs,           VIRTKEY, NOINVERT
    VK_F8,          ID_BuildOptions,        VIRTKEY, NOINVERT
    //VK_F9,          ID_BuildChanged,        VIRTKEY, NOINVERT
    VK_END,         ID_DropToFloor,          VIRTKEY, NOINVERT
    "W",            ID_SurfPopupSelectAdjacentWalls, VIRTKEY, SHIFT, NOINVERT
    "X",            ID_EditCut,             VIRTKEY, CONTROL, NOINVERT
    "X",            ID_SurfPopupXor,        VIRTKEY, SHIFT, NOINVERT
    "Y",            ID_EditRedo,            VIRTKEY, CONTROL, NOINVERT
    "Y",            ID_SurfPopupSelectAdjacentSlants, VIRTKEY, SHIFT, NOINVERT
    "Z",            ID_EditUndo,            VIRTKEY, CONTROL, NOINVERT
    "Z",            ID_EditSelectNone,      VIRTKEY, SHIFT, NOINVERT
END


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO
BEGIN
    IDDIALOG_NewObject, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 365
        TOPMARGIN, 7
        BOTTOMMARGIN, 224
    END

    IDDIALOG_2DShapeEditor_Extrude, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 138
        TOPMARGIN, 7
        BOTTOMMARGIN, 38
    END

    IDDIALOG_2DShapeEditor_Revolve, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 139
        TOPMARGIN, 7
        BOTTOMMARGIN, 48
    END

    IDPP_SP_FLAGS1, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 212
        TOPMARGIN, 7
        BOTTOMMARGIN, 118
    END

    IDDIALOG_IMPORT_SOUND, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 233
        TOPMARGIN, 7
        BOTTOMMARGIN, 75
    END

    IDDIALOG_IMPORT_TEXTURE, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 233
        TOPMARGIN, 7
        BOTTOMMARGIN, 115
    END

    IDDIALOG_TEX_PROP, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 440
        TOPMARGIN, 7
        BOTTOMMARGIN, 190
    END

    IDD_CreateDialog, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 200
        VERTGUIDE, 45
        VERTGUIDE, 139
        TOPMARGIN, 7
        BOTTOMMARGIN, 45
        HORZGUIDE, 7
        HORZGUIDE, 21
        HORZGUIDE, 34
        HORZGUIDE, 47
    END

    IDD_ImportDialog, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 200
        VERTGUIDE, 45
        VERTGUIDE, 139
        TOPMARGIN, 7
        BOTTOMMARGIN, 59
        HORZGUIDE, 7
        HORZGUIDE, 21
        HORZGUIDE, 34
        HORZGUIDE, 47
    END

    IDDIALOG_NEW_TEXTURE, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 233
        TOPMARGIN, 7
        BOTTOMMARGIN, 90
    END

    IDDIALOG_ADD_SPECIAL, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 205
        TOPMARGIN, 7
        BOTTOMMARGIN, 91
    END

    IDDIALOG_PROGRESS, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 288
        TOPMARGIN, 7
        BOTTOMMARGIN, 30
    END

    IDDIALOG_NEW_CLASS, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 233
        TOPMARGIN, 7
        BOTTOMMARGIN, 59
    END

    IDDIALOG_IMPORT_MUSIC, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 233
        TOPMARGIN, 7
        BOTTOMMARGIN, 64
    END

    IDDIALOG_FINDREPLACE, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 294
        TOPMARGIN, 7
        BOTTOMMARGIN, 98
    END

    IDDIALOG_IMPORT_BRUSH, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 233
        TOPMARGIN, 7
        BOTTOMMARGIN, 75
    END

    IDPP_BUILD_OPTIONS, DIALOG
    BEGIN
        LEFTMARGIN, 4
        RIGHTMARGIN, 217
        TOPMARGIN, 7
        BOTTOMMARGIN, 260
    END

    IDPP_BUILD_STATS, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 217
        TOPMARGIN, 7
        BOTTOMMARGIN, 258
    END

    IDDIALOG_SEARCH, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 326
        TOPMARGIN, 7
        BOTTOMMARGIN, 152
    END

    IDDIALOG_IMPORT_MAP, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 155
        TOPMARGIN, 7
        BOTTOMMARGIN, 38
    END

    IDDIALOG_SCALE_LIGHTS, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 183
        TOPMARGIN, 7
        BOTTOMMARGIN, 42
    END

    IDPP_SP_STATS, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 212
        TOPMARGIN, 7
        BOTTOMMARGIN, 118
    END

    IDDIALOG_VIEWPORT_CONFIG, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 212
        TOPMARGIN, 7
        BOTTOMMARGIN, 55
    END

    IDDIALOG_BRUSH_BUILDER, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 219
        TOPMARGIN, 7
        BOTTOMMARGIN, 150
    END

    IDDIALOG_2DShapeEditor_ExtrudeToPoint, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 138
        TOPMARGIN, 7
        BOTTOMMARGIN, 38
    END

    IDDIALOG_2DShapeEditor_ExtrudeToBevel, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 146
        TOPMARGIN, 7
        BOTTOMMARGIN, 49
    END

    IDPP_SP_ALIGNMENT, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 212
        TOPMARGIN, 7
        BOTTOMMARGIN, 118
    END

    IDDIALOG_GROUP, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 194
        TOPMARGIN, 7
        BOTTOMMARGIN, 37
    END

    IDDIALOG_RENAME, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 194
        TOPMARGIN, 7
        BOTTOMMARGIN, 37
    END

    IDDIALOG_2DSE_CUSTOM_DETAIL, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 124
        TOPMARGIN, 7
        BOTTOMMARGIN, 37
    END

    IDDIALOG_TEX_REPLACE, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 338
        TOPMARGIN, 7
        BOTTOMMARGIN, 176
    END

    IDPP_MAT_GENERAL, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 287
        TOPMARGIN, 7
        BOTTOMMARGIN, 102
    END

    IDD_TWENRATE, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 55
        TOPMARGIN, 7
        BOTTOMMARGIN, 194
    END
END
#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 1,0,0,1
 PRODUCTVERSION 1,0,0,1
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x40004L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", " "
            VALUE "FileDescription", "UnrealEd"
            VALUE "FileVersion", "1, 0, 0, 1"
            VALUE "InternalName", "UnrealEd"
            VALUE "LegalCopyright", "Copyright � 2002"
            VALUE "OriginalFilename", "UnrealEd.exe"
            VALUE "ProductName", "  UnrealEd"
            VALUE "ProductVersion", "1, 0, 0, 1"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END


/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE
BEGIN
    IDS_WARNING             "Warning"
    IDS_ERROR               "Error"
    IDS_WARNING_NO_TRIANGLES "Warning: No triangles in selection."
    IDS_WARNING_EXCEEDING_20_TRIANGLES 
                            "Warning: Exporting more than 20 triangles. UnrealEd may crash."
END

STRINGTABLE
BEGIN
    ID_INDICATOR_VERTS      "Vert: _000_"
    ID_INDICATOR_VERTS_FMT  "Vert: %d"
    ID_INDICATOR_TRIS       "Tri: _000_"
    ID_INDICATOR_TRIS_FMT   "Tri: %d"
END

STRINGTABLE
BEGIN
    ID_FILE_EXPORT_SELECTION 
                            "Export the selected shape as a U2D file\nExport Selection"
    ID_VIEW_ZOOM_IN         "Enlarge the view by a factor of 2\nZoom In"
    ID_VIEW_ZOOM_OUT        "Shrink the view by a factor of 2\nZoom Out"
    ID_VIEW_REFRESH         "Redraw the view\nRefresh View"
    ID_VIEW_RESET           "Reset the viewport size and origin\nReset View"
    ID_VIEW_IMAGE_OPEN      "Open a backdrop image\nOpen Backdrop Image"
    ID_VIEW_IMAGE_CLEAR     "Remove the backdrop image\nClear Backdrop Image"
    ID_VIEW_IMAGE_SIZE      "Change the image's size and position\nImage Size and Position"
    ID_VIEW_GRID            "Edit grid settings\nGrid Settings"
END

STRINGTABLE
BEGIN
    ID_SHAPE_TOOLS_SELECT   "Select\nSelect"
    ID_SHAPE_TOOLS_VERTEX_EDIT "Vertex Edit\nVertex Edit"
    ID_SHAPE_TOOLS_SPLIT    "Split\nSplit"
    ID_SHAPE_TOOLS_MERGE    "Merge\nMerge"
    ID_SHAPE_TOOLS_POLYGON  "Polygon\nPolygon"
    ID_SHAPE_TOOLS_SQUARE   "Square\nSquare"
    ID_SHAPE_TOOLS_RECTANGLE "Rectangle\nRectangle"
    ID_SHAPE_TOOLS_CIRCLE   "Circle\nCircle"
    ID_SHAPE_TOOLS_ELLIPSE  "Ellipse\nEllipse"
    ID_SEGMENT_INSERT       "Insert Segment\nInsert Segment"
    ID_SEGMENT_REMOVE       "Remove Segment\nRemove Segment"
    ID_SEGMENT_LINEAR       "Linear Segment\nLinear Segment"
    ID_SEGMENT_BEZIER       "B�zier Segment\nB�zier Segment"
    ID_SHAPE_SCALE          "Scale the selected shape\nScale Shape"
    ID_SHAPE_ROTATE         "Rotate the selected shape\nRotate Shape"
    ID_SHAPE_MIRROR         "Mirror the selected shape\nMirror Shape"
END

STRINGTABLE
BEGIN
    ID_SHAPE_DISPLACE       "Set the shape origin to (0,0)\nDisplace Shape"
    ID_SHAPE_RECENTER       "Move the shape to (0,0)\nRecenter Shape"
    ID_SHAPE_FLIP_ORDER     "Toggle the boundary vertex ordering (clockwise/counterclockwise)\nFlip Vertex Order"
    ID_EXTRAS_SHOW_WARNINGS "Enable or disable warning messages\nShow Warnings"
END

STRINGTABLE
BEGIN
    IDS_TOOL_SELECT         "Select: (CLICK/DRAG-L) select/move shapes, (CLICK-R) context menu"
    IDS_TOOL_VERTEX_EDIT    "Vertex Edit: (CLICK/DRAG-L) select/move vertices, (CLICK-R) context menu"
    IDS_TOOL_SPLIT_1        "Split: (CLICK-L) select shape"
    IDS_TOOL_SPLIT_2        "Split: (DRAG-L) draw split line between vertices"
    IDS_TOOL_MERGE_1        "Merge: (CLICK-L): select first shape"
    IDS_TOOL_MERGE_2        "Merge: (CLICK-L) select second shape"
    IDS_TOOL_MERGE_3        "Merge: (DRAG-L) draw connecting edge between vertices"
    IDS_TOOL_POLYGON        "Polygon: (CLICK-L) insert vertex/select segment"
    IDS_TOOL_SHAPE          "Shape: (DRAG-L) add shape"
    IDS_TOOL_SQUARE         "Square: (DRAG-L) add square"
    IDS_TOOL_RECTANGLE      "Rectangle: (DRAG-L) add rectangle"
    IDS_TOOL_CIRCLE         "Circle: (DRAG-L) add circle"
    IDS_TOOL_ELLIPSE        "Ellipse: (DRAG-L) add ellipse"
END

STRINGTABLE
BEGIN
    IDS_TRIANGULATING       "Triangulating..."
END

STRINGTABLE
BEGIN
    IDS_EXPORT_FILTER       "2DS Files (*.2ds)|*.2ds|U2D Files (*.u2d)|*.u2d||"
    IDS_IMAGE_FILTER        "Bitmap Files (*.bmp)|*.bmp|All Files (*.*)|*.*||"
END

STRINGTABLE
BEGIN
    ID_EDIT_CLONE           "Duplicate selected objects\nClone"
END

#endif    // English (United States) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//


/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

