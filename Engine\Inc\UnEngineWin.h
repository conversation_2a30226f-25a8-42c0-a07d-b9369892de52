/*=============================================================================
	UnEngineWin.h: Unreal engine windows-specific code.
	Copyright 1997-1999 Epic Games, Inc. All Rights Reserved.

Revision history:
	* Created by <PERSON>.
=============================================================================*/

DISABLE_OPTIMIZATION /* Avoid VC++ code generation bug */

#include "UnStat.h"

#if UNDYING_BINK
#include "..\Bink\bink.h"
#endif

/*-----------------------------------------------------------------------------
	Splash screen.
-----------------------------------------------------------------------------*/

//
// Splash screen, implemented with old-style Windows code so that it
// can be opened super-fast before initialization.
//
HWND    hWndSplash = NULL;
HBITMAP hBitmap = NULL;
INT     BitmapX = 0;
INT     BitmapY = 0;
DWORD   ThreadId = 0;
HANDLE  hThread = 0;

INT_PTR CALLBACK SplashDialogProc( HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam )
{
	if (uMsg == WM_DESTROY)
	{
		PostQuitMessage(0);
		ThreadId = 0;
	}
	return 0;
}
DWORD WINAPI ThreadProc( VOID* Parm )
{
	hWndSplash = TCHAR_CALL_OS(CreateDialogW(hInstance,MAKEINTRESOURCEW(IDDIALOG_Splash), NULL, SplashDialogProc),CreateDialogA(hInstance, MAKEINTRESOURCEA(IDDIALOG_Splash), NULL, SplashDialogProc) );
	if( hWndSplash )
	{
		HWND hWndLogo = GetDlgItem(hWndSplash,IDC_Logo);
		if( hWndLogo )
		{
			SetWindowPos(hWndSplash,HWND_TOPMOST,(GetSystemMetrics(SM_CXSCREEN)-BitmapX)/2,(GetSystemMetrics(SM_CYSCREEN)-BitmapY)/2,BitmapX,BitmapY,SWP_SHOWWINDOW);
			SetWindowPos(hWndSplash,HWND_NOTOPMOST,0,0,0,0,SWP_NOMOVE|SWP_NOSIZE);
			SendMessageX( hWndLogo, STM_SETIMAGE, IMAGE_BITMAP, (LPARAM)hBitmap );
			UpdateWindow( hWndSplash );
			MSG Msg;
			while( TCHAR_CALL_OS(GetMessageW(&Msg,NULL,0,0),GetMessageA(&Msg,NULL,0,0)) )
				DispatchMessageX(&Msg);
		}
	}
	return 0;
}
void InitSplash( const TCHAR* Filename, bool bCreateThread = true )
{
	FWindowsBitmap Bitmap(1);
	if( Filename && GFileManager->FileSize(Filename) > 0 )
	{
		verify(Bitmap.LoadFile(Filename) );
		hBitmap = Bitmap.GetBitmapHandle();
		BitmapX = Bitmap.SizeX;
		BitmapY = Bitmap.SizeY;
	}
	if (bCreateThread)
		hThread=CreateThread(NULL,0,&ThreadProc,NULL,0,&ThreadId);
}
void ExitSplash()
{
	if (ThreadId)
	{
		while (!PostThreadMessage(ThreadId, WM_QUIT, 0, 0))
			Sleep(100);
		ThreadId = 0;
	}
}

#if UNDYING_BINK
static HWND hWndBink;
static HBINK gBinkOpened;
static HBINKBUFFER gBinkBuffer;
DWORD   ThreadIdBink = 0;
HANDLE  hThreadBink = 0;
bool bShowSplashAfterBink = true;

// temp
HINSTANCE hInInstanceBink;
HINSTANCE hPrevInstanceBink;
HWND hWndParentBink;

DWORD WINAPI BinkProc(HWND hWnd, UINT Msg, unsigned __int16 wParam, LPARAM lParam)
{
	HDC v7;
	tagPAINTSTRUCT Paint;

	switch (Msg)
	{
#if ADDITIONS_IMPROVEMENTS
	case WM_LBUTTONDOWN:
		// skip intro
		DestroyWindow(hWndBink);
		return 0;
	case WM_KEYDOWN:
		if (wParam == VK_ESCAPE)
		{
			// skip intro
			DestroyWindow(hWndBink);
			return 0;
		}
		break;
#endif
	case WM_DESTROY:
		PostQuitMessage(0);
		ThreadIdBink = 0;
		return 0;
	case WM_SETFOCUS:
		BinkPause(gBinkOpened, 0);
		return DefWindowProcA(hWnd, Msg, wParam, lParam);
	case WM_KILLFOCUS:
		BinkPause(gBinkOpened, 1);
		return DefWindowProcA(hWnd, Msg, wParam, lParam);
	case WM_PAINT:
		v7 = BeginPaint(hWnd, &Paint);
		PatBlt(v7, 0, 0, 4096, 4096, 0x42u);
		if (gBinkBuffer)
			BinkBufferBlit(gBinkBuffer, 0, 1);
		EndPaint(hWnd, &Paint);
		return 0;
	case WM_ERASEBKGND:
		return 1;
	case WM_WINDOWPOSCHANGING:
		tagWINDOWPOS* wp = reinterpret_cast<tagWINDOWPOS*>(lParam);
		if ((wp->flags & 2) == 0 && gBinkBuffer)
		{
			int x = wp->x;
			int y = wp->y;
			BinkBufferCheckWinPos(gBinkBuffer, (signed long*)&x, (signed long*)&y);
			wp->x = x;
			wp->y = y;
		}
		break;
	}
	return DefWindowProcA(hWnd, Msg, wParam, lParam);
}

void BinkLoop()
{
	tagMSG Msg;
	int Rects;
	while (true)
	{
		while (!PeekMessageA(&Msg, 0, 0, 0, 1))
		{
			if (BinkWait(gBinkOpened))
			{
				Sleep(1);
			}
			else
			{
				BinkDoFrame(gBinkOpened);
				if (BinkBufferLock(gBinkBuffer))
				{
					BinkCopyToBuffer(
						gBinkOpened,
						gBinkBuffer->Buffer,
						gBinkBuffer->BufferPitch,
						gBinkBuffer->Height,
						0,
						0,
						gBinkBuffer->SurfaceType);
					BinkBufferUnlock(gBinkBuffer);
				}
				Rects = BinkGetRects(gBinkOpened, gBinkBuffer->SurfaceType);
				BinkBufferBlit(gBinkBuffer, gBinkOpened->FrameRects, Rects);
				if (gBinkOpened->FrameNum == gBinkOpened->Frames)
				{
					SleepEx(2500, 0);
					DestroyWindow(hWndBink);
				}
				else
				{
					BinkNextFrame(gBinkOpened);
				}
			}
		}
		if (Msg.message == WM_QUIT)
			break;
		TranslateMessage(&Msg);
		DispatchMessageA(&Msg);
	}

	// cleanup
	if (gBinkOpened)
	{
		BinkClose(gBinkOpened);
		gBinkOpened = 0;
	}
	if (gBinkBuffer)
	{
		BinkBufferClose(gBinkBuffer);
		gBinkBuffer = 0;
	}

	if (bShowSplashAfterBink)
		InitSplash(NULL);
}

int _InitBink(const TCHAR* Filename)
{
	const char* WindowName = "EA Games";

	if (!hPrevInstanceBink)
	{
		WNDCLASSA WndClass{};
		WndClass.style = 0;
		WndClass.lpfnWndProc = (WNDPROC)BinkProc;
		WndClass.cbClsExtra = 0;
		WndClass.cbWndExtra = 0;
		WndClass.hInstance = hInstance;
		//memset(&WndClass.hIcon, 0, 16);
		WndClass.lpszClassName = WindowName;

		// added, removes cursor
		BYTE CursorMaskAND[] = { 0xFF };
		BYTE CursorMaskXOR[] = { 0x00 };
		WndClass.hCursor = CreateCursor(NULL, 0, 0, 1, 1, CursorMaskAND, CursorMaskXOR);

		if (!RegisterClassA(&WndClass))
			return 1;
	}

	hWndBink = CreateWindowExA(0, WindowName, WindowName, WS_POPUP, 32, 32, 32, 32, hWndParentBink, 0, hInstance, 0);
	if (!hWndBink)
	{
		MessageBoxA(0, "Unable to create Intro Window", "Windows", MB_ICONSTOP);
		return 1;
	}

	// crashes with custom dsound
	//BinkSoundUseDirectSound(0);
	BinkSoundUseWaveOut(0);

	gBinkOpened = BinkOpen(appToAnsi(Filename), 0);
	if (!gBinkOpened)
	{
		MessageBoxA(0, BinkBufferGetError(), "Bink Error", MB_ICONSTOP);
		DestroyWindow(hWndBink);
		return 1;
	}

#if 0
	// if the cursor is on the second (left) monitor (x < 0), we crash
	// move it to the main monitor and restore it after the call
	POINT OldCursorPos;
	GetCursorPos(&OldCursorPos);
	bool MoveCursor = OldCursorPos.x < 0;
	if (MoveCursor)
	{
		RECT CursorClip = { 0,0, 16, 16 };
		ClipCursor(&CursorClip);
	}
	gBinkBuffer = BinkBufferOpen(hWndBink, gBinkOpened->Width, gBinkOpened->Height, 0);
	if (MoveCursor)
	{
		ClipCursor(NULL);
		SetCursorPos(OldCursorPos.x, OldCursorPos.y);
	}
#endif

	// just use BINKBUFFERDIBSECTION to fix cursor issue
	// this also gives us fullscreen playback
	gBinkBuffer = BinkBufferOpen(hWndBink, gBinkOpened->Width, gBinkOpened->Height, BINKBUFFERDIBSECTION);

	if (!gBinkBuffer)
	{
		ShowWindow(hWndBink, 0);
		MessageBoxA(0, BinkBufferGetError(), "Bink Error", MB_ICONSTOP);
		DestroyWindow(hWndBink);
		BinkClose(gBinkOpened);
		return 1;
	}

	int ScreenWidth = GetSystemMetrics(SM_CXSCREEN);
	int ScreenHeight = GetSystemMetrics(SM_CYSCREEN);

	SetWindowPos(hWndBink, 0, 0, 0, ScreenWidth, ScreenHeight, SWP_SHOWWINDOW);
	ShowWindow(hWndBink, 1);
	BinkBufferSetOffset(
		gBinkBuffer,
		ScreenWidth  / 2 - (gBinkBuffer->WindowWidth  / 2),
		ScreenHeight / 2 - (gBinkBuffer->WindowHeight / 2));

	BinkLoop();

	return 0;
}

DWORD WINAPI InitBinkThread(void* Filename)
{
	_InitBink((const TCHAR*)Filename);
	return 0;
}

int InitBink(HINSTANCE hInInstance, HINSTANCE hPrevInstance, HWND hWndParent, const TCHAR* Filename,bool bNewThread)
{
	hInInstanceBink = hInInstance;
	hPrevInstanceBink = hPrevInstance;
	hWndParentBink = hWndParent; // always 0? remove

	if (bNewThread)
	{
		hThreadBink = CreateThread(NULL, 0, &InitBinkThread, (void*)Filename, 0, &ThreadIdBink);
		// prevents laggy video
		//SetThreadPriority(hThreadBink, THREAD_PRIORITY_ABOVE_NORMAL);
		return 0;
	}
	else
	{
		// splash screen will already show after it
		bShowSplashAfterBink = false;
		return _InitBink(Filename);
	}
}

void ExitBink()
{
	if (ThreadIdBink)
	{
		while (!PostThreadMessage(ThreadIdBink, WM_QUIT, 0, 0))
			Sleep(100);
		ThreadIdBink = 0;
	}
}
#endif

/*-----------------------------------------------------------------------------
	System Directories.
-----------------------------------------------------------------------------*/

TCHAR SysDir[256]=TEXT(""), WinDir[256]=TEXT(""), ThisFile[256]=TEXT("");
void InitSysDirs()
{
#if UNICODE
	if( !GUnicodeOS )
	{
		ANSICHAR ASysDir[256]="", AWinDir[256]="", AThisFile[256]="";
		GetSystemDirectoryA( ASysDir, ARRAY_COUNT(ASysDir) );
		GetWindowsDirectoryA( AWinDir, ARRAY_COUNT(AWinDir) );
		GetModuleFileNameA( NULL, AThisFile, ARRAY_COUNT(AThisFile) );
		appStrcpy( SysDir, ANSI_TO_TCHAR(ASysDir) );
		appStrcpy( WinDir, ANSI_TO_TCHAR(AWinDir) );
		appStrcpy( ThisFile, ANSI_TO_TCHAR(AThisFile) );
	}
	else
#endif
	{
		GetSystemDirectory( SysDir, ARRAY_COUNT(SysDir) );
		GetWindowsDirectory( WinDir, ARRAY_COUNT(WinDir) );
		GetModuleFileName( NULL, ThisFile, ARRAY_COUNT(ThisFile) );
	}
	if( !appStricmp( &ThisFile[appStrlen(ThisFile) - 4], TEXT(".ICD") ) )
		appStrcpy( &ThisFile[appStrlen(ThisFile) - 4], TEXT(".EXE") );
}

/*-----------------------------------------------------------------------------
	Config wizard.
-----------------------------------------------------------------------------*/

class WConfigWizard : public WWizardDialog
{
	DECLARE_WINDOWCLASS(WConfigWizard,WWizardDialog,Startup)
	WLabel LogoStatic;
	FWindowsBitmap LogoBitmap;
	UBOOL Cancel;
	FString Title;
	WConfigWizard()
	: LogoStatic(this,IDC_Logo)
	, Cancel(0)
	{
		InitSysDirs();
	}
	void OnInitDialog()
	{
		guard(WStartupWizard::OnInitDialog);
		WWizardDialog::OnInitDialog();
		SendMessageX( *this, WM_SETICON, ICON_BIG, (WPARAM)LoadIconIdX(hInstance,IDICON_Mainframe) );
		LogoBitmap.LoadFile( TEXT("..\\Help\\Logo.bmp") );
		SendMessageX( LogoStatic, STM_SETIMAGE, IMAGE_BITMAP, (LPARAM)LogoBitmap.GetBitmapHandle() );
		SetText( *Title );
		SetForegroundWindow( hWnd );
		unguard;
	}
};

class WConfigPageFirstTime : public WWizardPage
{
	DECLARE_WINDOWCLASS(WConfigPageFirstTime,WWizardPage,Startup)
	WConfigWizard* Owner;
	WConfigPageFirstTime( WConfigWizard* InOwner )
	: WWizardPage( TEXT("ConfigPageFirstTime"), IDDIALOG_ConfigPageFirstTime, InOwner )
	, Owner(InOwner)
	{}
	const TCHAR* GetNextText()
	{
		return LocalizeGeneral(TEXT("Run"),TEXT("Startup"));
	}
	WWizardPage* GetNext()
	{
		FString Driver = GConfig->GetRenderDevice();

		if( Driver!=TEXT("") )
			GConfig->SetString( TEXT("Engine.Engine"), TEXT("GameRenderDevice"), *Driver );

		if( Driver==TEXT("SoftDrv.SoftwareRenderDevice") )
		{
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("WindowedViewportX"),  TEXT("640") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("WindowedViewportY"),  TEXT("480") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("WindowedColorBits"),  TEXT("16") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("FullscreenViewportX"), TEXT("640") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("FullscreenViewportY"), TEXT("480") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("FullscreenColorBits"), TEXT("16") );

			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("ActorShadows"),  TEXT("False") );
		}

		Owner->EndDialog(1);
		return NULL;
	}	
};

class WConfigPageSafeOptions : public WWizardPage
{
	DECLARE_WINDOWCLASS(WConfigPageSafeOptions,WWizardPage,Startup)
	WConfigWizard* Owner;
	WButton NoSoundButton, No3DSoundButton, No3DVideoButton, WindowButton, ResButton, ResetConfigButton, NoProcessorButton, NoJoyButton;
	WConfigPageSafeOptions( WConfigWizard* InOwner )
	: WWizardPage		( TEXT("ConfigPageSafeOptions"), IDDIALOG_ConfigPageSafeOptions, InOwner )
	, Owner				(InOwner)
	, NoSoundButton		(this,IDC_NoSound)
	, No3DSoundButton	(this,IDC_No3DSound)
	, No3DVideoButton	(this,IDC_No3dVideo)
	, WindowButton		(this,IDC_Window)
	, ResButton			(this,IDC_Res)
	, ResetConfigButton	(this,IDC_ResetConfig)
	, NoProcessorButton	(this,IDC_NoProcessor)
	, NoJoyButton		(this,IDC_NoJoy) // crashed undying safe mode options
	{}
	void OnInitDialog()
	{
		WWizardPage::OnInitDialog();
		SendMessageX( NoSoundButton,     BM_SETCHECK, 1, 0 );
		SendMessageX( No3DSoundButton,   BM_SETCHECK, 1, 0 );
		SendMessageX( No3DVideoButton,   BM_SETCHECK, 1, 0 );
		SendMessageX( WindowButton,      BM_SETCHECK, 1, 0 );
		SendMessageX( ResButton,         BM_SETCHECK, 1, 0 );
		SendMessageX( ResetConfigButton, BM_SETCHECK, 0, 0 );
		SendMessageX( NoProcessorButton, BM_SETCHECK, 1, 0 );
		SendMessageX( NoJoyButton,       BM_SETCHECK, 1, 0 );
	}
	const TCHAR* GetNextText()
	{
		return LocalizeGeneral(TEXT("Run"),TEXT("Startup"));
	}
	WWizardPage* GetNext()
	{
		FString CmdLine;
		if( SendMessageX(NoSoundButton,BM_GETCHECK,0,0)==BST_CHECKED )
			CmdLine+=TEXT(" -nosound");
		if( SendMessageX(No3DSoundButton,BM_GETCHECK,0,0)==BST_CHECKED )
			CmdLine+=TEXT(" -no3dsound");
		if( SendMessageX(No3DSoundButton,BM_GETCHECK,0,0)==BST_CHECKED )
			CmdLine+=TEXT(" -nohard");
		if( SendMessageX(No3DSoundButton,BM_GETCHECK,0,0)==BST_CHECKED )
			CmdLine+=TEXT(" -nohard -noddraw");
		if( SendMessageX(No3DSoundButton,BM_GETCHECK,0,0)==BST_CHECKED )
			CmdLine+=TEXT(" -defaultres");
		if( SendMessageX(NoProcessorButton,BM_GETCHECK,0,0)==BST_CHECKED )
			CmdLine+=TEXT(" -nommx -nokni -nok6");
		if( SendMessageX(NoJoyButton,BM_GETCHECK,0,0)==BST_CHECKED )
			CmdLine+=TEXT(" -nojoy");
		if( SendMessageX(ResetConfigButton,BM_GETCHECK,0,0)==BST_CHECKED )
		{
			debugf(TEXT("attempting to delete file %s."), TEXT("System.ini"));
			GFileManager->Delete(TEXT("System.ini"));
			GFileManager->Delete(TEXT("User.ini"));
			CmdLine+=TEXT(" -FirstRun -reset");
		}
		ShellExecuteX( NULL, TEXT("open"), ThisFile, *CmdLine, appBaseDir(), SW_SHOWNORMAL );
		Owner->EndDialog(0);
		return NULL;
	}
};

class WConfigPageDetail : public WWizardPage
{
	DECLARE_WINDOWCLASS(WConfigPageDetail,WWizardPage,Startup)
	WConfigWizard* Owner;
	WEdit DetailEdit;
	WConfigPageDetail( WConfigWizard* InOwner )
	: WWizardPage( TEXT("ConfigPageDetail"), IDDIALOG_ConfigPageDetail, InOwner )
	, Owner(InOwner)
	, DetailEdit(this,IDC_DetailEdit)
	{}
	void OnInitDialog()
	{
		WWizardPage::OnInitDialog();
		FString Info;

		INT DescFlags=0;
		FString Driver = GConfig->GetRenderDevice();
		GConfig->GetInt(*Driver,TEXT("DescFlags"),DescFlags);

		// Resolution and frame rate dependent LOD.
		FLOAT CpuMHz = 0.000001 / GSecondsPerCycle;
		if( CpuMHz >= 1000.0 )
		{
			Info = Info + LocalizeGeneral(TEXT("ResHigh"),TEXT("Startup")) + TEXT("\r\n");
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("WindowedViewportX"),  TEXT("1024") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("WindowedViewportY"),  TEXT("768") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("WindowedColorBits"),  TEXT("32") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("FullscreenViewportX"), TEXT("1024") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("FullscreenViewportY"), TEXT("768") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("FullscreenColorBits"), TEXT("32") );
		}
		else if( CpuMHz >= 480.0 )
		{
			Info = Info + LocalizeGeneral(TEXT("ResMed"),TEXT("Startup")) + TEXT("\r\n");
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("WindowedViewportX"),  TEXT("800") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("WindowedViewportY"),  TEXT("600") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("WindowedColorBits"),  TEXT("16") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("FullscreenViewportX"), TEXT("800") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("FullscreenViewportY"), TEXT("600") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("FullscreenColorBits"), TEXT("16") );
		}
		else
		{
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("MinQuality"),  TEXT("0.35") );	
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("MinDesiredFrameRate"),  TEXT("20") );

			Info = Info + LocalizeGeneral(TEXT("ResLow"),TEXT("Startup")) + TEXT("\r\n");
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("WindowedViewportX"),  TEXT("640") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("WindowedViewportY"),  TEXT("480") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("WindowedColorBits"),  TEXT("16") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("FullscreenViewportX"), TEXT("640") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("FullscreenViewportY"), TEXT("480") );
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("FullscreenColorBits"), TEXT("16") );

			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("ActorShadows"),  TEXT("False") );
		}

		// Sound quality.
		Info = Info + LocalizeGeneral(TEXT("SoundHigh"),TEXT("Startup")) + TEXT("\r\n");

		// Skins.
		if( (GPhysicalMemory < 96*1024*1024) || (DescFlags&RDDESCF_LowDetailSkins) )
		{
			Info = Info + LocalizeGeneral(TEXT("SkinsLow"),TEXT("Startup")) + TEXT("\r\n");
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("SkinDetail"), TEXT("Medium") );
		}
		else
		{
			Info = Info + LocalizeGeneral(TEXT("SkinsHigh"),TEXT("Startup")) + TEXT("\r\n");
		}

		// World.
		if( (GPhysicalMemory < 64*1024*1024) || (DescFlags&RDDESCF_LowDetailWorld) )
		{
			Info = Info + LocalizeGeneral(TEXT("WorldLow"),TEXT("Startup")) + TEXT("\r\n");
			GConfig->SetString( TEXT("WinDrv.WindowsClient"), TEXT("TextureDetail"), TEXT("Medium") );
		}
		else
		{
			Info = Info + LocalizeGeneral(TEXT("WorldHigh"),TEXT("Startup")) + TEXT("\r\n");
		}

		// should be WinDrv.WindowsClient, but we don't want this anyways
		//GConfig->SetString( TEXT("WinDrv.CurvedSurfaces"), TEXT("CurvedSurfaces"), TEXT("False") );

		DetailEdit.SetText(*Info);
	}
	WWizardPage* GetNext()
	{
		return new WConfigPageFirstTime(Owner);
	}
};

class WConfigPageDriver : public WWizardPage
{
	DECLARE_WINDOWCLASS(WConfigPageDriver,WWizardPage,Startup)
	WConfigWizard* Owner;
	//WUrlButton WebButton;
	WLabel Card;
	WConfigPageDriver( WConfigWizard* InOwner )
	: WWizardPage( TEXT("ConfigPageDriver"), IDDIALOG_ConfigPageDriver, InOwner )
	, Owner(InOwner)
	//, WebButton(this,LocalizeGeneral(TEXT("Direct3DWebPage"),TEXT("Startup")),IDC_WebButton)
	, Card(this,IDC_Card)
	{}
	void OnInitDialog()
	{
		WWizardPage::OnInitDialog();
		FString CardName=GConfig->GetStr(TEXT("D3DDrv.D3DRenderDevice"),TEXT("Description"));
		if( CardName!=TEXT("") )
			Card.SetText(*CardName);
	}
	WWizardPage* GetNext()
	{
		return new WConfigPageDetail(Owner);
	}	
};

class WConfigPageRenderer : public WWizardPage
{
	DECLARE_WINDOWCLASS(WConfigPageRenderer,WWizardPage,Startup)
	WConfigWizard* Owner;
	WListBox RenderList;
	WButton ShowCompatible, ShowAll;
	WLabel RenderNote;
	INT First;
	TArray<FRegistryObjectInfo> Classes;
	WConfigPageRenderer( WConfigWizard* InOwner )
	: WWizardPage( TEXT("ConfigPageRenderer"), IDDIALOG_ConfigPageRenderer, InOwner )
	, Owner(InOwner)
	, RenderList(this,IDC_RenderList)
	, ShowCompatible(this,IDC_Compatible,FDelegate(this,(TDelegate)&WConfigPageRenderer::RefreshList))
	, ShowAll(this,IDC_All,FDelegate(this,(TDelegate)&WConfigPageRenderer::RefreshList))
	, RenderNote(this,IDC_RenderNote)
	, First(0)
	{}
	void RefreshList()
	{
		RenderList.Empty();
		INT All=(SendMessageX(ShowAll,BM_GETCHECK,0,0)==BST_CHECKED), BestPriority=0;
		FString Default;
		Classes.Empty();
		UObject::GetRegistryObjects( Classes, UClass::StaticClass(), URenderDevice::StaticClass(), 0 );
		for( TArray<FRegistryObjectInfo>::TIterator It(Classes); It; ++It )
		{
			FString Path=It->Object, Left, Right, Temp;
			if( Path.Split(TEXT("."),&Left,&Right) )
			{
				INT DoShow=All, Priority=0;
				INT DescFlags=0;
				GConfig->GetInt(*Path,TEXT("DescFlags"),DescFlags);
				if
				(	It->Autodetect!=TEXT("")
				&& (GFileManager->FileSize(*FString::Printf(TEXT("%s\\%s"), SysDir, *It->Autodetect))>=0
				||  GFileManager->FileSize(*FString::Printf(TEXT("%s\\%s"), WinDir, *It->Autodetect))>=0) )
					DoShow = Priority = 3;
				else if( DescFlags & RDDESCF_Certified )
					DoShow = Priority = 2;
				else if( Path==TEXT("SoftDrv.SoftwareRenderDevice") )
					DoShow = Priority = 1;
				if( DoShow )
				{
					RenderList.AddString( *(Temp=Localize(*Right,TEXT("ClassCaption"),*Left)) );
					if( Priority>=BestPriority )
						{Default=Temp; BestPriority=Priority;}
				}
			}
		}
		if( Default!=TEXT("") )
			RenderList.SetCurrent(RenderList.FindStringChecked(*Default),1);
		CurrentChange();
	}
	void CurrentChange()
	{
		RenderNote.SetText(Localize(TEXT("Descriptions"),*CurrentDriver(),TEXT("Startup"),NULL,1));
	}
	void OnPaint()
	{
		if( !First++ )
		{
			UpdateWindow( *this );
			GConfig->Flush( 1 );
			if( !ParseParam(appCmdLine(),TEXT("nodetect")) )
			{
				GFileManager->Delete(TEXT("Detected.ini"));
				ShellExecuteX( NULL, TEXT("open"), ThisFile, TEXT("testrendev=D3DDrv.D3DRenderDevice log=Detected.log"), appBaseDir(), SW_SHOWNORMAL );
				for( INT MSec=10000; MSec>0 && GFileManager->FileSize(TEXT("Detected.ini"))<0; MSec-=100 )
					Sleep(100);
			}
			RefreshList();
		}
	}
	void OnCurrent()
	{
		guard(WFilerPageInstallProgress::OnCurrent);
		unguard;
	}
	void OnInitDialog()
	{
		WWizardPage::OnInitDialog();
		SendMessageX(ShowCompatible,BM_SETCHECK,BST_CHECKED,0);
		RenderList.SelectionChangeDelegate = FDelegate(this,(TDelegate)&WConfigPageRenderer::CurrentChange);
		RenderList.DoubleClickDelegate = FDelegate(Owner,(TDelegate)&WWizardDialog::OnNext);
		RenderList.AddString( LocalizeGeneral(TEXT("Detecting"),TEXT("Startup")) );
	}
	FString CurrentDriver()
	{
		if( RenderList.GetCurrent()>=0 )
		{
			FString Name = RenderList.GetString(RenderList.GetCurrent());
			for( TArray<FRegistryObjectInfo>::TIterator It(Classes); It; ++It )
			{
				FString Path=It->Object, Left, Right, Temp;
				if( Path.Split(TEXT("."),&Left,&Right) )
					if( Name==Localize(*Right,TEXT("ClassCaption"),*Left) )
						return Path;
			}
		}
		return TEXT("");
	}
	WWizardPage* GetNext()
	{
		if( CurrentDriver()!=TEXT("") )
			GConfig->SetRenderDevice(*CurrentDriver());
		if( CurrentDriver()==TEXT("D3DDrv.D3DRenderDevice") )
			return new WConfigPageDriver(Owner);
		else
			return new WConfigPageDetail(Owner);
	}
};

class WConfigPageAudio : public WWizardPage
{
	DECLARE_WINDOWCLASS(WConfigPageAudio,WWizardPage,Startup)
	WConfigWizard* Owner;
	WListBox RenderList;
	WLabel RenderNote;
	INT First;
	TArray<FRegistryObjectInfo> Classes;
	WConfigPageAudio( WConfigWizard* InOwner )
	: WWizardPage( TEXT("ConfigPageAudio"), IDDIALOG_ConfigPageAudio, InOwner )
	, Owner(InOwner)
	, RenderList(this,IDC_RenderList)
	, RenderNote(this,IDC_AudioNote)
	, First(0)
	{}
	void RefreshList()
	{
		RenderList.Empty();
		INT BestPriority=0;
		FString Default;
		Classes.Empty();
		UObject::GetRegistryObjects( Classes, UClass::StaticClass(), UAudioSubsystem::StaticClass(), 0 );
		for( TArray<FRegistryObjectInfo>::TIterator It(Classes); It; ++It )
		{
			FString Path=It->Object, Left, Right, Temp;
			if( Path.Split(TEXT("."),&Left,&Right) )
			{
				INT DoShow=1, Priority=0;
				INT DescFlags=0;
				GConfig->GetInt(*Path,TEXT("DescFlags"),DescFlags);
				if
				(	It->Autodetect!=TEXT("")
				&& (GFileManager->FileSize(*FString::Printf(TEXT("%s\\%s"), SysDir, *It->Autodetect))>=0
				||  GFileManager->FileSize(*FString::Printf(TEXT("%s\\%s"), WinDir, *It->Autodetect))>=0) )
					DoShow = Priority = 3;
				else if( DescFlags & RDDESCF_Certified )
					DoShow = Priority = 2;
				else if( Path==TEXT("Galaxy.GalaxyAudioSubsystem") )
					DoShow = Priority = 1;
				if( DoShow )
				{
					RenderList.AddString( *(Temp=Localize(*Right,TEXT("ClassCaption"),*Left)) );
					if( Priority>=BestPriority )
						{Default=Temp; BestPriority=Priority;}
				}
			}
		}
		if( Default!=TEXT("") )
			RenderList.SetCurrent(RenderList.FindStringChecked(*Default),1);
		CurrentChange();
	}
	void CurrentChange()
	{
		RenderNote.SetText(Localize(TEXT("Descriptions"),*CurrentDriver(),TEXT("Startup"),NULL,1));
	}
	void OnPaint()
	{
		if( !First++ )
		{
			UpdateWindow( *this );
			GConfig->Flush( 1 );
			RefreshList();
		}
	}
	void OnCurrent()
	{
		guard(WFilerPageInstallProgress::OnCurrent);
		unguard;
	}
	void OnInitDialog()
	{
		WWizardPage::OnInitDialog();
		RenderList.SelectionChangeDelegate = FDelegate(this,(TDelegate)&WConfigPageAudio::CurrentChange);
		RenderList.DoubleClickDelegate = FDelegate(Owner,(TDelegate)&WWizardDialog::OnNext);
		RenderList.AddString( LocalizeGeneral(TEXT("Detecting"),TEXT("Startup")) );
	}
	FString CurrentDriver()
	{
		if( RenderList.GetCurrent()>=0 )
		{
			FString Name = RenderList.GetString(RenderList.GetCurrent());
			for( TArray<FRegistryObjectInfo>::TIterator It(Classes); It; ++It )
			{
				FString Path=It->Object, Left, Right, Temp;
				if( Path.Split(TEXT("."),&Left,&Right) )
					if( Name==Localize(*Right,TEXT("ClassCaption"),*Left) )
						return Path;
			}
		}
		return TEXT("");
	}
	WWizardPage* GetNext()
	{
		if( CurrentDriver()!=TEXT("") )
			GConfig->SetString(TEXT("Engine.Engine"),TEXT("AudioDevice"),*CurrentDriver());
		return new WConfigPageDetail(Owner);
	}
};

#if 0
class WFilerPageWelcome : public WWizardPage
{
	DECLARE_WINDOWCLASS(WFilerPageWelcome,WWizardPage,Setup)

	// Variables.
	WConfigWizard*		Owner;
	WLabel				WelcomePrompt;
	WLabel				LanguagePrompt;
	WListBox			LanguageList;
	TArray<FString>		LanguageNames;
	TArray<FString>		LanguageCodes;
	
	// Constructor.
	WFilerPageWelcome( WConfigWizard* InOwner )
		: WWizardPage   ( TEXT("FilerPageWelcome"), IDDIALOG_FilerPageWelcome, InOwner )
		, Owner			( InOwner )
		, WelcomePrompt ( this, IDC_WelcomePrompt )
		, LanguagePrompt( this, IDC_LanguagePrompt )
		, LanguageList  ( this, IDC_LanguageList )
	{
		guard(WFilerPageWelcome::WFilerPageWelcome);
		LanguageList.SelectionChangeDelegate = FDelegate(this,(TDelegate)&WFilerPageWelcome::OnUserChangeLanguage);
		unguard;
	}

	// WDialog interface.
	void OnCurrent()
	{
		guard(WFilerPageWelcome::OnSetFocus);
		//Owner->SetText( "SetupWindowTitle" );
		unguard;
	}
	void OnInitDialog()
	{
		guard(WFilerPageWelcome::OnInitDialog);
		WWizardPage::OnInitDialog();

		// Open product info window.
		//ProductInfo.OpenChildWindow( IDC_ProductInfoHolder, 1 );

		// Get keyboard layout info.
		INT UserLangId = GetUserDefaultLangID() & ((1<<10)-1);
		INT UserSubLangId = GetUserDefaultLangID() >> 10;
		debugf( NAME_Init, TEXT("Language %i, Sublanguage %i"), UserLangId, UserSubLangId );

		// Get language list.
		INT Ideal=-1, Best=-1, Current=0;
		TArray<FRegistryObjectInfo> Results;
		UObject::GetRegistryObjects( Results, UClass::StaticClass(), ULanguage::StaticClass(), 0 );
		if( Results.Num()==0 )
			appErrorf( TEXT("No Languages Found") );

		// Pick language matching keyboard layout if one exists, otherwise .int.
		for( INT i=0; i<Results.Num(); i++ )
		{
			TCHAR Name[256];
			INT LangId, SubLangId;
			FString Path = US + TEXT("Core.") + Results(i).Object;
			if( !GConfig->GetString( TEXT("Language"), TEXT("Language"), Name, ARRAY_COUNT(Name), *Path ) ||
				!GConfig->GetInt( TEXT("Language"), TEXT("LangId"), LangId, *Path ) ||
				!GConfig->GetInt( TEXT("Language"), TEXT("SubLangId"), SubLangId, *Path ) )
				continue;

			new(LanguageNames)FString( Name );
			new(LanguageCodes)FString( *Results(i).Object );
			LanguageList.AddString( Name );
			if( appStricmp(*Results(i).Object,TEXT("int"))==0 )
				Current = i;
			if( LangId==UserLangId )
				Best = i;
			if( LangId==UserLangId && SubLangId==UserSubLangId )
				Ideal = i;
		}
		if( Best>=0 )
			Current = Best;
		if( Ideal>=0 )
			Current = Ideal;
		LanguageList.SetCurrent( LanguageList.FindString(*LanguageNames(Current)), 1 );
		OnUserChangeLanguage();

		unguard;
	}

	// WWizardPage interface.
	WWizardPage* GetNext()
	{
		guard(WFilerPageWelcome::GetNext);
		return new WConfigPageDetail(Owner);
		unguard;
	}
	const TCHAR* GetBackText()
	{
		return NULL;
	}

	// WFilerPageWelcome interface.
	void OnUserChangeLanguage()
	{
		guard(WFilerPageWelcome::OnUserChangeLanguage);
		INT Index;
		if( LanguageNames.FindItem(*LanguageList.GetString(LanguageList.GetCurrent()),Index) )
		{
			FString Language = *LanguageCodes(Index);
			UObject::SetLanguage( *Language );
			GConfig->SetString( TEXT("Engine.Engine"), TEXT("Language"), *Language );
		}
		LanguageChange();//!!
		unguard;
	}
	virtual void LanguageChange()
	{
		guard(WFilerPageWelcome::LanguageChange);

		// Welcome text.
		WelcomePrompt.SetText( *FString::Printf( LineFormat(Localize("IDDIALOG_FilerPageWelcome","IDC_WelcomePrompt" )), ENGINE_VERSION, 1.0f ) );

		// Other text.
		Owner->SetText(LineFormat(Localize("IDDIALOG_WizardDialog", "IDC_WizardDialog" )));
		LanguagePrompt.SetText(LineFormat(Localize("IDDIALOG_FilerPageWelcome","IDC_LanguagePrompt")));
		Owner->RefreshPage();//!!

		unguard;
	}
};
#endif

class WConfigPageSafeMode : public WWizardPage
{
	DECLARE_WINDOWCLASS(WConfigPageSafeMode,WWizardPage,Startup)
#if UNDYING_COMPAT_EXE
	TCHAR Unk[256];
#endif
	WConfigWizard* Owner;
	WCoolButton RunButton, VideoButton, AudioButton, SafeModeButton, WebButton;
	WConfigPageSafeMode( WConfigWizard* InOwner )
	: WWizardPage    ( TEXT("ConfigPageSafeMode"), IDDIALOG_ConfigPageSafeMode, InOwner )
	, RunButton      ( this, IDC_Run,      FDelegate(this,(TDelegate)&WConfigPageSafeMode::OnRun) )
	, VideoButton    ( this, IDC_Video,    FDelegate(this,(TDelegate)&WConfigPageSafeMode::OnVideo) )
	, AudioButton    ( this, IDC_Audio,    FDelegate(this,(TDelegate)&WConfigPageSafeMode::OnAudio) )
	, SafeModeButton ( this, IDC_SafeMode, FDelegate(this,(TDelegate)&WConfigPageSafeMode::OnSafeMode) )
	, WebButton      ( this, IDC_Web,      FDelegate(this,(TDelegate)&WConfigPageSafeMode::OnWeb) )
	, Owner          (InOwner)
	{}
	void OnRun()
	{
		Owner->EndDialog(1);
	}
	void OnVideo()
	{
		Owner->Advance( new WConfigPageRenderer(Owner) );
	}
	void OnAudio()
	{
		Owner->Advance( new WConfigPageAudio(Owner) );
	}
	void OnSafeMode()
	{
		Owner->Advance( new WConfigPageSafeOptions(Owner) );
	}
	void OnWeb()
	{
		ShellExecuteX( *this, TEXT("open"), LocalizeGeneral(TEXT("WebPage"),TEXT("Startup")), TEXT(""), appBaseDir(), SW_SHOWNORMAL );
		Owner->EndDialog(0);
	}
	const TCHAR* GetNextText()
	{
		return NULL;
	}
};

/*-----------------------------------------------------------------------------
	Launch mplayer.com.
	- by Jack Porter
	- Based on mp_launch2.c by <NAME_EMAIL>
-----------------------------------------------------------------------------*/

#define MPI_FILE TEXT("mput.mpi")
#define MPLAYNOW_EXE TEXT("mplaynow.exe")

static int GetMplayerDirectory(TCHAR *mplayer_directory)
{
	HKEY hkey;
	HKEY key = HKEY_LOCAL_MACHINE;
	TCHAR subkey[]=TEXT("software\\mpath\\mplayer\\main");
	TCHAR valuename[]=TEXT("root directory");
	TCHAR buffer[MAX_PATH];
	DWORD dwType, dwSize;
	
	if( RegOpenKeyExX(key, subkey, 0, KEY_READ, &hkey) == ERROR_SUCCESS )
	{
		dwSize = MAX_PATH;
		if( RegQueryValueExX(hkey, valuename, 0, &dwType, (LPBYTE) buffer, &dwSize) == ERROR_SUCCESS )
		{
			appSprintf(mplayer_directory, TEXT("%s"), buffer);
			return 1;
		}
		RegCloseKey(hkey);
	}

	return 0;
}

static void LaunchMplayer()
{
	TCHAR mplaunch_exe[MAX_PATH], mplayer_directory[MAX_PATH];

	if( GetMplayerDirectory(mplayer_directory) )
	{
		appSprintf( mplaunch_exe, TEXT("%s\\programs\\mplaunch.exe"), mplayer_directory );
		if( GFileManager->FileSize(mplaunch_exe)>0 )
		{
			appLaunchURL( mplaunch_exe, MPI_FILE );
			return;
		}
	}

	appLaunchURL( MPLAYNOW_EXE, TEXT("") );
}

#undef MPI_FILE
#undef MPLAYNOW_EXE

/*-----------------------------------------------------------------------------
	Exec hook.
-----------------------------------------------------------------------------*/

// FExecHook.
class FExecHook : public FExec, public FNotifyHook
{
private:
	WConfigProperties* Preferences;
	void NotifyDestroy( void* Src )
	{
		if( Src==Preferences )
			Preferences = NULL;
	}
	UBOOL Exec( const TCHAR* Cmd, FOutputDevice& Ar )
	{
		guard(FExecHook::Exec);
		if( ParseCommand(&Cmd,TEXT("ShowLog")) )
		{
			if( GLogWindow )
			{
				GLogWindow->Show(1);
				SetFocus( *GLogWindow );
				GLogWindow->Display.ScrollCaret();
			}
			return 1;
		}
		else if( ParseCommand(&Cmd,TEXT("TakeFocus")) )
		{
			TObjectIterator<UEngine> EngineIt;
			if
			(	EngineIt
			&&	EngineIt->Client
			&&	EngineIt->Client->Viewports.Num() )
				SetForegroundWindow( (HWND)EngineIt->Client->Viewports(0)->GetWindow() );
			return 1;
		}
		else if( ParseCommand(&Cmd,TEXT("EditActor")) )
		{
#if 1 //Fix added by Legend on 4/12/2000
			UClass* Class;
			FName ActorName;
			TObjectIterator<UEngine> EngineIt;

			AActor* Found = NULL;

			if( EngineIt && ParseObject<UClass>( Cmd, TEXT("Class="), Class, ANY_PACKAGE ) )
			{
				AActor* Player  = EngineIt->Client ? EngineIt->Client->Viewports(0)->Actor : NULL;
				FLOAT   MinDist = 999999.0;
				for( TObjectIterator<AActor> It; It; ++It )
				{
					FLOAT Dist = Player ? FDist(It->Location,Player->Location) : 0.0f;
					if
					(	(!Player || It->GetLevel()==Player->GetLevel())
					&&	(!It->bDeleteMe)
					&&	(It->IsA( Class) )
					&&	(Dist<MinDist) )
					{
						MinDist = Dist;
						Found   = *It;
					}
				}
			}
			else if( EngineIt && Parse( Cmd, TEXT("Name="), ActorName ) )
			{
				// look for actor by name
				for( TObjectIterator<AActor> It; It; ++It )
				{
					if( !It->bDeleteMe && It->GetName() == *ActorName )
					{
						Found = *It;
						break;
					}
				}
			}

			if( Found )
			{
				WObjectProperties* P = new WObjectProperties( TEXT("EditActor"), 0, TEXT(""), NULL, 1 );
				P->OpenWindow( (HWND)EngineIt->Client->Viewports(0)->GetWindow() );
				P->Root.SetObjects( (UObject**)&Found, 1 );
				P->Show(1);
			}
			else Ar.Logf( TEXT("Bad or missing class or name") );
#else
			UClass* Class;
			TObjectIterator<UEngine> EngineIt;
			if( EngineIt && ParseObject<UClass>( Cmd, TEXT("Class="), Class, ANY_PACKAGE ) )
			{
				AActor* Player  = EngineIt->Client ? EngineIt->Client->Viewports(0)->Actor : NULL;
				AActor* Found   = NULL;
				FLOAT   MinDist = 999999.0;
				for( TObjectIterator<AActor> It; It; ++It )
				{
					FLOAT Dist = Player ? FDist(It->Location,Player->Location) : 0.0;
					if
					(	(!Player || It->GetLevel()==Player->GetLevel())
					&&	(!It->bDeleteMe)
					&&	(It->IsA( Class) )
					&&	(Dist<MinDist) )
					{
						MinDist = Dist;
						Found   = *It;
					}
				}
				if( Found )
				{
					WObjectProperties* P = new WObjectProperties( TEXT("EditActor"), 0, TEXT(""), NULL, 1 );
					P->OpenWindow( (HWND)EngineIt->Client->Viewports(0)->GetWindow() );
					P->Root.SetObjects( (UObject**)&Found, 1 );
					P->Show(1);
				}
				else Ar.Logf( TEXT("Actor not found") );
			}
			else Ar.Logf( TEXT("Missing class") );
#endif
			return 1;
		}
		else if( ParseCommand(&Cmd,TEXT("HideLog")) )
		{
			if( GLogWindow )
				GLogWindow->Show(0);
			return 1;
		}
		else if( ParseCommand(&Cmd,TEXT("Preferences")) && !GIsClient )
		{
			if( !Preferences )
			{
				Preferences = new WConfigProperties( TEXT("Preferences"), LocalizeGeneral("AdvancedOptionsTitle",TEXT("Window")) );
				Preferences->SetNotifyHook( this );
				Preferences->OpenWindow( GLogWindow ? GLogWindow->hWnd : NULL );
				Preferences->ForceRefresh();
			}
			Preferences->Show(1);
			SetFocus( *Preferences );
			return 1;
		}
		else if( ParseCommand(&Cmd,TEXT("MPLAYER")) && GIsClient )
		{
			LaunchMplayer();			
			return 1;			
		}
		else if( ParseCommand(&Cmd,TEXT("HEAT")) && GIsClient )
		{
			appLaunchURL( TEXT("GotoHEAT.exe"), TEXT("5193") );
			return 1;			
		}
		else if (ParseCommand(&Cmd, TEXT("replay")) && GIsClient)
		{
			TObjectIterator<UGameEngine> EngineIt;
			FReplay& Replay = EngineIt->Replay;

			FString ReplayFile(TEXT("test"));
			TCHAR Parm[4096] = TEXT("");

			// Start replay if specified.
			if (Replay.Replay(*ReplayFile, *GLog))
				// Retrieve the URL from it.
				appStrcpy(Parm, *Replay.GetURLStr());
			
			FURL DefaultURL(TEXT("Manor_FrontGate"));
			FURL URL(&DefaultURL, Parm, TRAVEL_Partial);

			return 1;
		}
		else return 0;
		unguard;
	}
public:
	FExecHook()
	: Preferences( NULL )
	{}
};

/*-----------------------------------------------------------------------------
	Startup and shutdown.
-----------------------------------------------------------------------------*/

//
// Initialize.
//
#ifndef _EDITOR_
static UEngine* InitEngine()
{
	guard(InitEngine);
	FTime LoadTime = appSeconds();

	// Set exec hook.
	static FExecHook GLocalHook;
	GExec = &GLocalHook;

	// Create mutex so installer knows we're running.
	FString MutexName = FString(GPackage) + TEXT("IsRunning");
	CreateMutexX( NULL, 0, *MutexName);
	UBOOL AlreadyRunning = (GetLastError()==ERROR_ALREADY_EXISTS);

	// Initialize the MRRM subsystem.
	UMRMActorData::InitSubsystem();

	// Reset to default config if requested.
	if( ParseParam(appCmdLine(),TEXT("reset")) )
	{
		debugf(TEXT("Reseting default config"));
		GFileManager->Delete(TEXT("System.ini"));
		GFileManager->Delete(TEXT("User.ini"));
	}

	// First-run menu.
	INT FirstRun=0;
	GConfig->GetInt( TEXT("FirstRun"), TEXT("FirstRun"), FirstRun );
	if( ParseParam(appCmdLine(),TEXT("FirstRun")) )
		FirstRun=0;
	if( FirstRun<220 )
	{
		// Migrate savegames.
		TArray<FString> Saves = GFileManager->FindFiles( TEXT("..\\Save\\*.usa"), 1, 0 );
		for( TArray<FString>::TIterator It(Saves); It; ++It )
		{
			INT Pos = appAtoi(**It+4);
			FString Section = TEXT("UnrealShare.UnrealSlotMenu");
			FString Key     = FString::Printf(TEXT("SlotNames[%i]"),Pos);
			if( appStricmp(GConfig->GetStr(*Section,*Key,TEXT("user")),TEXT(""))==0 )
				GConfig->SetString(*Section,*Key,TEXT("Saved game"),TEXT("user"));
		}
	}

	// Commandline (for mplayer/heat)
	FString Command;
	if( Parse(appCmdLine(),TEXT("consolecommand="), Command) )
	{
		debugf(TEXT("Executing console command %s"),*Command);
		GExec->Exec( *Command, *GLog );
		return NULL;
	}

	// Test render device.
	FString Device;
	if( Parse(appCmdLine(),TEXT("testrendev="),Device) )
	{
		debugf(TEXT("Detecting %s"),*Device);
		try
		{
			UClass* Cls = LoadClass<URenderDevice>( NULL, *Device, NULL, 0, NULL );
			GConfig->SetInt(*Device,TEXT("DescFlags"),RDDESCF_Incompatible);
			GConfig->Flush(0);
			if( Cls )
			{
				URenderDevice* RenDev = ConstructObject<URenderDevice>(Cls);
				if( RenDev )
				{
					if( RenDev->Init(NULL,0,0,0,0) )
					{
						debugf(TEXT("Successfully detected %s"),*Device);
					}
					else delete RenDev;
				}
			}
		} catch( ... ) {}
		FArchive* Ar = GFileManager->CreateFileWriter(TEXT("Detected.ini"),0);
		if( Ar )
			delete Ar;
		return NULL;
	}

	// Config UI.
	guard(ConfigUI);
	if( !GIsEditor && GIsClient )
	{
		WConfigWizard D;
		WWizardPage* Page = NULL;
		if( ParseParam(appCmdLine(),TEXT("safe")) || appStrfind(appCmdLine(),TEXT("readini")) )
			{Page = new WConfigPageSafeMode(&D); D.Title=LocalizeGeneral(TEXT("SafeMode"),TEXT("Startup"));}
		else if( FirstRun<ENGINE_VERSION )
			{Page = new WConfigPageRenderer(&D); D.Title=LocalizeGeneral(TEXT("FirstTime"),TEXT("Startup"));}
		else if( ParseParam(appCmdLine(),TEXT("changevideo")) )
			{Page = new WConfigPageRenderer(&D); D.Title=LocalizeGeneral(TEXT("Video"),TEXT("Startup"));}
		else if( ParseParam(appCmdLine(),TEXT("changeaudio")) )
			{Page = new WConfigPageAudio(&D); D.Title=LocalizeGeneral(TEXT("Audio"),TEXT("Startup"));}
		else if( !AlreadyRunning && GFileManager->FileSize(TEXT("Running.ini"))>=0 && !ParseParam(appCmdLine(), TEXT("nosafe")) )
			{Page = new WConfigPageSafeMode(&D); D.Title=LocalizeGeneral(TEXT("RecoveryMode"),TEXT("Startup"));}
		if( Page )
		{
			ExitSplash();
#if UNDYING_BINK
			ExitBink();
#endif
			D.Advance( Page );
			if( !D.DoModal() )
				return NULL;
			InitSplash(NULL);
		}
	}
	unguard;

	// Create is-running semaphore file.
	FArchive* Ar = GFileManager->CreateFileWriter(TEXT("Running.ini"),0);
	if( Ar )
		delete Ar;

	// Update first-run.
	if( FirstRun<ENGINE_VERSION )
		FirstRun = ENGINE_VERSION;
	GConfig->SetInt( TEXT("FirstRun"), TEXT("FirstRun"), FirstRun );

	// Cd check.
#ifdef CD_CHECK
	FString CdPath;
	GConfig->GetString( TEXT("Engine.Engine"), TEXT("CdPath"), CdPath );
	if
	(	CdPath!=TEXT("")
	&&	GFileManager->FileSize(TEXT("..\\Textures\\Palettes.utx"))<=0 )//oldver
	{
		FString Check = CdPath * TEXT("Textures\\Palettes.utx");
		while( !GIsEditor && GFileManager->FileSize(*Check)<=0 )
		{
			if( MessageBox
			(
				NULL,
				LocalizeGeneral("InsertCdText",TEXT("Window")),
				LocalizeGeneral("InsertCdTitle",TEXT("Window")),
				MB_TASKMODAL|MB_OKCANCEL
			)==IDCANCEL )
			{
				GIsCriticalError = 1;
				ExitProcess( 0 );
			}
		}
	}
#endif
#if ENGINE_VERSION<230
	// Display the damn story to appease the German censors.
	UBOOL CanModifyGore=1;
	GConfig->GetBool( TEXT("UnrealI.UnrealGameOptionsMenu"), TEXT("bCanModifyGore"), CanModifyGore );
	if( !CanModifyGore && !GIsEditor )
	{
		FString S;
		if( appLoadFileToString( S, TEXT("Story.txt") ) )
		{
			WTextScrollerDialog Dlg( TEXT("The Story"), *S );
			Dlg.DoModal();
		}
	}
#endif

	// Create the global engine object.
	UClass* EngineClass;
	if( !GIsEditor )
	{
		// Create game engine.
		EngineClass = UObject::StaticLoadClass( UGameEngine::StaticClass(), NULL, TEXT("ini:Engine.Engine.GameEngine"), NULL, LOAD_NoFail, NULL );
	}
	else
	{
		// Editor.
		EngineClass = UObject::StaticLoadClass( UEngine::StaticClass(), NULL, TEXT("ini:Engine.Engine.EditorEngine"), NULL, LOAD_NoFail, NULL );
	}
	UEngine* Engine = ConstructObject<UEngine>( EngineClass );
	Engine->Init();
	debugf( TEXT("Startup time: %f seconds"), appSeconds()-LoadTime );

	return Engine;
	unguard;
}

static BOOL GIgnoreErrors = FALSE;
BOOL GRecoveryMode = FALSE;

// from UT469
static BOOL IgnoreUEdError()
{
	if (!GIsEditor)
		return FALSE;
	GErrorHist[ARRAY_COUNT(GErrorHist) - 1] = 0;
	//debugf(TEXT("Try catch error"));
	int Answer = GIgnoreErrors ? IDIGNORE : MessageBox(NULL, GErrorHist, GConfig ? TEXT("Critical Error") : TEXT("Critical Error At Startup"), MB_ABORTRETRYIGNORE | MB_ICONERROR | MB_TASKMODAL);
	if (Answer != IDRETRY && Answer != IDIGNORE)
		return FALSE;
	GIsCriticalError = 0;
	if (Answer == IDIGNORE)
		GIgnoreErrors = TRUE;
	if (!GRecoveryMode)
	{
		GRecoveryMode = TRUE;
		GWarn->YesNof(TEXT("UnrealEd will now continue in Recovery Mode.\n\nPlease back up your unsaved work and restart UnrealEd as soon as possible.\n\nClick any option to continue."));
	}
	else
		GRecoveryMode = TRUE;
	return TRUE;
}

#define guardcatch try {
#define unguardcatch }\
catch (TCHAR* Err) { if (!IgnoreUEdError()) throw Err; }\
catch (int Err) { if (!IgnoreUEdError()) throw Err; }\
catch (...) { if (!IgnoreUEdError()) throw; }\

//
// Unreal's main message loop.  All windows in Unreal receive messages
// somewhere below this function on the stack.
//
static void MainLoop( UEngine* Engine )
{
	guard(MainLoop);
	check(Engine);

	// Enter main loop.
	guard(EnterMainLoop);
	if( GLogWindow )
		GLogWindow->SetExec( Engine );
	unguard;

	// Loop while running.
	GIsRunning = 1;
	DWORD ThreadId = GetCurrentThreadId();
	HANDLE hThread = GetCurrentThread();
	FTime OldTime = appSeconds();
	FTime SecondStartTime = OldTime;
	INT TickCount = 0;
	GStatStack.Init();
	while( GIsRunning && !GIsRequestingExit )
	{
		guardcatch;

		// Update the world.
		guard(UpdateWorld);
		FTime NewTime   = appSeconds();
		FLOAT DeltaTime = NewTime - OldTime;
		Engine->Tick( DeltaTime );
		if( GWindowManager )
			GWindowManager->Tick( DeltaTime );
		OldTime = NewTime;
		TickCount++;
		if( OldTime > SecondStartTime + 1 )
		{
			Engine->CurrentTickRate = (FLOAT)TickCount / (OldTime - SecondStartTime);
			SecondStartTime = OldTime;
			TickCount = 0;
		}
		unguard;

		// Enforce optional maximum tick rate.
		guard(EnforceTickRate);
		FLOAT MaxTickRate = Engine->GetMaxTickRate();
		if( MaxTickRate>0.0 )
		{
			FLOAT Delta = (1.0/MaxTickRate) - (appSeconds()-OldTime);
			appSleep( Max(0.f,Delta) );
		}
		unguard;

		unguardcatch;

		// Handle all incoming messages.
		guard(MessagePump);
		MSG Msg;
		while( PeekMessageX(&Msg,NULL,0,0,PM_REMOVE) )
		{
			guardcatch;

			if( Msg.message == WM_QUIT )
				GIsRequestingExit = 1;

			if( GIsEditor )
			{
				HWND Curr = GetActiveWindow();
				if (Curr == NULL)
					Curr = Msg.hwnd;
				TranslateAccelerator(
					Curr,  // handle to receiving window
					hAccel,    // handle to active accelerator table
					&Msg);     // message data
				if (WC_DIALOG == MAKEINTATOM(GetClassLong(Curr, GCW_ATOM)) && IsDialogMessage(Curr, &Msg))
					continue;
			}

			guard(TranslateMessage);
			TranslateMessage( &Msg );
			unguardf(( TEXT("%08X %i"), (PTRINT)Msg.hwnd, Msg.message ));

			guard(DispatchMessage);
			DispatchMessageX( &Msg );
			unguardf(( TEXT("%08X %i"), (PTRINT)Msg.hwnd, Msg.message ));
			
			unguardcatch;

			// Modern versions of windows no longer propagate exceptions thrown in Window procs.
			// If you want an exception thrown in a window proc to propagate all the way to the main loop, then you need to
			// eat the exception in the window proc, and rethrow it here.
			// See https://docs.microsoft.com/en-us/previous-versions/windows/desktop/legacy/ms633573(v=vs.85) for details
			if( GIsCriticalError && !IgnoreUEdError() )
				throw 1;
		}
		unguard;

		// If editor thread doesn't have the focus, don't suck up too much CPU time.
		if( GIsEditor )
		{
			guard(ThrottleEditor);
			static UBOOL HadFocus=1;
			UBOOL HasFocus = (GetWindowThreadProcessId(GetForegroundWindow(),NULL) == ThreadId );
			if( HadFocus && !HasFocus )
			{
				// Drop our priority to speed up whatever is in the foreground.
				SetThreadPriority( hThread, THREAD_PRIORITY_BELOW_NORMAL );
			}
			else if( HasFocus && !HadFocus )
			{
				// Boost our priority back to normal.
				SetThreadPriority( hThread, THREAD_PRIORITY_NORMAL );
			}
			if( !HasFocus )
			{
				// Surrender the rest of this timeslice.
				Sleep(0);
			}
			HadFocus = HasFocus;
			unguard;
		}

#if ADDITIONS_IMPROVEMENTS
		bool bSuppressLog = !GLogWindow || !GLogWindow->m_bShow;
#else
		bool bSuppressLog = true;
#endif

#if !_DEBUG
		GLog->SuppressAllEnable(bSuppressLog);
#endif
	}
	GStatStack.Finish();
	GIsRunning = 0;

	// Exit main loop.
	guard(ExitMainLoop);
	if( GLogWindow )
		GLogWindow->SetExec( NULL );
	GExec = NULL;
	unguard;

	unguard;
}
#endif

/*-----------------------------------------------------------------------------
	The End.
-----------------------------------------------------------------------------*/
