/*=============================================================================
	UnGame.cpp: Unreal game engine.
	Copyright 1997-1999 Epic Games, Inc. All Rights Reserved.

	Revision history:
		* Created by <PERSON>
=============================================================================*/

#include "EnginePrivate.h"
#include "UnRender.h"
#include "UnNet.h"
#include "UnStat.h"

/*-----------------------------------------------------------------------------
	Object class implementation.
-----------------------------------------------------------------------------*/

IMPLEMENT_CLASS(UGameEngine);

/*-----------------------------------------------------------------------------
	cleanup!!
-----------------------------------------------------------------------------*/

void UGameEngine::PaintProgress()
{
	guard(PaintProgress);

	FVector LoadFog(0,0,0);
	FVector LoadScale(0,0,0);
	UViewport* Viewport=Client->Viewports(0);
	Exchange(Viewport->Actor->FlashFog,LoadFog);
	Exchange(Viewport->Actor->FlashScale,LoadScale);
	Draw( Viewport );
	Exchange(Viewport->Actor->FlashFog,LoadFog);
	Exchange(Viewport->Actor->FlashScale,LoadScale);

	unguard;
}

INT UGameEngine::ChallengeResponse( INT Challenge )
{
	guard(UGameEngine::ChallengeResponse);
	return (Challenge*237) ^ (0x93fe92Ce) ^ (Challenge>>16) ^ (Challenge<<16);
	unguard;
}

void UGameEngine::UpdateConnectingMessage()
{
	guard(UGameEngine::UpdateConnectingMessage);
	if( GPendingLevel && Client && Client->Viewports.Num() )
	{
		APlayerPawn* Actor = Client->Viewports(0)->Actor;
		if( Actor->ProgressTimeOut<Actor->Level->TimeSeconds )
		{
			TCHAR Msg1[256], Msg2[256];
			if( GPendingLevel->DemoRecDriver )
			{
				appSprintf( Msg1, TEXT("") );
				appSprintf( Msg2, *GPendingLevel->URL.Map );
			}
			else
			{
				appSprintf( Msg1, LocalizeProgress("ConnectingText") );
				appSprintf( Msg2, LocalizeProgress("ConnectingURL"), *GPendingLevel->URL.Host, *GPendingLevel->URL.Map );
			}
			SetProgress( Msg1, Msg2, 60.0 );
		}
	}
	unguard;
}
void UGameEngine::BuildServerMasterMap( UNetDriver* NetDriver, ULevel* InLevel )
{
	guard(UGameEngine::BuildServerMasterMap);
	check(NetDriver);
	check(InLevel);
	BeginLoad();
	{
		// Init LinkerMap.
		check(InLevel->GetLinker());
		NetDriver->MasterMap->AddLinker( InLevel->GetLinker() );

		// Load server-required packages.
		for( INT i=0; i<ServerPackages.Num(); i++ )
		{
			debugf( TEXT("Server Package: %s"), *ServerPackages(i) );
			ULinkerLoad* Linker = GetPackageLinker( NULL, *ServerPackages(i), LOAD_NoFail, NULL, NULL );
			if( NetDriver->MasterMap->AddLinker( Linker )==INDEX_NONE )
				debugf( TEXT("   (server-side only)") );
		}

		// Add GameInfo's package to map.
		check(InLevel->GetLevelInfo());
		check(InLevel->GetLevelInfo()->Game);
		check(InLevel->GetLevelInfo()->Game->GetClass()->GetLinker());
		NetDriver->MasterMap->AddLinker( InLevel->GetLevelInfo()->Game->GetClass()->GetLinker() );

		// Precompute linker info.
		NetDriver->MasterMap->Compute();
	}
	EndLoad();
	unguard;
}

/*-----------------------------------------------------------------------------
	Game init and exit.
-----------------------------------------------------------------------------*/

//
// Construct the game engine.
//
UGameEngine::UGameEngine()
: LastURL(TEXT(""))
, ServerActors( E_NoInit )
, ServerPackages( E_NoInit )
{
	if( !GEngine )
		GEngine = this;
}

//
// Class creator.
//
void UGameEngine::StaticConstructor()
{
	guard(UGameEngine::StaticConstructor);

	UArrayProperty* A = new(GetClass(),TEXT("ServerActors"),RF_Public)UArrayProperty( CPP_PROPERTY(ServerActors), TEXT("Settings"), CPF_Config );
	A->Inner = new(A,TEXT("StrProperty0"),RF_Public)UStrProperty;

	UArrayProperty* B = new(GetClass(),TEXT("ServerPackages"),RF_Public)UArrayProperty( CPP_PROPERTY(ServerPackages), TEXT("Settings"), CPF_Config );
	B->Inner = new(B,TEXT("StrProperty0"),RF_Public)UStrProperty;

	unguard;
}

//extern void InitPublisher();

//
// Initialize the game engine.
//
void UGameEngine::Init()
{
	guard(UGameEngine::Init);
	check(sizeof(*this)==GetClass()->GetPropertiesSize());

	// Call base.
	UEngine::Init();

	// Init variables.
	GLevel = NULL;
	GViewport = NULL;

	//if (!GSaveGame )
	//{
	//	GSaveGame = new(TEXT("SaveGame")) USaveGame();
	//}

	// Delete temporary files in cache.
	appCleanFileCache();

	GFileManager->MakeDirectory( *GSys->SavePath );

	// delete temporary saves
	TArray<FString> TempSaves = GFileManager->FindFiles( *FString::Printf( TEXT("%s") PATH_SEPARATOR TEXT("*.%s"), *GSys->SavePath, *FURL::DefaultSaveExt ), 1, 0 );
	for( INT i=0; i<TempSaves.Num(); i++ )
	{
		FString SaveName = *FString::Printf( TEXT("%s") PATH_SEPARATOR TEXT("%s"), *GSys->SavePath, *TempSaves(i) );
		GLog->Logf( TEXT("Deleting temporary save file: %s"), *SaveName );

		GFileManager->Delete( *SaveName, 0, 1 );
	}

	// If not a dedicated server.
	if( GIsClient )
	{	
		// Init client.
		UClass* ClientClass = StaticLoadClass( UClient::StaticClass(), NULL, TEXT("ini:Engine.Engine.ViewportManager"), NULL, LOAD_NoFail, NULL );
		Client = ConstructObject<UClient>( ClientClass );
		Client->Init( this );

		// Init rendering.
		UClass* RenderClass = StaticLoadClass( URenderBase::StaticClass(), NULL, TEXT("ini:Engine.Engine.Render"), NULL, LOAD_NoFail, NULL );
		Render = ConstructObject<URenderBase>( RenderClass );
		Render->Init( this );

		// Init audio.
		InitAudio();
	}

	// Load the entry level.
	FString Error;
	if( Client )
	{
		if( !LoadMap( FURL(TEXT("Entry")), NULL, NULL, Error ) )
			appErrorf( LocalizeError("FailedBrowse"), TEXT("Entry"), *Error );
		Exchange( GLevel, GEntry );
	}

	// Create default URL.
	FURL DefaultURL;
	DefaultURL.LoadURLConfig( TEXT("DefaultPlayer"), TEXT("User") );

	// Enter initial world.
	TCHAR Parm[4096]=TEXT("");
	const TCHAR* Tmp = appCmdLine();
	if
	(	!ParseToken( Tmp, Parm, ARRAY_COUNT(Parm), 0 )
	||	(appStricmp(Parm,TEXT("SERVER"))==0 && !ParseToken( Tmp, Parm, ARRAY_COUNT(Parm), 0 ))
	||	Parm[0]=='-' )
		appStrcpy( Parm, *FURL::DefaultLocalMap );

	FString ReplayFile;
	if( Parse( appCmdLine(), TEXT("-REPLAY="), ReplayFile ) )
	{
		// Start replay if specified.
		if( Replay.Replay( *ReplayFile, *GLog ) )
			// Retrieve the URL from it.
			appStrcpy( Parm, *Replay.GetURLStr() );
	}
	else if( Parse( appCmdLine(), TEXT("-RECORD="), ReplayFile ) )
	{
		// Start recording, with this URL.
		Replay.Record( *ReplayFile, Parm, *GLog );
	}

	FURL URL( &DefaultURL, Parm, TRAVEL_Partial );
	if( !URL.Valid )
		appErrorf( LocalizeError("InvalidUrl"), Parm );

	UBOOL Success = Browse( URL, NULL, Error );

	// If waiting for a network connection, go into the starting level.
	if( !Success && Error==TEXT("") && appStricmp( Parm, *FURL::DefaultLocalMap )!=0 )
		Success = Browse( FURL(&DefaultURL,*FURL::DefaultLocalMap,TRAVEL_Partial), NULL, Error );

	// Handle failure.
	if( !Success )
		appErrorf( LocalizeError("FailedBrowse"), Parm, *Error );

	// IPC
	//InitPublisher();

	// Open initial Viewport.
	if( Client )
	{
		// Init input.!!Temporary
		UInput::StaticInitInput();

		// Create viewport.
		UViewport* Viewport = GViewport = Client->NewViewport( NAME_None );

		// Create console.
		UClass* ConsoleClass = StaticLoadClass( UConsole::StaticClass(), NULL, TEXT("ini:Engine.Engine.Console"), NULL, LOAD_NoFail, NULL );
		Viewport->Console = ConstructObject<UConsole>( ConsoleClass );
		Viewport->Console->_Init( Viewport );

		// Spawn play actor.
		FString SpawnError;
		if( !GLevel->SpawnPlayActor( Viewport, ROLE_SimulatedProxy, URL, SpawnError ) )
			appErrorf( TEXT("%s"), *SpawnError );
		Viewport->Input->Init( Viewport );
		Viewport->OpenWindow( 0, 0, (INT) INDEX_NONE, (INT) INDEX_NONE, (INT) INDEX_NONE, (INT) INDEX_NONE );
		GLevel->DetailChange( Viewport->RenDev->HighDetailActors );
		if( Audio )
			Audio->SetViewport( Viewport );
	}
	debugf( NAME_Init, TEXT("Game engine initialized") );

	unguard;
}

//
// Pre exit.
//
void UGameEngine::Exit()
{
	guard(UGameEngine::Exit);
	Super::Exit();

	// Exit net.
	if( GLevel->NetDriver )
	{
		delete GLevel->NetDriver;
		GLevel->NetDriver = NULL;
	}

	unguard;
}

//
// Game exit.
//
void UGameEngine::Destroy()
{
	guard(UGameEngine::Destroy);

	// Game exit.
	if( GPendingLevel )
		CancelPending();
	GLevel = NULL;
	debugf( NAME_Exit, TEXT("Game engine shut down") );

	Super::Destroy();
	unguard;
}

//
// Progress text.
//
void UGameEngine::SetProgress( const TCHAR* Str1, const TCHAR* Str2, FLOAT Seconds )
{
	guard(UGameEngine::SetProgress);
	if( Client && Client->Viewports.Num() )
	{
		APlayerPawn* Actor = Client->Viewports(0)->Actor;
		if( Seconds==-1.0 )
		{
			// Upgrade message.
			Actor->eventShowUpgradeMenu();
		}
		Actor->ProgressMessage[0] = Str1;
		Actor->ProgressColor[0].R = 255;
		Actor->ProgressColor[0].G = 255;
		Actor->ProgressColor[0].B = 255;

		Actor->ProgressMessage[1] = Str2;
		Actor->ProgressColor[1].R = 255;
		Actor->ProgressColor[1].G = 255;
		Actor->ProgressColor[1].B = 255;

		Actor->ProgressTimeOut    = Actor->Level->TimeSeconds + Seconds;
	}
	unguard;
}

/*-----------------------------------------------------------------------------
	Command line executor.
-----------------------------------------------------------------------------*/

//
// This always going to be the last exec handler in the chain. It
// handles passing the command to all other global handlers.
//
UBOOL UGameEngine::Exec( const TCHAR* Cmd, FOutputDevice& Ar )
{
	guard(UGameEngine::Exec);
	const TCHAR* Str=Cmd;
	if( ParseCommand( &Str, TEXT("OPEN") ) )
	{
		FString Error;
		if( Client && Client->Viewports.Num() )
			SetClientTravel( Client->Viewports(0), Str, 0, TRAVEL_Partial );
		else
		if( !Browse( FURL(&LastURL,Str,TRAVEL_Partial), NULL, Error ) && Error!=TEXT("") )
			Ar.Logf( TEXT("Open failed: %s"), *Error );
		return 1;
	}
	else if( ParseCommand( &Str, TEXT("START") ) )
	{
		FString Error;
		if( Client && Client->Viewports.Num() )
			SetClientTravel( Client->Viewports(0), Str, 0, TRAVEL_Absolute );
		else
		if( !Browse( FURL(&LastURL,Str,TRAVEL_Absolute), NULL, Error ) && Error!=TEXT("") )
			Ar.Logf( TEXT("Start failed: %s"), *Error );
		return 1;
	}
	else if( ParseCommand( &Str, TEXT("SERVERTRAVEL") ) && (GIsServer && !GIsClient) )
	{
		GLevel->GetLevelInfo()->eventServerTravel(Str,0);
		return 1;
	}
	else if( (GIsServer && !GIsClient) && ParseCommand( &Str, TEXT("SAY") ) )
	{
		GLevel->GetLevelInfo()->eventBroadcastMessage(Str,1,NAME_None);
		return 1;
	}
	else if( ParseCommand(&Str, TEXT("DISCONNECT")) )
	{
		FString Error;
		if( Client && Client->Viewports.Num() )
			SetClientTravel( Client->Viewports(0), TEXT("?failed"), 0, TRAVEL_Absolute );
		else
		if( !Browse( FURL(&LastURL,TEXT("?failed"),TRAVEL_Absolute), NULL, Error ) && Error!=TEXT("") )
			Ar.Logf( TEXT("Disconnect failed: %s"), *Error );
		return 1;
	}
	else if( ParseCommand(&Str, TEXT("RECONNECT")) )
	{
		FString Error;
		if( Client && Client->Viewports.Num() )
			SetClientTravel( Client->Viewports(0), *LastURL.String(), 0, TRAVEL_Absolute );
		else
		if( !Browse( FURL(LastURL), NULL, Error ) && Error!=TEXT("") )
			Ar.Logf( TEXT("Reconnect failed: %s"), *Error );
		return 1;
	}
	else if( ParseCommand( &Str, TEXT("GETCURRENTTICKRATE") ) )
	{
		Ar.Logf( TEXT("%f"), CurrentTickRate );
		return 1;
	}
	else if( ParseCommand( &Str, TEXT("GETMAXTICKRATE") ) )
	{
		Ar.Logf( TEXT("%f"), GetMaxTickRate() );
		return 1;
	}
	else if( ParseCommand( &Str, TEXT("GSPYLITE") ) )
	{
		FString Error;
		appLaunchURL( TEXT("GSpyLite.exe"), TEXT(""), &Error );
		return 1;
	}
#if 1 // added by Legend on 4/12/2000
	else if( ParseCommand(&Str,TEXT("OBJCLEAN")) )
	{
		// quick hack to cleanup destroyed objects 
		if( GLevel )
			GLevel->CleanupDestroyed(1);
		return 1;
	}
#endif
	else if( ParseCommand(&Str,TEXT("SAVEGAME")) || ParseCommand(&Str,TEXT("SAVEGAMESTATE")) )
	{
		if( appIsDigit(Str[0]) )
		{
			SaveGameState( appAtoi(Str) );
		}
		return 1;
	}
	else if( ParseCommand(&Str,TEXT("LOADGAME")) || ParseCommand(&Str,TEXT("LOADGAMESTATE")) )
	{
		if( appIsDigit(Str[0]) && !GLevel->GetLevelInfo()->bDontAllowSavegame )
		{
			LoadGameState( appAtoi(Str) );
		}

		GLevel->GetLevelInfo()->bDontAllowSavegame = 0;

		if( Client && Client->Viewports.Num() && Client->Viewports(0)->Console )
			Client->Viewports(0)->Console->bShellPauses = 0;

		return 1;
	}
	else if( ParseCommand(&Str,TEXT("DELETEGAME")) )
	{
		if( appIsDigit(Str[0]) )
		{
			DeleteGameState( appAtoi(Str) );
		}
		return 1;
	}
	else if( ParseCommand(&Str,TEXT("SAVELEVEL")) )
	{
		return 1;
	}
	else if( ParseCommand(&Str,TEXT("LOADLEVEL")) )
	{
		return 1;
	}
	else if( ParseCommand(&Str,TEXT("RESETLEVEL")) )
	{
		return 1;
	}
	else if( ParseCommand(&Str,TEXT("DELETESAVELEVELS")) )
	{
		TCHAR Filename[256];
		appSprintf( Filename, TEXT("%s") PATH_SEPARATOR TEXT("*.%s"), *GSys->SavePath, *FURL::DefaultSaveExt );
		TArray<FString> TempSaves = GFileManager->FindFiles(Filename, 1, 0);

		for( INT i=0; i<TempSaves.Num(); i++ )
		{
			FString SaveName = *FString::Printf(TEXT("%s") PATH_SEPARATOR TEXT("%s"), *GSys->SavePath, *TempSaves(i));
			GLog->Logf(TEXT("Deleting %s."), *SaveName);
			GFileManager->Delete(*SaveName, 0, 1);
		}

		return 1;
	}
	else if( ParseCommand( &Cmd, TEXT("CANCEL") ) )
	{
		if( GPendingLevel )
			SetProgress( LocalizeProgress("CancelledConnect"), TEXT(""), 2.0 );
		else
			SetProgress( TEXT(""), TEXT(""), 0.0 );
		CancelPending();
		return 1;
	}
	else if( GLevel && GLevel->Exec( Cmd, Ar ) )
	{
		return 1;
	}
	else if( GLevel && GLevel->GetLevelInfo()->Game && GLevel->GetLevelInfo()->Game->ScriptConsoleExec(Cmd,Ar,NULL) )
	{
		return 1;
	}
	else if( UEngine::Exec( Cmd, Ar ) )
	{
		return 1;
	}
	else return 0;
	unguard;
}

/*-----------------------------------------------------------------------------
	Serialization.
-----------------------------------------------------------------------------*/

//
// Serializer.
//
void UGameEngine::Serialize( FArchive& Ar )
{
	guard(UGameEngine::Serialize);
	Super::Serialize( Ar );

	Ar << GLevel << GEntry << GPendingLevel;

	unguardobj;
}

/*-----------------------------------------------------------------------------
	Game entering.
-----------------------------------------------------------------------------*/

//
// Cancel pending level.
//
void UGameEngine::CancelPending()
{
	guard(UGameEngine::CancelPending);
	if( GPendingLevel )
	{
		delete GPendingLevel;
		GPendingLevel = NULL;
	}
	unguard;
}

//
// Match Viewports to actors.
//
static void MatchViewportsToActors( UClient* Client, ULevel* Level, const FURL& URL )
{
	guard(MatchViewportsToActors);
	for( INT i=0; i<Client->Viewports.Num(); i++ )
	{
		FString Error;
		UViewport* Viewport = Client->Viewports(i);
		debugf( NAME_Log, TEXT("Spawning new actor for Viewport %s"), Viewport->GetName() );
		if( !Level->SpawnPlayActor( Viewport, ROLE_SimulatedProxy, URL, Error ) )
			appErrorf( TEXT("%s"), *Error );
	}
	unguardf(( TEXT("(%s)"), *Level->URL.Map ));
}

//
// Browse to a specified URL, relative to the current one.
//
UBOOL UGameEngine::Browse( FURL URL, const TMap<FString,FString>* TravelInfo, FString& Error )
{
	guard(UGameEngine::Browse);
	Error = TEXT("");
	const TCHAR* Option;

	// Convert .unreal link files.
	const TCHAR* LinkStr = TEXT(".unreal");//!!
	if( appStrstr(*URL.Map,LinkStr)-*URL.Map==appStrlen(*URL.Map)-appStrlen(LinkStr) )
	{
		debugf( TEXT("Link: %s"), *URL.Map );
		FString NewUrlString;
		if( GConfig->GetString( TEXT("Link")/*!!*/, TEXT("Server"), NewUrlString, *URL.Map ) )
		{
			// Go to link.
			URL = FURL( NULL, *NewUrlString, TRAVEL_Absolute );//!!
		}
		else
		{
			// Invalid link.
			guard(InvalidLink);
			Error = FString::Printf( LocalizeError("InvalidLink", TEXT("Engine")), *URL.Map );
			unguard;
			return 0;
		}
	}

	// Crack the URL.
	debugf( TEXT("Browse: %s"), *URL.String() );

	// Handle it.
	if( !URL.Valid )
	{
		// Unknown URL.
		guard(UnknownURL);
		Error = FString::Printf( LocalizeError("InvalidUrl", TEXT("Engine")), *URL.String() );
		unguard;
		return 0;
	}
	else if( URL.HasOption(TEXT("failed")) || URL.HasOption(TEXT("entry")) )
	{
		// Handle failure URL.
		guard(FailedURL);
		debugf( NAME_Log, LocalizeError("AbortToEntry", TEXT("Engine")) );
		// Exit net. Prevents client from staying connected if they use the disconnect command.
		if (GLevel->NetDriver)
		{
			delete GLevel->NetDriver;
			GLevel->NetDriver = NULL;
		}
		if( GLevel && GLevel!= GEntry )
		{
			if( GLevel->BrushTracker )
			{
				delete GLevel->BrushTracker;
				GLevel->BrushTracker = NULL;
			}

#if ADDITIONS_FIXES
			if( GLevel->Hash )
			{
				delete GLevel->Hash;
				GLevel->Hash = NULL;
			}
			
			ResetLoaders( GLevel->GetOuter(), 0, 0 ); // changed DynamicOnly to 0 to potentially fix mismatch errors
#else
			ResetLoaders( GLevel->GetOuter(), 1, 0 );
#endif
		}
		NotifyLevelChange();
		GLevel = GEntry;
		GLevel->GetLevelInfo()->LevelAction = LEVACT_None;
		check(Client && Client->Viewports.Num());
		MatchViewportsToActors( Client, GLevel, URL );
		if( Audio )
			Audio->SetViewport( Audio->GetViewport() );
		guard(CollectGarbageAtDisconnect);
		// collecting garbage is necessary to prevent crashes on the client
		// example: MyPropInfo(0)=(Prop=Class'Aeons.WitchDaggers') reproducibly caused crashing when client changed level with 'start'
		UObject::CollectGarbage( RF_Native ); // Causes texture corruption unless you flush.
		if ( Client )
			Client->Flush(1);
		unguard;
		if( URL.HasOption(TEXT("failed")) )
		{
			if( !GPendingLevel )
			{
				if( URL.HasOption(TEXT("reconnect")) )
					GLevel->GetLevelInfo()->NextURL = LastURL.String();
				else
					SetProgress( LocalizeError("ConnectionFailed", TEXT("Engine")), TEXT(""), 6.0 );
			}
		}
		unguard;
		return 1;
	}
	else if( URL.HasOption(TEXT("pop")) )
	{
		// Pop the hub.
		guard(PopURL);
		if( GLevel && GLevel->GetLevelInfo()->HubStackLevel>0 )
		{
			TCHAR Filename[256], SavedPortal[256];
			appSprintf( Filename, TEXT("%s") PATH_SEPARATOR TEXT("Game%i.%s"), *GSys->SavePath, GLevel->GetLevelInfo()->HubStackLevel-1, *FURL::DefaultSaveExt );
			appStrcpy( SavedPortal, *URL.Portal );
			URL = FURL( &URL, Filename, TRAVEL_Partial );
			URL.Portal = SavedPortal;
		}
		else return 0;
		unguard;
	}
	else if( URL.HasOption(TEXT("restart")) )
	{
		// Handle restarting.
		guard(RestartURL);
		GLog->Logf(TEXT("restarting LastURL = %s"), *LastURL.String(0));
		URL = LastURL;
		URL.AddOption(TEXT("nosave"));
		unguard;
	}
	else if( (Option=URL.GetOption(TEXT("load="),NULL))!=NULL )
	{
		// Handle loadgame.
		guard(LoadURL);
		FString LoadError, Temp=FString::Printf( TEXT("%s") PATH_SEPARATOR TEXT("Save%i.%s?load"), *GSys->SavePath, appAtoi(Option), *FURL::DefaultSaveExt );
		if( LoadMap(FURL(&LastURL,*Temp,TRAVEL_Partial),NULL,NULL,LoadError) )
		{
			// Copy the hub stack.
			INT i;
			for( i=0; i<GLevel->GetLevelInfo()->HubStackLevel; i++ )
			{
				TCHAR Src[256], Dest[256];//!!
				appSprintf( Src, TEXT("%s") PATH_SEPARATOR TEXT("Save%i%i.%s"), *GSys->SavePath, appAtoi(Option), i, *FURL::DefaultSaveExt );
				appSprintf( Dest, TEXT("%s") PATH_SEPARATOR TEXT("Game%i.%s"), *GSys->SavePath, i, *FURL::DefaultSaveExt );
				GFileManager->Copy( Src, Dest );
			}
			while( 1 )
			{
				Temp = FString::Printf( TEXT("%s") PATH_SEPARATOR TEXT("Game%i.%s"), *GSys->SavePath, i++, *FURL::DefaultSaveExt );
				if( GFileManager->FileSize(*Temp)<=0 )
					break;
				GFileManager->Delete( *Temp );
			}
			LastURL = GLevel->URL;
			return 1;
		}
		else return 0;
		unguard;
	}

	// check for existing save before loading a map
	// added GLevel check, needed for checking netmode and it makes sense to not look for levels on the first Browse
	if (GLevel && GLevel->GetLevelInfo()->NetMode != NM_Client)
	{
		// when loading the saves they have already been copied to SavePath by now
		bool IsServer = GLevel->GetLevelInfo()->NetMode == NM_DedicatedServer || GLevel->GetLevelInfo()->NetMode == NM_ListenServer;
		TCHAR Filename[256];
		appSprintf(Filename, TEXT("%s\\%s.%s"), *GSys->SavePath, *URL.Map, * FURL::DefaultSaveExt);

		TArray<FString> Files = GFileManager->FindFiles(Filename, 1, 0);

		if (Files.Num() > 0)
		{
			GLog->Logf(TEXT("Found level saved: %s"), *Files(0));

			FString Portal = URL.Portal;
			//const TCHAR* Mutators = URL.GetOption(TEXT("mutator="), NULL);

			FURL LoadURL = FURL(&URL, Filename, TRAVEL_Relative);
			if (IsServer)
			{
				// fix portal resetting if second character in SavePath is ':'
				// only fixing this for multiplayer to preserve singleplayer speedrun strats
				LoadURL.Portal = Portal;
			}

			// mutators should be here already, don't add them here
			//if (Mutators != NULL)
			//	LoadURL.AddOption(*FString::Printf(TEXT("mutator=%s"), Mutators));

			LoadURL.AddOption(TEXT("load"));

			if (!URL.HasOption(TEXT("save")))
			{
				LoadURL.AddOption(TEXT("save"));
			}

			if (LoadMap(LoadURL, NULL, TravelInfo, Error))
			{
				if (!URL.HasOption(TEXT("loadgame")))
				{
					GLog->Logf(TEXT("found saved LastURL = %s"), *LastURL.String(0));
				}

				return 1;
			}
		}
	}

	// Handle normal URL's.
	if( URL.IsLocalInternal() )
	{
		// Local map file.
		guard(LocalMapURL);
		return LoadMap( URL, NULL, TravelInfo, Error )!=NULL;
		unguard;
	}
	else if( URL.IsInternal() && GIsClient )
	{
		// Network URL.
		guard(NetworkURL);
		if( GPendingLevel )
			CancelPending();
		GPendingLevel = new UNetPendingLevel(this, URL);
		if( !GPendingLevel->NetDriver )
		{
			SetProgress( TEXT("Networking Failed"), *GPendingLevel->Error, 6.0 );
			delete GPendingLevel;
			GPendingLevel = NULL;
		}
		return 0;
		unguard;
	}
	else if( URL.IsInternal() )
	{
		// Invalid.
		guard(InvalidURL);
		Error = LocalizeError("ServerOpen", TEXT("Engine"));
		unguard;
		return 0;
	}
	else
	{
		// External URL.
		guard(ExternalURL);
		appLaunchURL( *URL.String(), TEXT(""), &Error );
		unguard;
		return 0;
	}
	unguard;
}


//
// Notify that level is changing
//
void UGameEngine::NotifyLevelChange()
{
	guard(UGameEngine::NotifyLevelChange);
	if( Client && Client->Viewports.Num() && Client->Viewports(0)->Console )
		Client->Viewports(0)->Console->eventNotifyLevelChange();
	unguard;	
}

// Fixup a map
// hack to post release fix map actor problems without breaking compatibility
void UGameEngine::FixUpLevel( )
{
	if ( appStricmp(GLevel->GetFullName(), TEXT("Level CTF-Coret.MyLevel"))==0 )
	{
		debugf(TEXT("Fixing up CTF-Coret"));

		ANavigationPoint *Nav = GLevel->GetLevelInfo()->NavigationPointList;
		while (Nav)
		{
			if ( appStricmp(Nav->GetName(), TEXT("AlternatePath12"))==0
				|| appStricmp(Nav->GetName(), TEXT("AlternatePath13"))==0 )
			{
				debugf(NAME_Log, TEXT("Fixed up %s"),Nav->GetName());
				Nav->bTwoWay = 1;
			}
			else if ( appStricmp(Nav->GetName(), TEXT("PlayerStart5"))==0 )
			{
				APlayerStart *PS = Cast<APlayerStart>(Nav);
				if ( PS )
				{
					PS->bEnabled = false;
					debugf(NAME_Log, TEXT("Fixed up %s"),Nav->GetName());
				}
			}

			Nav = Nav->nextNavigationPoint;
		}
	}
	debugf(TEXT("Level is %s"), GLevel->GetFullName());

}

//
// Load a map.
//
ULevel* UGameEngine::LoadMap( const FURL& URL, UPendingLevel* Pending, const TMap<FString,FString>* TravelInfo, FString& Error )
{
	guard(UGameEngine::LoadMap);

	if ( GEntry ) GEntry->CleanupDestroyed(1); // gam

	Error = TEXT("");
	debugf( NAME_Log, TEXT("LoadMap: %s"), *URL.String() );
	GInitRunaway();

	// Remember current level's stack level.
	INT SavedHubStackLevel = GLevel ? GLevel->GetLevelInfo()->HubStackLevel : 0;

	// Display loading screen.
	guard(LoadingScreen);
	if( Client && Client->Viewports.Num() && GLevel )
	{
		GLevel->GetLevelInfo()->LevelAction = LEVACT_Loading;
		GLevel->GetLevelInfo()->Pauser = TEXT("");
		APlayerPawn* PP = Client->Viewports(0)->Actor;
		if( PP )
			PP->bShowMenu = 0;
		PaintProgress();
		UConsole* Console = Client->Viewports(0)->Console;
		if ( Console && Console->bUseTransitionScreen )
			appSleep(1.5);
		if( Audio )
			Audio->SetViewport( Audio->GetViewport() );
		GLevel->GetLevelInfo()->LevelAction = LEVACT_None;
	}
	unguard;

	// Get network package map.
	UPackageMap* PackageMap = NULL;
	if( Pending )
		PackageMap = Pending->GetDriver()->ServerConnection->PackageMap;

	// Verify that we can load all packages we need.
	UObject* MapParent = NULL;
	guard(VerifyPackages);
	try
	{
		BeginLoad();
		if( Pending )
		{
			// Verify that we can load everything needed for client in this network level.
			for( INT i=0; i<PackageMap->List.Num(); i++ )
				PackageMap->List(i).Linker = GetPackageLinker
				(
					PackageMap->List(i).Parent,
					NULL,
					LOAD_Verify | LOAD_Throw | LOAD_NoWarn | LOAD_NoVerify,
					NULL,
					&PackageMap->List(i).Guid
				);
			for( INT i=0; i<PackageMap->List.Num(); i++ )
				VerifyLinker( PackageMap->List(i).Linker );
			if( PackageMap->List.Num() )
				MapParent = PackageMap->List(0).Parent;
		}
		LoadObject<ULevel>( MapParent, TEXT("MyLevel"), *URL.Map, LOAD_Verify | LOAD_Throw | LOAD_NoWarn, NULL );
		EndLoad();

#if DEMOVERSION
		// If we area demo, prevent third party maps from being loaded.
		if( !Pending || !Pending->DemoRecDriver )
		{
			FString FileName(FString(TEXT("../Maps/"))+URL.Map);
			if( FileName.Right(4).Caps() != TEXT(".UNR"))
				FileName = FileName + TEXT(".unr");
			INT FileSize = GFileManager->FileSize( *FileName );
			debugf(TEXT("Looking for file: %s %d"), *FileName, FileSize);
			if( //FileSize != 0 &&
				( FileName.Caps() != TEXT("../MAPS/DM-TURBINEDEMO.UNR")	|| FileSize != 2135105 ) &&
				( FileName.Caps() != TEXT("../MAPS/DM-PHOBOSDEMO.UNR")	|| FileSize != 1618994 ) &&
				( FileName.Caps() != TEXT("../MAPS/DM-MORPHEUSDEMO.UNR")|| FileSize != 1193759 ) &&
				( FileName.Caps() != TEXT("../MAPS/DM-TEMPESTDEMO.UNR")	|| FileSize != 2152238 ) &&
				( FileName.Caps() != TEXT("../MAPS/CTF-CORETDEMO.UNR")	|| FileSize != 3498978 ) &&
				( FileName.Caps() != TEXT("../MAPS/DOM-SESMARDEMO.UNR")	|| FileSize != 2155658 ) &&
				( FileName.Caps() != TEXT("../MAPS/ENTRY.UNR")			|| FileSize != 34822 ) &&
				( FileName.Caps() != TEXT("../MAPS/UT-LOGO-MAP.UNR")	|| FileSize != 34884 ) )
			{
				Error = TEXT("Sorry, only the retail version of UT can load third party maps.");
				SetProgress( LocalizeError(TEXT("UrlFailed"),TEXT("Core")), *Error, 6.0 );
				return NULL;
			}
		}
#endif
	}
	catch( TCHAR* CatchError )
	{
		// Safely failed loading.
		EndLoad();
		Error = CatchError;
		SetProgress( LocalizeError(TEXT("UrlFailed"),TEXT("Core")), CatchError, 6.0 );
		return NULL;
	}
	unguard;

	// Notify of the level change, before we dissociate Viewport actors
	guard(NotifyLevelChange);
	if( GLevel )
		NotifyLevelChange();
	unguard;

	// Dissociate Viewport actors.
	guard(DissociateViewports);
	if( Client )
	{
		for( INT i=0; i<Client->Viewports.Num(); i++ )
		{
			APlayerPawn* Actor          = Client->Viewports(i)->Actor;
			ULevel*      Level          = Actor->GetLevel();
			Actor->Player               = NULL;
			Client->Viewports(i)->Actor = NULL;
			Level->DestroyActor( Actor );
		}
	}
	unguard;

	// Clean up game state.
	guard(ExitLevel);
	if( GLevel )
	{
		// Shut down.
#if ADDITIONS_FIXES
		if ( GLevel->Hash )
		{
			delete GLevel->Hash;
			GLevel->Hash = NULL;
		}
		ResetLoaders( GLevel->GetOuter(), 1, 0 );
#else
		ResetLoaders( GLevel->GetOuter(), 0, 1 );
#endif
		if( GLevel->BrushTracker )
		{
			delete GLevel->BrushTracker;
			GLevel->BrushTracker = NULL;
		}
		if( GLevel->NetDriver )
		{
			delete GLevel->NetDriver;
			GLevel->NetDriver = NULL;
		}
		if( GLevel->DemoRecDriver )
		{
			delete GLevel->DemoRecDriver;
			GLevel->DemoRecDriver = NULL;
		}

		/// Save the level we just exited

		//bool bPlayingDemo = GEngine->GPendingLevel && GEngine->GPendingLevel->DemoRecDriver; // fix crash when loading demos
		//if (GEngine->GLevel == GEngine->GEntry || bPlayingDemo || GLevel->GetLevelInfo()->NetMode == NM_Client || GetDefault<ALevelInfo>()->bDontAllowSavegame)
		//	return;

		if (URL.HasOption(TEXT("nosave")))
		{
			if (URL.HasOption(TEXT("push")))
			{
				// Save the current level minus players actors.
				GLevel->CleanupDestroyed(1);
				TCHAR Filename[256];
				appSprintf(Filename, TEXT("%s") PATH_SEPARATOR TEXT("Game%i.%s"), *GSys->SavePath, SavedHubStackLevel, *FURL::DefaultSaveExt);
				UObject::SavePackage(GLevel->GetOuter(), GLevel, 0, Filename, GLog, NULL, true);
			}
		}
		else
		{
			GLog->Logf(TEXT("Saving %s..."), *GLevel->URL.String(0));
			GLevel->CleanupDestroyed(1);
			FString Filename = FString::Printf(TEXT("%s\\%s.%s"), *GSys->SavePath, *GLevel->URL.Map, *FURL::DefaultSaveExt);
			UObject::SavePackage(GLevel->GetOuter(), GLevel, 0, *Filename, GLog, NULL, true);
			GLog->Logf(TEXT("Saving complete."));
		}
		GLevel = NULL;
	}
	unguard;

	// Load the level and all objects under it, using the proper Guid.
	guard(LoadLevel);
	GLevel = LoadObject<ULevel>( MapParent, TEXT("MyLevel"), *URL.Map, LOAD_NoFail, NULL );
	unguard;

	// If pending network level.
	if( Pending )
	{
		// If playing this network level alone, ditch the pending level.
		if( Pending && Pending->LonePlayer )
			Pending = NULL;

		// Setup network package info.
		PackageMap->Compute();
		for( INT i=0; i<PackageMap->List.Num(); i++ )
			if( PackageMap->List(i).LocalGeneration!=PackageMap->List(i).RemoteGeneration )
				Pending->GetDriver()->ServerConnection->Logf( TEXT("HAVE GUID=%s GEN=%i"), PackageMap->List(i).Guid.String(), PackageMap->List(i).LocalGeneration );
	}

	// Verify classes.
	guard(VerifyClasses);
	VERIFY_CLASS_OFFSET( A, Actor,       Owner         );
	VERIFY_CLASS_OFFSET( A, Actor,       TimerCounter  );
	VERIFY_CLASS_OFFSET( A, PlayerPawn,  Player        );
	VERIFY_CLASS_OFFSET( A, PlayerPawn,  MaxStepHeight );
	unguard;

	// Verify all native properties.
	#include "VerifyAllProperties.h"

	// Get LevelInfo.
	check(GLevel);
	ALevelInfo* Info = GLevel->GetLevelInfo();
	Info->ComputerName = appComputerName();

	// Handle pushing.
	guard(ProcessHubStack);
	Info->HubStackLevel
	=	URL.HasOption(TEXT("load")) ? Info->HubStackLevel
	:	URL.HasOption(TEXT("push")) ? SavedHubStackLevel+1
	:	URL.HasOption(TEXT("pop" )) ? Max(SavedHubStackLevel-1,0)
	:	URL.HasOption(TEXT("peer")) ? SavedHubStackLevel
	:	                              0;
	unguard;

	// Handle pending level.
	guard(ActivatePending);
	if( Pending )
	{
		check(Pending==GPendingLevel);

		// Hook network driver up to level.
		GLevel->NetDriver = Pending->NetDriver;
		if( GLevel->NetDriver )
			GLevel->NetDriver->Notify = GLevel;

		// Hook demo playback driver to level
		GLevel->DemoRecDriver = Pending->DemoRecDriver;
		if( GLevel->DemoRecDriver )
			GLevel->DemoRecDriver->Notify = GLevel;

		// Setup level.
		GLevel->GetLevelInfo()->NetMode = NM_Client;
	}
	else check(!GLevel->NetDriver);
	unguard;

	// Set level info.
	guard(InitLevel);
	if( !URL.GetOption(TEXT("load"),NULL) )
		GLevel->URL = URL;
	Info->EngineVersion = FString::Printf( TEXT("%i"), ENGINE_VERSION );
	Info->MinNetVersion = FString::Printf( TEXT("%i"), ENGINE_MIN_NET_VERSION );
	GLevel->Engine = this;
	if( TravelInfo )
		GLevel->TravelInfo = *TravelInfo;
	unguard;

	// Purge unused objects and flush caches.
	guard(Cleanup);
	if( appStricmp(GLevel->GetOuter()->GetName(),TEXT("Entry"))!=0 )
	{
		Flush(0);
		{for( TObjectIterator<AActor> It; It; ++It )
			if( It->IsIn(GLevel->GetOuter()) )
				It->SetFlags( RF_EliminateObject );}
		{for( INT i=0; i<GLevel->Actors.Num(); i++ )
			if( GLevel->Actors(i) )
				GLevel->Actors(i)->ClearFlags( RF_EliminateObject );}
		CollectGarbage( RF_Native );
	}
	unguard;

	// Tell the audio driver to clean up.
	if( Audio )
		Audio->CleanUp();

	// Init collision.
	GLevel->SetActorCollision( 1 );

	// Setup zone distance table for sound damping. Fast enough: Approx 3 msec.
	guard(SetupZoneTable);
	QWORD OldConvConn[64];
	QWORD ConvConn[64];
	for( INT i=0; i<64; i++ )
	{
		for( INT j=0; j<64; j++ )
		{
			OldConvConn[i] = GLevel->Model->Zones[i].Connectivity;
			if( i == j )
				GLevel->ZoneDist[i][j] = 0;
			else
				GLevel->ZoneDist[i][j] = 255;
		}
	}
	for( INT i=1; i<64; i++ )
	{
		for( INT j=0; j<64; j++ )
			for( INT k=0; k<64; k++ )
				if( (GLevel->ZoneDist[j][k] > i) && ((OldConvConn[j] & ((QWORD)1 << k)) != 0) )
					GLevel->ZoneDist[j][k] = i;
		for( INT j=0; j<64; j++ )
			ConvConn[j] = 0;
		for( INT j=0; j<64; j++ )
			for( INT k=0; k<64; k++ )
				if( (OldConvConn[j] & ((QWORD)1 << k)) != 0 )
					ConvConn[j] = ConvConn[j] | OldConvConn[k];
		for( INT j=0; j<64; j++ )
			OldConvConn[j] = ConvConn[j];
	}
	unguard;

	// Update the LevelInfo's time.
	GLevel->UpdateTime(Info);

	// Init the game info.
	TCHAR Options[1024]=TEXT("");
	TCHAR GameClassName[256]=TEXT("");
	FString GameError=TEXT(""); // or just use Error that's passed in?
	guard(InitGameInfo);
	for( INT i=0; i<URL.Op.Num(); i++ )
	{
		appStrcat( Options, TEXT("?") );
		appStrcat( Options, *URL.Op(i) );
		Parse( *URL.Op(i), TEXT("GAME="), GameClassName, ARRAY_COUNT(GameClassName) );
	}
	if( GLevel->IsServer() && !Info->Game )
	{
		// Get the GameInfo class.
		UClass* GameClass=NULL;
		if( !GameClassName[0] )
		{
			GameClass=Info->DefaultGameType;
			if( !GameClass )
				GameClass = StaticLoadClass( AGameInfo::StaticClass(), NULL, Client ? TEXT("ini:Engine.Engine.DefaultGame") : TEXT("ini:Engine.Engine.DefaultServerGame"), NULL, LOAD_NoFail, PackageMap );
		}
		else GameClass = StaticLoadClass( AGameInfo::StaticClass(), NULL, GameClassName, NULL, LOAD_NoFail, PackageMap );

		// Spawn the GameInfo.
		debugf( NAME_Log, TEXT("Game class is '%s'"), GameClass->GetName() );
		Info->Game = (AGameInfo*)GLevel->SpawnActor( GameClass );
		check(Info->Game!=NULL);
	}
	unguard;

	// Listen for clients.
	guard(Listen);
	if( !Client || URL.HasOption(TEXT("Listen")) )
	{
		if( GPendingLevel )
		{
			guard(CancelPendingForListen);
			check(!Pending);
			delete GPendingLevel;
			GPendingLevel = NULL;
			unguard;
		}
		FString ListenError;
		if( !GLevel->Listen( ListenError ) )
			appErrorf( LocalizeError("ServerListen", TEXT("Engine")), *ListenError );
	}
	unguard;

	// Init detail.
	Info->bHighDetailMode = 1;
	if
	(	Client
	&&	Client->Viewports.Num()
	&&	Client->Viewports(0)->RenDev
	&&	!Client->Viewports(0)->RenDev->HighDetailActors )
		Info->bHighDetailMode = 0;

	// Init level gameplay info.
	guard(BeginPlay);
	GLevel->iFirstDynamicActor = 0;
#if ADDITIONS_FIXES
	// the client has to have bBegunPlay == 0 so it can kill off actors that aren't interesting to the client
	// bBegunPlay should be 1 on the server so events are not called multiple times which breaks things
	// beacause client receives the save from the server, this is necessary
	if( !Info->bBegunPlay || Info->NetMode == NM_Client )
#else
	if( !Info->bBegunPlay )
#endif
	{
		// fix up level problems
		FixUpLevel();

		// Lock the level.
		debugf( NAME_Log, TEXT("Bringing %s up for play (%i)..."), GLevel->GetFullName(), appRound(GetMaxTickRate()) );
		GLevel->TimeSeconds = 0.0;
		GLevel->GetLevelInfo()->TimeSeconds = 0;

		// Init touching actors.
		for( INT i=0; i<GLevel->Actors.Num(); i++ )
			if( GLevel->Actors(i) )
				for( INT j=0; j<ARRAY_COUNT(GLevel->Actors(i)->Touching); j++ )
					GLevel->Actors(i)->Touching[j] = NULL;

		// Kill off actors that aren't interesting to the client.
		if( !GLevel->IsServer() )
		{
			for( INT i=0; i<GLevel->Actors.Num(); i++ )
			{
				AActor* Actor = GLevel->Actors(i);
				if( Actor )
				{
					bool bIsParticle = Actor->IsA(AParticleFX::StaticClass());
					if( Actor->bStatic || Actor->bNoDelete || bIsParticle ) // added hack for AParticleFX, ROLE_None for particles represents bClientAuthoritative
					{
						if ( !bIsParticle || Actor->RemoteRole != ROLE_None )
							Exchange( Actor->Role, Actor->RemoteRole );
					}
					else
						GLevel->DestroyActor( Actor );
				}
			}

#if ADDITIONS_FIXES
			// not really needed but it's good to have for loading multiplayer saves to not have to check bDeleteMe
			GLevel->CleanupDestroyed(1);
#endif
		}

		// Init scripting.
		for( INT i=0; i<GLevel->Actors.Num(); i++ )
			if( GLevel->Actors(i) )
				GLevel->Actors(i)->InitExecution();

		// Enable actor script calls.
		Info->bBegunPlay = 1;
		Info->bStartup = 1;

		// Init the game.
		if( Info->Game )
			Info->Game->eventInitGame( Options, GameError );

		// Send PreBeginPlay.
		for( INT i=0; i<GLevel->Actors.Num(); i++ )
			if( GLevel->Actors(i) && !GLevel->Actors(i)->bScriptInitialized )
				GLevel->Actors(i)->eventPreBeginPlay();

		// Set BeginPlay.
		for( INT i=0; i<GLevel->Actors.Num(); i++ )
			if( GLevel->Actors(i) && !GLevel->Actors(i)->bScriptInitialized )
				GLevel->Actors(i)->eventBeginPlay();

		// Set zones.
		for( INT i=0; i<GLevel->Actors.Num(); i++ )
			if( GLevel->Actors(i) && !GLevel->Actors(i)->bScriptInitialized )
				GLevel->SetActorZone( GLevel->Actors(i), 1, 1 );

		// Post begin play.
		for( INT i=0; i<GLevel->Actors.Num(); i++ )
			if( GLevel->Actors(i) && !GLevel->Actors(i)->bScriptInitialized )
				GLevel->Actors(i)->eventPostBeginPlay();

#if 0 // ut 2004
		// Post net begin play.
		for ( i = 0; i<GLevel->Actors.Num(); i++ )
			if ( GLevel->Actors(i) && !GLevel->Actors(i)->bScriptInitialized )
				GLevel->Actors(i)->eventPostNetBeginPlay();
#endif

		// Begin scripting.
		for( INT i=0; i<GLevel->Actors.Num(); i++ )
			if( GLevel->Actors(i) && !GLevel->Actors(i)->bScriptInitialized )
				GLevel->Actors(i)->eventSetInitialState();

		// Find bases
		for( INT i=0; i<GLevel->Actors.Num(); i++ )
		{
			if( GLevel->Actors(i) ) 
			{
				if ( GLevel->Actors(i)->AttachTag != NAME_None )
				{
					//find actor to attach self onto
					for( INT j=0; j<GLevel->Actors.Num(); j++ )
					{
						if( GLevel->Actors(j) && (GLevel->Actors(j)->Tag == GLevel->Actors(i)->AttachTag) )
						{
							INT JointLocIndex = GLevel->Actors(j)->JointLocIndex(GLevel->Actors(i)->AttachJointTag);
							GLevel->Actors(i)->SetBase(GLevel->Actors(j), JointLocIndex < 0 ? 0 : JointLocIndex, -1, 0);
							break;
						}
					}
				}
				else if( !GLevel->Actors(i)->Base && GLevel->Actors(i)->bCollideWorld 
				 && (GLevel->Actors(i)->IsA(ADecoration::StaticClass()) || GLevel->Actors(i)->IsA(AInventory::StaticClass()) || GLevel->Actors(i)->IsA(APawn::StaticClass())) 
				 &&	((GLevel->Actors(i)->Physics == PHYS_None) || (GLevel->Actors(i)->Physics == PHYS_Rotating)) )
				{
					 GLevel->Actors(i)->FindBase();
					 if ( GLevel->Actors(i)->Base == Info )
						 GLevel->Actors(i)->SetBase(NULL, 0);
				}
			}
		}
		Info->bStartup = 0;
	}
	else GLevel->TimeSeconds = GLevel->GetLevelInfo()->TimeSeconds;
	unguard;

	// Rearrange actors: static first, then others.
	guard(Rearrange);
	TArray<AActor*> Actors;
	Actors.AddItem(GLevel->Actors(0));
	Actors.AddItem(GLevel->Actors(1));
	for( INT i=2; i<GLevel->Actors.Num(); i++ )
		if( GLevel->Actors(i) && GLevel->Actors(i)->bStatic && !GLevel->Actors(i)->bAlwaysRelevant )
			Actors.AddItem( GLevel->Actors(i) );
	GLevel->iFirstNetRelevantActor=Actors.Num();
	for( INT i=2; i<GLevel->Actors.Num(); i++ )
		if( GLevel->Actors(i) && GLevel->Actors(i)->bStatic && GLevel->Actors(i)->bAlwaysRelevant )
			Actors.AddItem( GLevel->Actors(i) );
	GLevel->iFirstDynamicActor=Actors.Num();
	for( INT i=2; i<GLevel->Actors.Num(); i++ )
		if( GLevel->Actors(i) && !GLevel->Actors(i)->bStatic )
			Actors.AddItem( GLevel->Actors(i) );
	GLevel->Actors.Empty();
	GLevel->Actors.Add( Actors.Num() );
	for( INT i=0; i<Actors.Num(); i++ )
		GLevel->Actors(i) = Actors(i);
	unguard;

	// Cleanup profiling.
#if DO_GUARD_SLOW
	guard(CleanupProfiling);
	for( TObjectIterator<UFunction> It; It; ++It )
		It->Calls = It->Cycles=0;
	GTicks=1;
	unguard;
#endif

	// Client init.
	guard(ClientInit);
	if( Client )
	{
		// Match Viewports to actors.
		MatchViewportsToActors( Client, GLevel->IsServer() ? GLevel : GEntry, URL );

		// Init brush tracker.
		if( appStricmp(GLevel->GetOuter()->GetName(),TEXT("Entry"))!=0 )//!!
			GLevel->BrushTracker = GNewBrushTracker( GLevel );

		// Set up audio.
		if( Audio )
			Audio->SetViewport( Audio->GetViewport() );

		// Reset viewports.
		for( INT i=0; i<Client->Viewports.Num(); i++ )
		{
			UViewport* Viewport = Client->Viewports(i);
			Viewport->Input->ResetInput();
			if( Viewport->RenDev )
				Viewport->RenDev->Flush(1);
		}
	}
	unguard;

	// Init detail.
	GLevel->DetailChange( Info->bHighDetailMode );

	// Remember the URL.
	guard(RememberURL);
	if( !URL.GetOption(TEXT("loadgame"),NULL) )
	{
		LastURL = URL;
		GLog->Logf(TEXT("loadmap LastURL = %s"), *LastURL.String(0));
	}
	unguard;

	// Remember DefaultPlayer options.
	if( GIsClient )
	{
		URL.SaveURLConfig( TEXT("DefaultPlayer"), TEXT("Name" ), TEXT("User") );
		URL.SaveURLConfig( TEXT("DefaultPlayer"), TEXT("Team" ), TEXT("User") );
		URL.SaveURLConfig( TEXT("DefaultPlayer"), TEXT("Class"), TEXT("User") );
		URL.SaveURLConfig( TEXT("DefaultPlayer"), TEXT("Skin" ), TEXT("User") );
		URL.SaveURLConfig( TEXT("DefaultPlayer"), TEXT("Face" ), TEXT("User") );
		URL.SaveURLConfig( TEXT("DefaultPlayer"), TEXT("Voice" ), TEXT("User") );
		URL.SaveURLConfig( TEXT("DefaultPlayer"), TEXT("OverrideClass" ), TEXT("User") );
	}

	// Reset bSpawned.
	for( INT i=2; i<GLevel->Actors.Num(); i++ )
		if( GLevel->Actors(i) && !GLevel->Actors(i)->bStatic )
			GLevel->Actors(i)->bSpawned = 0;

	// Start level.
	for( INT i=0; i<GLevel->Actors.Num(); i++ )
		if( GLevel->Actors(i) )
			GLevel->Actors(i)->eventStartLevel();

	// Successfully started local level.
	return GLevel;
	unguard;
}


/*-----------------------------------------------------------------------------
	Game Viewport functions.
-----------------------------------------------------------------------------*/

//
// Draw a global view.
//
void UGameEngine::Draw( UViewport* Viewport, UBOOL Blit, BYTE* HitData, INT* HitSize )
{
	guard(UGameEngine::Draw);

	// If not up and running yet, don't draw.
	if( !GIsRunning )
		return;
	UpdateConnectingMessage();

	// Get view location.
	AActor*      ViewActor    = Viewport->Actor;
	FVector      ViewLocation = ViewActor->Location;
	FRotator     ViewRotation = ViewActor->Rotation;
	Viewport->Actor->eventPlayerCalcView( ViewActor, ViewLocation, ViewRotation );
	check(ViewActor);

	// Precaching message.
	BYTE SavedAction = ViewActor->Level->LevelAction;
	if( Viewport->RenDev->PrecacheOnFlip && !Viewport->bSuspendPrecaching )
		ViewActor->Level->LevelAction = LEVACT_Precaching;

	// See if viewer is inside world.
	DWORD LockFlags=0;
	FCheckResult Hit;
	if( !GLevel->Model->PointCheck(Hit,NULL,ViewLocation,FVector(0,0,0),0) )
		LockFlags |= LOCKR_ClearScreen;

	//UMRMActorData::GarbageCollect();

#if defined(LEGEND) //MWP
	if( Viewport->Actor->IsA( APlayerPawn::StaticClass() ) )
	{
		// call the PlayerPawn Render Control Interface (RCI) to assess clear-screen operations
		if( Viewport->Actor->ClearScreen() )
		{
			LockFlags |= LOCKR_ClearScreen;
		}

		// call the PlayerPawn Render Control Interface (RCI) to assess lighting recomputation
		//
		// WARNING: RecomputeLighting() should *not* return false regularly, or rendering 
		//          performance will be severly compromised
		if( Viewport->Actor->RecomputeLighting() )
		{
			guard(RecomputeLighting);
			Flush(1); // 0 or 1? doesn't matter since this code is not in undying, because RecomputeLighting always returns false
			unguard;
		}
	}
#endif

	// Lock the Viewport.
	check(Render);
	FPlane FlashScale = Client->ScreenFlashes ? 0.5*Viewport->Actor->FlashScale : FVector(0.5,0.5,0.5);
	FPlane FlashFog   = Client->ScreenFlashes ? Viewport->Actor->FlashFog : FVector(0,0,0);
	FlashScale.X = Clamp( FlashScale.X, 0.f, 1.f );
	FlashScale.Y = Clamp( FlashScale.Y, 0.f, 1.f );
	FlashScale.Z = Clamp( FlashScale.Z, 0.f, 1.f );
	FlashFog.X   = Clamp( FlashFog.X  , 0.f, 1.f );
	FlashFog.Y   = Clamp( FlashFog.Y  , 0.f, 1.f );
	FlashFog.Z   = Clamp( FlashFog.Z  , 0.f, 1.f );
	if( Viewport->Lock(FlashScale,FlashFog,FPlane(0,0,0,0),LockFlags,HitData,HitSize) )
	{
#if UNDYING_MEM
		FMemMark MemMark(GMem);
		FMemMark DynMark(GDynMem);
		FMemMark SceneMark(GSceneMem);
#endif

		APlayerPawn* PlayerPawn = Cast<APlayerPawn>(Viewport->Actor); // useless cast
		FLOAT OrigMinQuality = Viewport->GetOuterUClient()->MinQuality;
		UBOOL bRestoreMinQuality = 0;
		if( PlayerPawn )
		{
			FName StateName = (PlayerPawn->GetStateFrame() && PlayerPawn->GetStateFrame()->StateNode) ? PlayerPawn->GetStateFrame()->StateNode->GetFName() : NAME_None;
			if( StateName == TEXT("PlayerCutScene") || StateName == TEXT("DialogScene") || StateName == TEXT("SpecialKill") )
			{
				bRestoreMinQuality = 1;
				Viewport->GetOuterUClient()->MinQuality = 1.0;
			}
		}

		// Setup rendering coords.
		FSceneNode* Frame = Render->CreateMasterFrame( Viewport, ViewLocation, ViewRotation, NULL );

		// Update level audio.
		if( Audio )
		{
			appClock(GLevel->AudioTickCycles);
			Audio->Update( ViewActor->Region, Frame->Coords );
			appUnclock(GLevel->AudioTickCycles);
		}

		// Render.
		Render->PreRender( Frame );
		Viewport->Canvas->Render = Render;
		if( Viewport->Console )
			Viewport->Console->PreRender( Frame );
		Viewport->Canvas->Update( Frame );
		Viewport->Actor->eventPreRender( Viewport->Canvas );
		Frame->YB += Viewport->Canvas->OrgY;
		Frame->Y = Viewport->Canvas->ClipY;
		Frame->ComputeRenderSize();

		check( GFileManager );
		GFileManager->ProxyEnd();
		GFileManager->DisableFileRestart();

		if( Frame->X>0 && Frame->Y>0 && (!Viewport->Console || Viewport->Console->GetDrawWorld()) )
			Render->DrawWorld( Frame );

		Viewport->RenDev->EndFlash();
		Viewport->Actor->eventPostRender( Viewport->Canvas );
		if( Viewport->Console )
		{
			Viewport->Console->PostRender( Frame );
			Viewport->Console->eventPostRender( Viewport->Canvas );
		}
		if( Audio )
			Audio->PostRender( Frame );

#if 0
/* BEGIN BETA VERSION */
		if(GLevel && GLevel->GetLevelInfo() && GLevel->GetLevelInfo()->Game && FString(GLevel->GetLevelInfo()->Game->GetClass()->GetName()) == FString(TEXT("UTIntro")))
		{
			if ( ((AGameInfo*) AGameInfo::StaticClass()->GetDefaultObject())->DemoBuild == 0 )
			{
				// "BETA VERSION" XOR'd with BetaDecoder
				static TCHAR BetaCypher[] = { 67, 4, 50, 41, 108, 125, 82, 27, 46, 55, 121, 25 };
				static TCHAR BetaDecoder[] = { 1, 65, 102, 104, 76, 43, 23, 73, 125, 126, 54, 87, 33, 78, 0 };
				static TCHAR BetaDecoded[] = TEXT("            "); // gets replaced with "BETA VERSION"

				for(INT i=0; BetaDecoded[i]; i++)
						BetaDecoded[i] = BetaCypher[i] ^ BetaDecoder[i];
			
				Frame->Viewport->Canvas->Color = FColor(255,255,255);
				Frame->Viewport->Canvas->CurX=0;
				Frame->Viewport->Canvas->CurY=0;
				Frame->Viewport->Canvas->WrappedPrintf( Frame->Viewport->Canvas->SmallFont, 0, BetaDecoded );
				Frame->Viewport->Canvas->CurX=Frame->Viewport->Canvas->ClipX - 72;
				Frame->Viewport->Canvas->CurY=0;
				Frame->Viewport->Canvas->WrappedPrintf( Frame->Viewport->Canvas->SmallFont, 0, BetaDecoded );
				Frame->Viewport->Canvas->CurX=0;
				Frame->Viewport->Canvas->CurY=Frame->Viewport->Canvas->ClipY - 10;
				Frame->Viewport->Canvas->WrappedPrintf( Frame->Viewport->Canvas->SmallFont, 0, BetaDecoded );
				Frame->Viewport->Canvas->CurX=Frame->Viewport->Canvas->ClipX - 72;
				Frame->Viewport->Canvas->CurY=Frame->Viewport->Canvas->ClipY - 10;
				Frame->Viewport->Canvas->WrappedPrintf( Frame->Viewport->Canvas->SmallFont, 0, BetaDecoded );
			}
		}
/* END BETA VERSION */
#endif

#if 0
		UAnimState* AnimState = Viewport->Actor->ViewTarget ? Viewport->Actor->ViewTarget->GetAnimState() : Viewport->Actor->GetAnimState();
		if( AnimState )
		{
			FLOAT X = 10;
			FLOAT Y = 10;
			FLOAT TextPadding = 5;
			UFont* Font = Frame->Viewport->Canvas->SmallFont;

			Frame->Viewport->Canvas->SetClip( 0, 0, Frame->X, Frame->Y );

			int TextWidth, TextHeight;
			Frame->Viewport->Canvas->WrappedStrLenf( Font, TextWidth, TextHeight, TEXT("TEST") );

			INT Count = 0;
			while( DWI::AnimChannel* Channel = AnimState->FindCommand<DWI::AnimChannel>(Count++) )
			{
				Frame->Viewport->Canvas->CurX = X;
				Frame->Viewport->Canvas->CurY = Y + ((TextHeight + TextPadding) * Count);

				FString Flags = FString::Printf("%c %c %c %c", Channel->Spec.bLoop ? 'L' : ' ', Channel->Stacking() ? 'S' : ' ', Channel->Absolute() ? 'A' : ' ', Channel->Spec.move ? 'M' : ' ');

				Frame->Viewport->Canvas->WrappedPrintf( Font, 0, TEXT("%s: Elapsed %f, Fade %f, AnimSpeed %f, TimeSeconds %f, %s %p"), appFromAnsi(Channel->Spec.AnimName), Channel->ElapsedTime, Channel->FadeTime, Channel->AnimSpeed, Channel->TimeSeconds, *Flags, Channel );
			}

		}
#endif

#if _DEBUG
		FLOAT TextPadding = 5;
		UFont* Font = Frame->Viewport->Canvas->SmallFont;
		AActor* AnimViewActor = Viewport->Actor->ViewTarget ? Viewport->Actor->ViewTarget : Viewport->Actor;
		
		for( INT i=0; i<GLevel->Actors.Num(); i++ )
		{
			AActor* Actor = GLevel->Actors(i);
			if( Actor && Actor->GetAnimState() && Actor->AnimSequence != NAME_None && Actor->AnimSequence != NAME_Static && !Actor->bHidden /*&& Actor->IsA(APawn::StaticClass())*/ )
			{
				//FVector vecPawnView = Actor->Location - AnimViewActor->Location - (CollisionHeight/2)*vect(0,0,1);
				FVector vecPawnView = Actor->Location - AnimViewActor->Location - FVector(0,0,Actor->CollisionHeight);
				//FVector vecPawnView = Actor->Location - AnimViewActor->Location;

				FCoords Coords = GMath.UnitCoords / AnimViewActor->GetViewRotation();

				if ( (vecPawnView | Coords.XAxis) > 0 )
				{
					// range to the pawn
					//FLOAT RangeToTarget = (Actor->Location - AnimViewActor->Location).Size();

					FLOAT fX = (Frame->Viewport->Canvas->ClipX / 2) + ( (vecPawnView | Coords.YAxis)) * ((Frame->Viewport->Canvas->ClipX / 2) / appTan(Viewport->Actor->FovAngle * PI / 360)) / (vecPawnView | Coords.XAxis);
					FLOAT fY = (Frame->Viewport->Canvas->ClipY / 2) + (-(vecPawnView | Coords.ZAxis)) * ((Frame->Viewport->Canvas->ClipX / 2) / appTan(Viewport->Actor->FovAngle * PI / 360)) / (vecPawnView | Coords.XAxis);

					Frame->Viewport->Canvas->Style = STY_Translucent;
					Frame->Viewport->Canvas->Font = Font;
					Frame->Viewport->Canvas->Color.R = 255;
					Frame->Viewport->Canvas->Color.G = 255;
					Frame->Viewport->Canvas->Color.B = 0;

					Frame->Viewport->Canvas->SetClip( 0, 0, Frame->X, Frame->Y );

					Frame->Viewport->Canvas->CurX = fX / 2;
					Frame->Viewport->Canvas->CurY = fY;
					Frame->Viewport->Canvas->WrappedPrintf( Font, 0, TEXT("%s - %s"), Actor->GetName(), *Actor->AnimSequence );

					INT Count = 0;
					while( DWI::AnimChannel* Channel = Actor->AnimState->FindCommand<DWI::AnimChannel>(Count++) )
					{
						FString Flags = FString::Printf(TEXT("%c %c %c %c"), Channel->Spec.bLoop ? 'L' : ' ', Channel->Stacking() ? 'S' : ' ', Channel->Absolute() ? 'A' : ' ', Channel->OverrideMod() ? 'O' : ' ');
						FString Str = FString::Printf(TEXT("%s | Elapsed: %.2f, Fade: %.2f, AnimSpeed: %.2f, Tween: %.2f | %s %p"), Channel->Spec.AnimName, Channel->ElapsedTime, Channel->FadeTime, Channel->AnimSpeed, Channel->Spec.TweenTime, *Flags, Channel);
						
						Frame->Viewport->Canvas->SetClip( 0, 0, Frame->X, Frame->Y );

						INT TextWidth, TextHeight;
						Frame->Viewport->Canvas->WrappedStrLenf( Font, TextWidth, TextHeight, *Str );

						if( Actor->AnimSequence == FName(Channel->Spec.AnimName) )
						{
							Frame->Viewport->Canvas->Color.R = 0;
							Frame->Viewport->Canvas->Color.G = 255;
							Frame->Viewport->Canvas->Color.B = 0;
						}
						else
						{
							Frame->Viewport->Canvas->Color.R = 255;
							Frame->Viewport->Canvas->Color.G = 255;
							Frame->Viewport->Canvas->Color.B = 255;
						}

						Frame->Viewport->Canvas->CurX = fX / 2;
						Frame->Viewport->Canvas->CurY = fY + ((TextHeight + TextPadding) * (Count));
						Frame->Viewport->Canvas->WrappedPrintf( Font, 0, *Str );
					}
				}
			}
		}
#endif

		Viewport->Canvas->Render = 0;
		Render->PostRender( Frame );
		Viewport->Unlock( Blit );
		Render->FinishMasterFrame();

		if( bRestoreMinQuality )
			Viewport->GetOuterUClient()->MinQuality = OrigMinQuality;

#if UNDYING_MEM
		MemMark.Pop();
		DynMark.Pop();
		SceneMark.Pop();
#endif
	}
	ViewActor->Level->LevelAction = SavedAction;

	// Precache now if desired.
	if( Viewport->RenDev->PrecacheOnFlip && !Viewport->bSuspendPrecaching )
	{
		Viewport->RenDev->PrecacheOnFlip = 0;
		if ( !ViewActor->Level->bNeverPrecache )
			Render->Precache( Viewport );
	}

	unguard;
}

void ExportTravel( FOutputDevice& Out, AActor* Actor )
{
	guard(ExportTravel);
	debugf( TEXT("Exporting travelling actor of class %s"), Actor->GetClass()->GetPathName() );//!!xyzzy
	check(Actor);
	if( !Actor->bTravel )
		return;
	Actor->bTravel = 0;
	Out.Logf( TEXT("Class=%s Name=%s\r\n{\r\n"), Actor->GetClass()->GetPathName(), Actor->GetName() );
	TArray<AActor*> Actors;
	for( TFieldIterator<UProperty> It(Actor->GetClass()); It; ++It )
	{
		for( INT Index=0; Index<It->ArrayDim; Index++ )
		{
			TCHAR Value[1024];
			if
			(	(It->PropertyFlags & CPF_Travel)
			&&	It->ExportText( Index, Value, (BYTE*)Actor, &Actor->GetClass()->Defaults(0), 0 ) )
			{
				Out.Log( It->GetName() );
				if( It->ArrayDim!=1 )
					Out.Logf( TEXT("[%i]"), Index );
				Out.Log( TEXT("=") );
				UObjectProperty* Ref = Cast<UObjectProperty>( *It );
				if( Ref && Ref->PropertyClass->IsChildOf(AActor::StaticClass()) )
				{
					UObject* Obj = *(UObject**)( (BYTE*)Actor + It->Offset + Index*It->ElementSize );
					Out.Logf( TEXT("%s\r\n"), Obj ? Obj->GetName() : TEXT("None") );

					if( Obj && Obj->IsA(AActor::StaticClass()) )
					{
						Actors.AddItem((AActor*)Obj);
					}
				}
				Out.Logf( TEXT("%s\r\n"), Value );
			}
		}
	}
	Out.Logf( TEXT("}\r\n") );
	for( INT i = 0; i < Actors.Num(); i++ )
	{
		ExportTravel( Out, Actors(i) );
	}
	unguard;
}

//
// Jumping viewport.
//
void UGameEngine::SetClientTravel( UPlayer* Player, const TCHAR* NextURL, UBOOL bItems, ETravelType TravelType )
{
	guard(UGameEngine::SetClientTravel);
	check(Player);

	UViewport* Viewport    = CastChecked<UViewport>( Player );
	Viewport->TravelURL    = NextURL;
	Viewport->TravelType   = TravelType;
	Viewport->bTravelItems = bItems;

	unguard;
}

/*-----------------------------------------------------------------------------
	Tick.
-----------------------------------------------------------------------------*/

//
// Get tick rate limitor.
//
FLOAT UGameEngine::GetMaxTickRate()
{
	guard(UGameEngine::GetMaxTickRate);
	static UBOOL LanPlay = ParseParam(appCmdLine(),TEXT("lanplay"));
#if ADDITIONS_FIXES
	static UBOOL Share = Client && ParseParam(appCmdLine(),TEXT("share"));
#else
	static UBOOL Share = ParseParam(appCmdLine(),TEXT("share"));
#endif
	if( Share && !Client->ClientMaxTickRate )
		Client->ClientMaxTickRate = 30;
	if( GLevel && GLevel->NetDriver && !GIsClient )
		return Clamp( LanPlay ? GLevel->NetDriver->LanServerMaxTickRate : GLevel->NetDriver->NetServerMaxTickRate, 10, 120 );
	else if( GLevel && GLevel->NetDriver && GLevel->NetDriver->ServerConnection )
		return Share && Client->ClientMaxTickRate > 0 ? Client->ClientMaxTickRate : GLevel->NetDriver->ServerConnection->CurrentNetSpeed/64;
	else if( GLevel && GLevel->DemoRecDriver && !GLevel->DemoRecDriver->ServerConnection )
		return Clamp( LanPlay ? GLevel->NetDriver->LanServerMaxTickRate : GLevel->DemoRecDriver->NetServerMaxTickRate, 10, 120 );
	else
		return 0;
	unguard;
}

//
// Update everything.
//
void UGameEngine::Tick( FLOAT DeltaSeconds )
{
	guard(UGameEngine::Tick);

	// Read or write frame tick for replay.
	if( Replay.Paused() )
		return;
	Replay.SerializeFrameTick( DeltaSeconds );

	GStatStack.Update();

	//INT LocalTickCycles=0;
	//appClock(LocalTickCycles);

	// If all viewports closed, time to exit.
	if( Client && Client->Viewports.Num()==0 )
	{
		debugf( TEXT("All Windows Closed") );
		appRequestExit( 0 );
		return;
	}

	TimeSinceInput += DeltaSeconds;
	if( TimeSinceInput > 360.0 && GLevel->GetLevelInfo()->NetMode == NM_Standalone )
	{
		GLevel->GetLevelInfo()->Pauser = TEXT("System Idle");
	}

	if( GViewport && GViewport->Actor && GViewport->Actor->myHUD )
	{
		if (Replay.Replaying() || Replay.Recording())
			GViewport->Actor->myHUD->ReplayMessage = FString::Printf(TEXT("%s %s: %d"), Replay.Recording() ? TEXT("Recording") : TEXT("Replaying"), *Replay.GetFileName(), Replay.GetFrameNum());
		else
			GViewport->Actor->myHUD->ReplayMessage = TEXT("");
	}

	// If game is paused, release the cursor.
	static UBOOL WasPaused=1;
	if
	(	Client
	&&	Client->Viewports.Num()==1
	&&	GLevel
	&&	!Client->Viewports(0)->IsFullscreen() )
	{
		UBOOL IsPaused
		=	GLevel->GetLevelInfo()->Pauser!=TEXT("")
		||	Client->Viewports(0)->Actor->bShowMenu
		||	Client->Viewports(0)->bShowWindowsMouse;
		if( IsPaused && !WasPaused )
			Client->Viewports(0)->SetMouseCapture( 0, 0, 0 );
		else if( WasPaused && !IsPaused && Client->CaptureMouse )
			Client->Viewports(0)->SetMouseCapture( 1, 1, 1 );
		WasPaused = IsPaused;
	}
	else WasPaused=0;

	// Update subsystems.
	UObject::StaticTick();				
	GCache.Tick();

	// Update the level.
	guard(TickLevel);
	//GameCycles=0;
	//appClock(GameCycles);
	if( GLevel )
	{
		// Decide whether to drop high detail because of frame rate
		if ( Client )
		{
			GLevel->GetLevelInfo()->bDropDetail = (DeltaSeconds > 1.f/Clamp(Client->MinDesiredFrameRate,1.f,100.f));
			GLevel->GetLevelInfo()->bAggressiveLOD = (DeltaSeconds > 1.f/Clamp(Client->MinDesiredFrameRate - 5.f,1.f,100.f));;
		}
		// tick the level
		GLevel->Tick( LEVELTICK_All, DeltaSeconds );
	}
	if( GEntry && GEntry!=GLevel )
		GEntry->Tick( LEVELTICK_All, DeltaSeconds );
	if( Client && Client->Viewports.Num() && Client->Viewports(0)->Actor->GetLevel()!=GLevel )
		Client->Viewports(0)->Actor->GetLevel()->Tick( LEVELTICK_All, DeltaSeconds );
	//appUnclock(GameCycles);
	unguard;

	// Handle server travelling.
	guard(ServerTravel);
	if( GLevel && GLevel->GetLevelInfo()->NextURL!=TEXT("") )
	{
		if( (GLevel->GetLevelInfo()->NextSwitchCountdown-=DeltaSeconds) <= 0.f )
		{
			// Travel to new level, and exit.
			FURL TravelURL = FURL(&LastURL, *GLevel->GetLevelInfo()->NextURL, TRAVEL_Relative);
			TMap<FString,FString> TravelInfo;
			if( GLevel->GetLevelInfo()->NextURL==TEXT("?RESTART") || TravelURL.GetOption(TEXT("loadgame"), 0) )
			{
				TravelInfo = GLevel->TravelInfo;
			}
			else if( GLevel->GetLevelInfo()->bNextItems )
			{
				TravelInfo = GLevel->TravelInfo;
				for( INT i=0; i<GLevel->Actors.Num(); i++ )
				{
					APlayerPawn* P = Cast<APlayerPawn>( GLevel->Actors(i) );
					if( P && P->Player )
					{
						// Export items and self.
						FStringOutputDevice PlayerTravelInfo;
						ExportTravel( PlayerTravelInfo, P );
						for( AActor* Inv=P->Inventory; Inv; Inv=Inv->Inventory )
							ExportTravel( PlayerTravelInfo, Inv );
						TravelInfo.Set( *P->PlayerReplicationInfo->PlayerName, *PlayerTravelInfo );

						// Prevent local ClientTravel from taking place, since it will happen automatically.
						if( Cast<UViewport>( P->Player ) )
							Cast<UViewport>( P->Player )->TravelURL = TEXT("");
					}
				}
			}
			debugf( TEXT("Server switch level: %s"), *GLevel->GetLevelInfo()->NextURL );
			FString Error;
			GLevel->GetLevelInfo()->NextURL = TEXT(""); // moved before Browse to not save NextURL on the server
			Browse( TravelURL, &TravelInfo, Error );
			if( LastURL.HasOption(TEXT("nosave")) )
				LastURL.RemoveOption(TEXT("nosave"));
			return;
		}
	}
	unguard;

	// Handle client travelling.
	guard(ClientTravel);
	if( Client && Client->Viewports.Num() && Client->Viewports(0)->TravelURL!=TEXT("") )
	{
		// Travel to new level, and exit.
		UViewport* Viewport = Client->Viewports( 0 );
		FURL TravelURL = FURL(&LastURL, *Viewport->TravelURL, Viewport->TravelType);
		TMap<FString,FString> TravelInfo;

		// Export items.
		if( appStricmp(*Viewport->TravelURL,TEXT("?RESTART"))==0 || TravelURL.GetOption(TEXT("loadgame"), 0) )
		{
			TravelInfo = GLevel->TravelInfo;
		}
		else if( Viewport->Actor->Health <= 0.0 && Viewport->Actor->Level->NetMode == NM_Standalone )
		{
			return;
		}
		else if( Viewport->bTravelItems && Viewport->Actor->PlayerReplicationInfo )
		{
			debugf( TEXT("Export travel for: %s"), *Viewport->Actor->PlayerReplicationInfo->PlayerName );
			FStringOutputDevice PlayerTravelInfo;
			ExportTravel( PlayerTravelInfo, Viewport->Actor );
			for( AActor* Inv=Viewport->Actor->Inventory; Inv; Inv=Inv->Inventory )
				ExportTravel( PlayerTravelInfo, Inv );
			TravelInfo.Set( *Viewport->Actor->PlayerReplicationInfo->PlayerName, *PlayerTravelInfo );
		}
		FString Error;
		Browse( TravelURL, &TravelInfo, Error );
		if( LastURL.HasOption(TEXT("nosave")) )
			LastURL.RemoveOption(TEXT("nosave"));
		Viewport->TravelURL=TEXT("");

		return;
	}
	unguard;

	// Update the pending level.
	guard(TickPending);
	if( GPendingLevel )
	{
		GPendingLevel->Tick( DeltaSeconds );
		if( GPendingLevel->Error!=TEXT("") )
		{
			// Pending connect failed.
			guard(PendingFailed);
			SetProgress( LocalizeError("ConnectionFailed", TEXT("Engine")), *GPendingLevel->Error, 4.f);
			debugf( NAME_Log, LocalizeError("Pending", TEXT("Engine")), *GPendingLevel->URL.String(), *GPendingLevel->Error );
			// Notify console.
			if( GPendingLevel->FailCode!=TEXT("") && Client && Client->Viewports(0) && Client->Viewports(0)->Console )
				Client->Viewports(0)->Console->eventConnectFailure( GPendingLevel->FailCode, GPendingLevel->FailURL );

			delete GPendingLevel;
			GPendingLevel = NULL;
			unguard;
		}
		else if( GPendingLevel->Success && !GPendingLevel->FilesNeeded && !GPendingLevel->SentJoin )
		{
			// Attempt to load the map.
			FString Error;
			guard(AttemptLoadPending);
			LoadMap( GPendingLevel->URL, GPendingLevel, NULL, Error );
			if( Error!=TEXT("") )
			{
				SetProgress( LocalizeError("ConnectionFailed", TEXT("Engine")), *Error, 4.f );
			}
			else if( !GPendingLevel->LonePlayer )
			{
				// Force Garbage Collection directly after map load.
				// from ut2004, not really needed, is it a safety measure?
				UObject::CollectGarbage(RF_Native | RF_Standalone);

				// Show connecting message, cause precaching to occur.
				GLevel->GetLevelInfo()->LevelAction = LEVACT_Connecting;
				GEntry->GetLevelInfo()->LevelAction = LEVACT_Connecting;
				if( Client )
					Client->Tick();

				// Send join.
				GPendingLevel->SendJoin();
				GPendingLevel->NetDriver = NULL;
				GPendingLevel->DemoRecDriver = NULL;
			}
			unguard;

			// Kill the pending level.
			guard(KillPending);
			delete GPendingLevel;
			GPendingLevel = NULL;
			unguard;
		}
	}
	unguard;

	// Render everything.
	guard(ClientTick);
	//INT LocalClientCycles=0;
	if( Client )
	{
		//appClock(LocalClientCycles);
		//UViewport* Viewport=Client->Viewports(0);
		//Viewport->Actor->eventViewFlash( DeltaSeconds );
		Client->Tick();
		//appUnclock(LocalClientCycles);
	}
	//ClientCycles=LocalClientCycles;
	unguard;

	//appUnclock(LocalTickCycles);
	//TickCycles=LocalTickCycles;

	// Replay input here.
	if( Replay.Replaying() )
	{
		FReplay::FInputEvent IE;
		while( Replay.SerializeFrameInput( IE ) )
		{
			if( IE.State == IST_MAX )
			{
				// Special input functions.
				if( IE.iKey == IK_MouseX )
					GViewport->WindowsMouseX = IE.Delta;
				else if( IE.iKey == IK_MouseY )
					GViewport->WindowsMouseY = IE.Delta;
				else
					Key( GViewport, IE.iKey );
			}
			else
				InputEvent( GViewport, IE.iKey, IE.State, IE.Delta );
		}
	}

	GTicks++;
	unguard;
}

/*-----------------------------------------------------------------------------
	Mouse feedback.
-----------------------------------------------------------------------------*/

//
// Mouse delta while dragging.
//
void UGameEngine::MouseDelta( UViewport* Viewport, DWORD ClickFlags, FLOAT DX, FLOAT DY )
{
	guard(UGameEngine::MouseDelta);
	if
	(	(ClickFlags & MOUSE_FirstHit)
	&&	Client
	&&	Client->Viewports.Num()==1
	&&	GLevel
	&&	!Client->Viewports(0)->IsFullscreen()
	&&	GLevel->GetLevelInfo()->Pauser==TEXT("")
	&&	!Viewport->Actor->bShowMenu
	&&  !Viewport->bShowWindowsMouse )
	{
		Viewport->SetMouseCapture( 1, 1, 1 );
	}
	else if( (ClickFlags & MOUSE_LastRelease) && !Client->CaptureMouse )
	{
		Viewport->SetMouseCapture( 0, 0, 0 );
	}
	unguard;
}

//
// Absolute mouse position.
//
void UGameEngine::MousePosition( UViewport* Viewport, DWORD ClickFlags, FLOAT X, FLOAT Y )
{
	// No replay functionality needed.
	guard(UGameEngine::MousePosition);

	if( Viewport )
	{
		Viewport->WindowsMouseX = X;
		Viewport->WindowsMouseY = Y;
		// added, not in undying
		if( Replay.Recording() )
		{
			Replay.SerializeFrameInput( IK_MouseX, IST_MAX, X );
			Replay.SerializeFrameInput( IK_MouseY, IST_MAX, Y );
		}
	}

	unguard;
}

//
// Mouse clicking.
//
void UGameEngine::Click( UViewport* Viewport, DWORD ClickFlags, FLOAT X, FLOAT Y )
{
	// No replay functionality needed.
	guard(UGameEngine::Click);
	unguard;
}

UBOOL UGameEngine::Key( UViewport* Viewport, EInputKey Key )
{
	// Intercept, and record handled input keys.
	if( UEngine::Key( Viewport, Key ) )
	{
		if( Replay.Recording() )
			Replay.SerializeFrameInput( Key, IST_MAX );
		return true;
	}
	return false;
}

UBOOL UGameEngine::InputEvent( UViewport* Viewport, EInputKey iKey, EInputAction State, FLOAT Delta )
{
	if( Replay.Replaying() && !Replay.InReplay() )
	{
		// Ignore real input, except for replay control keys.
		if( State == IST_Press && iKey == IK_Escape )
			Replay.Stop();
		else if( State == IST_Press && iKey == IK_Pause )
			Replay.Pause( !Replay.Paused() );
		else if( State == IST_Press && iKey == IK_ScrollLock )
			if( Replay.Paused() )
				// Single step.
				Replay.Pause( true, 1 );
			
		return true;
	}
	else if( Replay.Recording() )
	{
		if( State == IST_Press && iKey == IK_Backslash )
		{
			Replay.Stop();
			return true;
		}
	}

	// Intercept, and record handled input keys.
	if( Replay.Recording() )
		Replay.SerializeFrameInput( iKey, State, Delta );
	return UEngine::InputEvent( Viewport, iKey, State, Delta );
}

/*-----------------------------------------------------------------------------
	The End.
-----------------------------------------------------------------------------*/
