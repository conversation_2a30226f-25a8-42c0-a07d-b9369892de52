/*=============================================================================
	Window.cpp: GUI window management code.
	Copyright 1997-1999 Epic Games, Inc. All Rights Reserved.

	Revision history:
		* Created by <PERSON>
=============================================================================*/

#pragma warning( disable : 4201 )
#define STRICT
#pragma pack(push,8)
#include <windows.h>
#include <commctrl.h>
#include <shlobj.h>
#pragma pack(pop)

#include "Core.h"
#include "Window.h"

/*-----------------------------------------------------------------------------
	Globals.
-----------------------------------------------------------------------------*/

WNDPROC WTabControl::SuperProc;
WNDPROC WLabel::SuperProc;
WNDPROC WCustomLabel::SuperProc;
WNDPROC WGroupBox::SuperProc;
WNDPROC WEdit::SuperProc;
WNDPROC WRichEdit::SuperProc;
WNDPROC WListView::SuperProc;		//needed for debugger gk 4/4/02
WNDPROC WListBox::SuperProc;
WNDPROC WCheckListBox::SuperProc;
WNDPROC WTrackBar::SuperProc;
WNDPROC WProgressBar::SuperProc;
WNDPROC WComboBox::SuperProc;
WNDPROC WPropertySheet::SuperProc;
WNDPROC WButton::SuperProc;
WNDPROC WToolTip::SuperProc;
WNDPROC WCoolButton::SuperProc;
WNDPROC WUrlButton::SuperProc;
WNDPROC WCheckBox::SuperProc;
WNDPROC WScrollBar::SuperProc;
WNDPROC WVScrollBar::SuperProc;
WNDPROC WTreeView::SuperProc;
INT WWindow::ModalCount=0;
TArray<WWindow*> WWindow::_Windows;
TArray<WWindow*> WWindow::_DeleteWindows;
TArray<WProperties*> WProperties::PropertiesWindows;
WINDOW_API WLog* GLogWindow=NULL;
WINDOW_API HBRUSH hBrushBlack;
WINDOW_API HBRUSH hBrushWhite;
WINDOW_API HBRUSH hBrushOffWhite;
WINDOW_API HBRUSH hBrushHeadline;
WINDOW_API HBRUSH hBrushStipple;
WINDOW_API HBRUSH hBrushCurrent;
WINDOW_API HBRUSH hBrushDark;
WINDOW_API HBRUSH hBrushGrey;
WINDOW_API HFONT hFontUrl;
WINDOW_API HFONT hFontText;
WINDOW_API HFONT hFontHeadline;
WINDOW_API HINSTANCE hInstanceWindow;
WINDOW_API UBOOL GNotify=0;
WCoolButton* WCoolButton::GlobalCoolButton=NULL;
WINDOW_API UINT WindowMessageOpen;
WINDOW_API UINT WindowMessageMouseWheel;
WINDOW_API NOTIFYICONDATA NID;
#if UNICODE
WINDOW_API NOTIFYICONDATAA NIDA;
WINDOW_API BOOL (WINAPI* Shell_NotifyIconWX)( DWORD dwMessage, PNOTIFYICONDATAW pnid )=NULL;
WINDOW_API BOOL (WINAPI* SHGetSpecialFolderPathWX)( HWND hwndOwner, LPTSTR lpszPath, INT nFolder, BOOL fCreate );
#endif
WINDOW_API HACCEL hAccel = NULL;
WINDOW_API TArray<ACCEL> Accel;

IMPLEMENT_PACKAGE(Window)

/*-----------------------------------------------------------------------------
	Window manager.
-----------------------------------------------------------------------------*/

W_IMPLEMENT_CLASS(WWindow)
W_IMPLEMENT_CLASS(WControl)
W_IMPLEMENT_CLASS(WTabControl)
W_IMPLEMENT_CLASS(WLabel)
W_IMPLEMENT_CLASS(WCustomLabel)
W_IMPLEMENT_CLASS(WGroupBox)
W_IMPLEMENT_CLASS(WButton)
W_IMPLEMENT_CLASS(WListView)		//needed for debugger gk 3/4/02
W_IMPLEMENT_CLASS(WToolTip)
W_IMPLEMENT_CLASS(WCoolButton)
W_IMPLEMENT_CLASS(WUrlButton)
W_IMPLEMENT_CLASS(WComboBox)
W_IMPLEMENT_CLASS(WPropertySheet)
W_IMPLEMENT_CLASS(WEdit)
W_IMPLEMENT_CLASS(WRichEdit)
W_IMPLEMENT_CLASS(WTerminalBase)
W_IMPLEMENT_CLASS(WTerminal)
W_IMPLEMENT_CLASS(WLog)
W_IMPLEMENT_CLASS(WDialog)
W_IMPLEMENT_CLASS(WPasswordDialog)
W_IMPLEMENT_CLASS(WTextScrollerDialog)
W_IMPLEMENT_CLASS(WCrashBoxDialog);
W_IMPLEMENT_CLASS(WTrackBar)
W_IMPLEMENT_CLASS(WProgressBar)
W_IMPLEMENT_CLASS(WListBox)
W_IMPLEMENT_CLASS(WItemBox)
W_IMPLEMENT_CLASS(WCheckListBox)
W_IMPLEMENT_CLASS(WPropertiesBase)
W_IMPLEMENT_CLASS(WDragInterceptor)
W_IMPLEMENT_CLASS(WProperties)
W_IMPLEMENT_CLASS(WObjectProperties)
W_IMPLEMENT_CLASS(WClassProperties)
W_IMPLEMENT_CLASS(WConfigProperties)
W_IMPLEMENT_CLASS(WWizardPage)
W_IMPLEMENT_CLASS(WWizardDialog)
W_IMPLEMENT_CLASS(WEditTerminal)
W_IMPLEMENT_CLASS(WCheckBox)
W_IMPLEMENT_CLASS(WVScrollBar)
W_IMPLEMENT_CLASS(WScrollBar)
W_IMPLEMENT_CLASS(WTreeView)
W_IMPLEMENT_CLASS(WPropertyPage)


UWindowManager::UWindowManager()
	{
		guard(UWindowManager::UWindowManager);

		// Init common controls.
		InitCommonControls();

		// Get addresses of procedures that don't exist in Windows 95.
#if UNICODE
		HMODULE hModShell32 = GetModuleHandle( TEXT("SHELL32.DLL") );
		*(FARPROC*)&Shell_NotifyIconWX       = GetProcAddress( hModShell32, "Shell_NotifyIconW" );
		*(FARPROC*)&SHGetSpecialFolderPathWX = GetProcAddress( hModShell32, "SHGetSpecialFolderPathW" );
#endif

		// Save instance.
		hInstanceWindow = hInstance;

		LoadLibraryX(TEXT("RICHED32.DLL"));

		// Implement window classes.
		IMPLEMENT_WINDOWSUBCLASS(WListBox,TEXT("LISTBOX"));
		IMPLEMENT_WINDOWSUBCLASS(WItemBox,TEXT("LISTBOX"));
		IMPLEMENT_WINDOWSUBCLASS(WCheckListBox,TEXT("LISTBOX"));
		IMPLEMENT_WINDOWSUBCLASS(WTabControl,WC_TABCONTROL);
		IMPLEMENT_WINDOWSUBCLASS(WLabel,TEXT("STATIC"));
		IMPLEMENT_WINDOWSUBCLASS(WGroupBox,TEXT("BUTTON"));
		IMPLEMENT_WINDOWSUBCLASS(WCustomLabel,TEXT("STATIC"));
		IMPLEMENT_WINDOWSUBCLASS(WEdit,TEXT("EDIT"));
		IMPLEMENT_WINDOWSUBCLASS(WRichEdit,TEXT("RICHEDIT"));
		IMPLEMENT_WINDOWSUBCLASS(WComboBox,TEXT("COMBOBOX"));
		IMPLEMENT_WINDOWSUBCLASS(WEditTerminal,TEXT("EDIT"));
		IMPLEMENT_WINDOWSUBCLASS(WButton,TEXT("BUTTON"));
		IMPLEMENT_WINDOWSUBCLASS(WToolTip,TOOLTIPS_CLASS);
		IMPLEMENT_WINDOWSUBCLASS(WCoolButton,TEXT("BUTTON"));
		IMPLEMENT_WINDOWSUBCLASS(WPropertySheet,TEXT("STATIC"));
		IMPLEMENT_WINDOWSUBCLASS(WUrlButton,TEXT("BUTTON"));
		IMPLEMENT_WINDOWSUBCLASS(WCheckBox,TEXT("BUTTON"));
		IMPLEMENT_WINDOWSUBCLASS(WVScrollBar,TEXT("SCROLLBAR"));
		IMPLEMENT_WINDOWSUBCLASS(WScrollBar,TEXT("SCROLLBAR"));
		IMPLEMENT_WINDOWSUBCLASS(WTreeView,WC_TREEVIEW);
		IMPLEMENT_WINDOWSUBCLASS(WTrackBar,TRACKBAR_CLASS);
		IMPLEMENT_WINDOWSUBCLASS(WProgressBar,PROGRESS_CLASS);
		IMPLEMENT_WINDOWCLASS(WTerminal,CS_DBLCLKS);
		IMPLEMENT_WINDOWCLASS(WLog,CS_DBLCLKS);
		IMPLEMENT_WINDOWCLASS(WPasswordDialog,CS_DBLCLKS);
		IMPLEMENT_WINDOWCLASS(WTextScrollerDialog,CS_DBLCLKS);
		IMPLEMENT_WINDOWCLASS(WCrashBoxDialog,CS_DBLCLKS);
		IMPLEMENT_WINDOWCLASS(WProperties,CS_DBLCLKS);
		IMPLEMENT_WINDOWCLASS(WObjectProperties,CS_DBLCLKS);
		IMPLEMENT_WINDOWCLASS(WConfigProperties,CS_DBLCLKS);
		IMPLEMENT_WINDOWCLASS(WClassProperties,CS_DBLCLKS);
		IMPLEMENT_WINDOWCLASS(WWizardDialog,0);
		IMPLEMENT_WINDOWCLASS(WWizardPage,0);
		IMPLEMENT_WINDOWCLASS(WDragInterceptor,CS_DBLCLKS);
		IMPLEMENT_WINDOWCLASS(WPictureButton,CS_DBLCLKS);
		IMPLEMENT_WINDOWCLASS(WPropertyPage,CS_DBLCLKS);
		IMPLEMENT_WINDOWSUBCLASS(WListView,TEXT("SysListView32")); //needed for debugger 3/4/02 gk


		//WC_HEADER (InitCommonControls)
		//WC_TABCONTROL (InitCommonControls)
		//TOOLTIPS_CLASS (InitCommonControls)
		//TRACKBAR_CLASS (InitCommonControls)
		//UPDOWN_CLASS (InitCommonControls)
		//STATUSCLASSNAME (InitCommonControls)
		//TOOLBARCLASSNAME (InitCommonControls)
		//"RichEdit" (RICHED32.DLL)

		// Create brushes.
		hBrushBlack    = CreateSolidBrush( RGB(0,0,0) );
		hBrushWhite    = CreateSolidBrush( RGB(255,255,255) );
		hBrushOffWhite = CreateSolidBrush( RGB(224,224,224) );
		hBrushHeadline = CreateSolidBrush( RGB(200,200,200) );
		hBrushCurrent  = CreateSolidBrush( RGB(0,0,128) );
		hBrushDark     = CreateSolidBrush( RGB(64,64,64) );
		hBrushGrey     = CreateSolidBrush( RGB(128,128,128) );

		// Create stipple brush.
		WORD Pat[8];
		for( INT i = 0; i < 8; i++ )
			Pat[i] = (WORD)(0x5555 << (i & 1));
		HBITMAP Bitmap = CreateBitmap( 8, 8, 1, 1, &Pat );
		check(Bitmap);
		hBrushStipple = CreatePatternBrush(Bitmap);
		DeleteObject(Bitmap);

		// Create fonts.
		HDC hDC       = GetDC( NULL );
#ifndef JAPANESE
		hFontText     = CreateFont( -MulDiv(9/*PointSize*/,  GetDeviceCaps(hDC, LOGPIXELSY), 72), 0, 0, 0, 0, 0, 0, 0, ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH, TEXT("Arial") );
		hFontUrl      = CreateFont( -MulDiv(9/*PointSize*/,  GetDeviceCaps(hDC, LOGPIXELSY), 72), 0, 0, 0, 0, 0, 1, 0, ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH, TEXT("Arial") );
		hFontHeadline = CreateFont( -MulDiv(15/*PointSize*/, GetDeviceCaps(hDC, LOGPIXELSY), 72), 0, 0, FW_BOLD, 1, 1, 0, 0, ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH, TEXT("Arial") );
#else
		hFontText     = (HFONT)( GetStockObject( DEFAULT_GUI_FONT ) );
		hFontUrl      = (HFONT)( GetStockObject( DEFAULT_GUI_FONT ) );
		hFontHeadline = (HFONT)( GetStockObject( DEFAULT_GUI_FONT ) );
#endif
		ReleaseDC( NULL, hDC );

		// Custom window messages.
		WindowMessageOpen       = RegisterWindowMessageX( TEXT("UnrealOpen") );
		WindowMessageMouseWheel = RegisterWindowMessageX( TEXT("MSWHEEL_ROLLMSG") );

		unguard;
	}

UBOOL UWindowManager::Exec( const TCHAR* Cmd, FOutputDevice& Ar )
{
	guard(UWindowManager::Exec);
	return 0;
	unguard;
}

void UWindowManager::Serialize( FArchive& Ar )
{
	guard(UWindowManager::Serialize);
	Super::Serialize( Ar );
	for( INT i=0; i<WWindow::_Windows.Num(); i++ )
		WWindow::_Windows(i)->Serialize( Ar );
	for( INT i=0; i<WWindow::_DeleteWindows.Num(); i++ )
		WWindow::_DeleteWindows(i)->Serialize( Ar );
	unguard;
}

void UWindowManager::Destroy()
{
	guard(UWindowManager::Destroy);
	Super::Destroy();
	check(GWindowManager==this);
	GWindowManager = NULL;
	if( !GIsCriticalError )
		Tick( 0.0 );
	WWindow::_Windows.Empty();
	WWindow::_DeleteWindows.Empty();
	WProperties::PropertiesWindows.Empty();
	unguard;
}

void UWindowManager::Tick( FLOAT DeltaTime )
{
	guard(UWindowManager::Tick);
	while( WWindow::_DeleteWindows.Num() )
	{
		WWindow* W = WWindow::_DeleteWindows( 0 );
		delete W;
		check(WWindow::_DeleteWindows.FindItemIndex(W)==INDEX_NONE);
	}
	unguard;
}

IMPLEMENT_CLASS(UWindowManager);

/*-----------------------------------------------------------------------------
	Functions.
-----------------------------------------------------------------------------*/

WINDOW_API HBITMAP LoadFileToBitmap( const TCHAR* Filename, INT& SizeX, INT& SizeY )
{
	guard(LoadFileToBitmap);
	TArray<BYTE> Bitmap;
	if( appLoadFileToArray(Bitmap,Filename) )
	{
		HDC              hDC = GetDC(NULL);
		BITMAPFILEHEADER* FH = (BITMAPFILEHEADER*)&Bitmap(0);
		BITMAPINFO*       BM = (BITMAPINFO      *)(FH+1);
		BITMAPINFOHEADER* IH = (BITMAPINFOHEADER*)(FH+1);
		RGBQUAD*          RQ = (RGBQUAD         *)(IH+1);
		BYTE*             BY = (BYTE            *)(RQ+((PTRINT)1<<IH->biBitCount));
		SizeX                = IH->biWidth;
		SizeY                = IH->biHeight;
		HBITMAP      hBitmap = CreateDIBitmap( hDC, IH, CBM_INIT, BY, BM, DIB_RGB_COLORS );
		ReleaseDC( NULL, hDC );
		return hBitmap;
	}
	return NULL;
	unguard;
}

WINDOW_API void InitWindowing()
{
	guard(InitWindowing);
	GWindowManager = new UWindowManager;
	GWindowManager->AddToRoot();
	unguard;
}





//gk milestone 2 code
// updated milestone 4
INT_PTR CALLBACK editDlg(HWND hDlg, UINT iMsg, WPARAM wParam, LPARAM lParam)
{

	HWND hwndParent;
	HDC hdc;
	PAINTSTRUCT ps;
	static TCHAR* str;
	HWND richHdc;
	int size;
	TCHAR message[]=TEXT("Dude, You are reaching the max size of the String, any additional data will be lost");
	TCHAR titlebar[]=TEXT("Max Size Reached");
	static bool warningSent;
	static bool locSet=false;
	static RECT winLoc;


	hwndParent=GetParent(hDlg);


	switch (iMsg)
	{

		case WM_INITDIALOG:


			if(locSet)
			{
				SetWindowPos(hDlg,HWND_TOP,winLoc.left,winLoc.top,winLoc.right-winLoc.left,winLoc.bottom-winLoc.top,SWP_SHOWWINDOW);
			}
			
			InvalidateRect(hDlg,NULL,TRUE);
			str=(TCHAR*)lParam;

			richHdc=GetDlgItem(hDlg,IDC_RICHEDIT2);
			SetWindowText(richHdc,str);
			warningSent=false;
			SetTimer(hDlg,NULL,1000,NULL);

			return TRUE;

		case WM_TIMER:
				richHdc=GetDlgItem(hDlg,IDC_RICHEDIT2);
				size=GetWindowTextLength(richHdc);
				if(size>4000&&!warningSent)
				{
					warningSent=true;
					MessageBox(hDlg,message,titlebar,MB_ICONWARNING);
				}
				else
				{
					SetTimer(hDlg,NULL,1000,NULL);
				}
				return true;
		
			
			
			break;

		case WM_PAINT:

			hdc=BeginPaint(hDlg,&ps);
			EndPaint(hDlg,&ps);

		
			return TRUE;

		case WM_SIZE:
				richHdc=GetDlgItem(hDlg,IDC_RICHEDIT2);
				MoveWindow(richHdc,0,0,LOWORD(lParam),HIWORD(lParam),true);
				return true;

		case WM_COMMAND:
			switch LOWORD(wParam)
			{

				case ID_CANCEL:

					GetWindowRect(hDlg,&winLoc);
					locSet=true;
					EndDialog(hDlg,0);
					return true;
			}
			break;

		
		case WM_CLOSE:
			richHdc=GetDlgItem(hDlg,IDC_RICHEDIT2);
			size=GetWindowTextLength(richHdc);
			GetWindowText(richHdc,str,4000);
			locSet=true;
			GetWindowRect(hDlg,&winLoc);

			EndDialog(hDlg,0);
			return true;



	}
	return false;

}

/*-----------------------------------------------------------------------------
	WWindow.
-----------------------------------------------------------------------------*/

#if UNDYING_COMPAT
LONG CALLBACK WWindow::StaticDlgProc( HWND hwndDlg, UINT uMsg, WPARAM wParam, LPARAM lParam )
#else
INT_PTR CALLBACK WWindow::StaticDlgProc( HWND hwndDlg, UINT uMsg, WPARAM wParam, LPARAM lParam )
#endif
{
	guard(WWindow::StaticDlgProc);
	INT i;
	for( i=0; i<_Windows.Num(); i++ )
		if( _Windows(i)->hWnd==hwndDlg )
			break;
	if( i==_Windows.Num() && uMsg==WM_INITDIALOG )
	{
		WWindow* WindowCreate = (WWindow*)lParam;
		check(WindowCreate);
		check(!WindowCreate->hWnd);
		WindowCreate->hWnd = hwndDlg;
		for( i=0; i<_Windows.Num(); i++ )
			if( _Windows(i)==WindowCreate )
				break;
		check(i<_Windows.Num());
	}
	if( i!=_Windows.Num() && !GIsCriticalError )
	{
		_Windows(i)->WndProc( uMsg, wParam, lParam );			
	}

	// Give up cycles.
	//
	::Sleep(0);

	return 0;
	unguard;
}

LRESULT APIENTRY WWindow::StaticWndProc( HWND hWnd, UINT Message, WPARAM wParam, LPARAM lParam )
{
	try
	{
		INT i;
		for( i=0; i<_Windows.Num(); i++ )
			if( _Windows(i)->hWnd==hWnd )
				break;
		if( i==_Windows.Num() && (Message==WM_NCCREATE || Message==WM_INITDIALOG) )
		{
			WWindow* WindowCreate
			=	Message!=WM_NCCREATE
			?	(WWindow*)lParam
			:	(GetWindowLongX(hWnd,GWL_EXSTYLE) & WS_EX_MDICHILD)
			?	(WWindow*)((MDICREATESTRUCT*)((CREATESTRUCT*)lParam)->lpCreateParams)->lParam
			:	(WWindow*)((CREATESTRUCT*)lParam)->lpCreateParams;
			check(WindowCreate);
			check(!WindowCreate->hWnd);
			WindowCreate->hWnd = hWnd;
			for( i=0; i<_Windows.Num(); i++ )
				if( _Windows(i)==WindowCreate )
					break;
			check(i<_Windows.Num());
		}
		if( i==_Windows.Num() || GIsCriticalError )
		{
			// Gets through before WM_NCCREATE: WM_GETMINMAXINFO
			return DefWindowProcX( hWnd, Message, wParam, lParam );
		}
		else
		{
			return _Windows(i)->WndProc( Message, wParam, lParam );			
		}
	}
	catch(...)
	{
		appUnwindf(TEXT("WWindow::StaticWndProc (Message=%x)"), Message);
	}
	return DefWindowProcX( hWnd, Message, wParam, lParam );
}

WNDPROC WWindow::RegisterWindowClass( const TCHAR* Name, DWORD Style )
{
	guard(WWindow::RegisterWindowClass);
#if UNICODE
	if( GUnicodeOS )
	{
		WNDCLASSEXW Cls;
		appMemzero( &Cls, sizeof(Cls) );
		Cls.cbSize			= sizeof(Cls);
		Cls.style			= Style;
		Cls.lpfnWndProc		= StaticWndProc;
		Cls.hInstance		= hInstanceWindow;
		Cls.hIcon			= LoadIconIdX(hInstanceWindow,(GIsEditor?IDICON_Editor:IDICON_Mainframe));
		Cls.lpszClassName	= Name;
		Cls.hIconSm			= LoadIconIdX(hInstanceWindow,(GIsEditor?IDICON_Editor:IDICON_Mainframe));
		verify(RegisterClassExW( &Cls ));
	}
	else
#endif
	{
		WNDCLASSEXA Cls;
		appMemzero( &Cls, sizeof(Cls) );
		Cls.cbSize			= sizeof(Cls);
		Cls.style			= Style;
		Cls.lpfnWndProc		= StaticWndProc;
		Cls.hInstance		= hInstanceWindow;
		Cls.hIcon			= LoadIconIdX(hInstanceWindow,(GIsEditor?IDICON_Editor:IDICON_Mainframe));
		Cls.lpszClassName	= TCHAR_TO_ANSI(Name);
		Cls.hIconSm			= LoadIconIdX(hInstanceWindow,(GIsEditor?IDICON_Editor:IDICON_Mainframe));
		verify(RegisterClassExA( &Cls ));
	}
	return NULL;
	unguard;
}

#if WIN_OBJ
void WWindow::Destroy()
{
	guard(WWindow::Destroy);
	Super::Destroy();
	MaybeDestroy();
	m_bShow = FALSE;
	WWindow::_DeleteWindows.RemoveItem( this );
	unguard;
}
#else
WWindow::~WWindow()
{
	guard(WWindow:;~WWindow);
	MaybeDestroy();
	WWindow::_DeleteWindows.RemoveItem( this );
	unguard;
}
#endif

// Accessors.
FRect WWindow::GetClientRect() const
{
	RECT R;
	::GetClientRect( hWnd, &R );
	return FRect( R );
}

void WWindow::MoveWindow( FRect R, UBOOL bRepaint )
{
	::MoveWindow( hWnd, R.Min.X, R.Min.Y, R.Width(), R.Height(), bRepaint );
}

void WWindow::MoveWindow( int Left, int Top, int Width, int Height, UBOOL bRepaint )
{
	::MoveWindow( hWnd, Left, Top, Width, Height, bRepaint );
}

FRect WWindow::GetWindowRect() const
{
	RECT R;
	::GetWindowRect( hWnd, &R );
	return OwnerWindow ? OwnerWindow->ScreenToClient(R) : FRect(R);
}

FPoint WWindow::ClientToScreen( const FPoint& InP )
{
	POINT P;
	P.x = InP.X;
	P.y = InP.Y;
	::ClientToScreen( hWnd, &P );
	return FPoint( P.x, P.y );
}

FPoint WWindow::ScreenToClient( const FPoint& InP )
{
	POINT P;
	P.x = InP.X;
	P.y = InP.Y;
	::ScreenToClient( hWnd, &P );
	return FPoint( P.x, P.y );
}

FRect WWindow::ClientToScreen( const FRect& InR )
{
	return FRect( ClientToScreen(InR.Min), ClientToScreen(InR.Max) );
}

FRect WWindow::ScreenToClient( const FRect& InR )
{
	return FRect( ScreenToClient(InR.Min), ScreenToClient(InR.Max) );
}

FPoint WWindow::GetCursorPos()
{
	FPoint Mouse;
	::GetCursorPos( Mouse );
	return ScreenToClient( Mouse );
}

void WWindow::Show( UBOOL Show )
{
	guard(WWindow::Show);
	m_bShow = Show;
	ShowWindow( hWnd, Show ? ::IsIconic(hWnd) ? SW_RESTORE : SW_SHOW : SW_HIDE );
	unguard;
}

// WWindow interface.
void WWindow::Serialize( FArchive& Ar )
{
	guard(WWindow::Serialize);
	//!!UObject interface.
	//!!Super::Serialize( Ar );
	Ar << PersistentName;
	unguard;
}

const TCHAR* WWindow::GetPackageName()
{
	return TEXT("Window");
}

void WWindow::DoDestroy()
{
	guard(WWindow::DoDestroy);
	if( NotifyHook )
		NotifyHook->NotifyDestroy( this );
	if( hWnd )
		DestroyWindow( hWnd );
	_Windows.RemoveItem( this );
	unguard;
}

LRESULT WWindow::WndProc( UINT Message, WPARAM wParam, LPARAM lParam )
{
	guard(WWindow::WndProc);
	try
	{
		LastwParam = wParam;
		LastlParam = lParam;

		// Message snoop.
		if( Snoop )
		{
			guard(Snoop);
			if( Message==WM_CHAR )
				Snoop->SnoopChar( this, wParam );
			else if( Message==WM_KEYDOWN )
				Snoop->SnoopKeyDown( this, wParam );
			else if( Message==WM_LBUTTONDOWN )
				Snoop->SnoopLeftMouseDown( this, FPoint(LOWORD(lParam),HIWORD(lParam)) );
			else if( Message==WM_RBUTTONDOWN )
				Snoop->SnoopRightMouseDown( this, FPoint(LOWORD(lParam),HIWORD(lParam)) );
			unguard;
		}

		// Special multi-window activation handling.
		if( !MdiChild && !ModalCount )
		{
			guard(Special);
			static UBOOL AppActive=0;
			if( Message==WM_ACTIVATEAPP )
			{
				AppActive = wParam;
				SendMessageX( hWnd, WM_NCACTIVATE, wParam, 0 );
			}
			else if( Message==WM_NCACTIVATE && AppActive && !wParam )
			{
				return 1;
			}
			unguard;
		}

		// Message processing.
		if( Message==WM_DESTROY )
		{
			guard(WM_DESTROY);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_DESTROY\n"));
#endif
			OnDestroy();
			unguard;
		}
		else if( Message==WM_DRAWITEM )
		{
			guard(WM_DRAWITEM);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_DRAWITEM\n"));
#endif
			DRAWITEMSTRUCT* Info = (DRAWITEMSTRUCT*)lParam;
			for( INT i=0; i<Controls.Num(); i++ )
				if( ((WWindow*)Controls(i))->hWnd==Info->hwndItem )
					{((WWindow*)Controls(i))->OnDrawItem(Info); break;}
			return 1;
			unguard;
		}
		else if( Message==WM_MEASUREITEM )
		{
			guard(WM_MEASUREITEM);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_MEASUREITEM\n"));
#endif
			MEASUREITEMSTRUCT* Info = (MEASUREITEMSTRUCT*)lParam;
			for( INT i=0; i<Controls.Num(); i++ )
				if( ((WWindow*)Controls(i))->ControlId==Info->CtlID )
					{((WWindow*)Controls(i))->OnMeasureItem(Info); break;}
			return 1;
			unguard;
		}
		else if( Message==WM_CLOSE )
		{
			guard(WM_CLOSE);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_CLOSE\n"));
#endif
			OnClose();
			unguard;
		}
		else if( Message==WM_CHAR )
		{
			guard(WM_CHAR);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_CHAR\n"));
#endif
			OnChar( wParam );
			unguard;
		}
		else if( Message==WM_KEYDOWN )
		{
			guard(WM_KEYDOWN);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_KEYDOWN\n"));
#endif
			OnKeyDown( wParam );
			unguard;
		}
		else if( Message==WM_PAINT )
		{
			guard(WM_PAINT);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_PAINT\n"));
#endif
			OnPaint();
			unguard;
		}
		else if( Message==WM_CREATE )
		{
			guard(WM_CREATE);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_CREATE\n"));
#endif
			OnCreate();
			unguard;
		}
		else if( Message==WM_TIMER )
		{
			guard(WM_TIMER);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_TIMER\n"));
#endif
			OnTimer();
			unguard;
		}
		else if( Message==WM_INITDIALOG )
		{
			guard(WM_INITDIALOG);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_INITDIALOG\n"));
#endif
			OnInitDialog();
			unguard;
		}
		else if( Message==WM_ENTERIDLE )
		{
			guard(WM_ENTERIDLE);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_ENTERIDLE\n"));
#endif
			OnEnterIdle();
			unguard;
		}
		else if( Message==WM_SETFOCUS )
		{
			guard(WM_SETFOCUS);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_SETFOCUS\n"));
#endif
			OnSetFocus( (HWND)wParam );
			unguard;
		}
		else if( Message==WM_ACTIVATE )
		{
			guard(WM_ACTIVATE);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_ACTIVATE\n"));
#endif
			OnActivate( LOWORD(wParam)!=0 );
			unguard;
		}
		else if( Message==WM_KILLFOCUS )
		{
			guard(WM_KILLFOCUS);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_KILLFOCUS\n"));
#endif
			OnKillFocus( (HWND)wParam );
			unguard;
		}
		else if( Message==WM_SIZE )
		{
			guard(WM_SIZE);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_SIZE\n"));
#endif
			OnSize( wParam, LOWORD(lParam), HIWORD(lParam) );
			SaveWindowPos();
			unguard;
		}
		else if( Message==WM_MOVE )
		{
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_MOVE\n"));
#endif
			OnMove( LOWORD(lParam), HIWORD(lParam) );
			SaveWindowPos();
		}
		else if( Message==WM_PASTE )
		{
			guard(WM_PASTE);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_PASTE\n"));
#endif
			OnPaste();
			unguard;
		}
		else if( Message==WM_SHOWWINDOW )
		{
			guard(WM_SHOWWINDOW);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_SHOWWINDOW\n"));
#endif
			OnShowWindow( wParam );
			unguard;
		}
		else if( Message==WM_COPYDATA )
		{
			guard(WM_COPYDATA);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_COPYDATA\n"));
#endif
			OnCopyData( (HWND)wParam, (COPYDATASTRUCT*)lParam );
			unguard;
		}
		else if( Message==WM_CAPTURECHANGED )
		{
			guard(WM_CAPTURECHANGED);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_CAPTURECHANGED\n"));
#endif
			OnReleaseCapture();
			unguard;
		}
		else if( Message==WM_MDIACTIVATE )
		{
			guard(WM_MDIACTIVATE);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_MDIACTIVATE\n"));
#endif
			OnMdiActivate( (HWND)lParam==hWnd );
			unguard;
		}
		else if( Message==WM_MOUSEMOVE )
		{
			guard(WM_MOUSEMOVE);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_MOUSEMOVE\n"));
#endif
			OnMouseMove( wParam, FPoint(LOWORD(lParam), HIWORD(lParam)) );
			unguard;
		}
		else if( Message==WM_LBUTTONDOWN )
		{
			guard(WM_LBUTTONDOWN);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_LBUTTONDOWN\n"));
#endif
			OnLeftButtonDown();
			unguard;
		}
		else if( Message==WM_RBUTTONDOWN )
		{
			guard(WM_RBUTTONDOWN);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_RBUTTONDOWN\n"));
#endif
			OnRightButtonDown();
			unguard;
		}
		else if( Message==WM_LBUTTONUP )
		{
			guard(WM_LBUTTONUP);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_LBUTTONUP\n"));
#endif
			OnLeftButtonUp();
			unguard;
		}
		else if( Message==WM_RBUTTONUP )
		{
			guard(WM_RBUTTONUP);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_RBUTTONUP\n"));
#endif
			OnRightButtonUp();
			unguard;
		}
		else if( Message==WM_CUT )
		{
			guard(WM_CUT);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_CUT\n"));
#endif
			OnCut();
			unguard;
		}
		else if( Message==WM_COPY )
		{
			guard(WM_COPY);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_COPY\n"));
#endif
			OnCopy();
			unguard;
		}
		else if( Message==WM_UNDO )
		{
			guard(WM_UNDO);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_UNDO\n"));
#endif
			OnUndo();
			unguard;
		}
		else if( Message==WM_SETCURSOR )
		{
			guard(WM_SETCURSOR);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_SETCURSOR\n"));
#endif
			if( OnSetCursor() )
				return 1;
			unguard;
		}
		else if( Message==WM_NOTIFY )
		{
			guard(WM_NOTIFY);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_NOTIFY\n"));
#endif
			for( INT i=0; i<Controls.Num(); i++ )
				if(wParam==((WWindow*)Controls(i))->ControlId
						&& ((WWindow*)Controls(i))->InterceptControlCommand(Message,wParam,lParam) )
					return 1;
			OnCommand( wParam );
			unguard;
		}
		else if( Message==WM_VSCROLL )
		{
			guard(WM_VSCROLL);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_VSCROLL\n"));
#endif
			OnVScroll( wParam, lParam );
			unguard;
		}
		else if( Message==WM_KEYUP)
		{
			guard(WM_KEYU);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_KEYUP\n"));
#endif
			OnKeyUp( wParam, lParam );
			unguard;
		}
		else if( Message == WM_MOUSEWHEEL )
		{
			guard(WM_MOUSEWHEEL);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_MOUSEWHEEL\n"));
#endif
			SWORD zDelta = HIWORD(wParam);
			//if( GIsEditor )
			{
				if( zDelta < 0 )
					OnVScroll( MAKEWPARAM(SB_LINEDOWN, 0), WM_MOUSEWHEEL );// Hack to make scrollbar working in 2DShapeEditor.
				else if( zDelta > 0 )
					OnVScroll( MAKEWPARAM(SB_LINEUP, 0), WM_MOUSEWHEEL );
			}
			unguard;
 		}
		else if( Message==WM_COMMAND || Message==WM_HSCROLL )
		{
			guard(WM_HSCROLL);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_COMMAND\n"));
#endif
			for( INT i=0; i<Controls.Num(); i++ )
				if((HWND)lParam==((WWindow*)Controls(i))->hWnd
						&& ((WWindow*)Controls(i))->InterceptControlCommand(Message,wParam,lParam) )
					return 1;
			// stijn: we should only pass the low word here. The high word indicates
			// whether the command was generated through an accelerator table
			OnCommand( LOWORD(wParam) ); 
			// stijn: ugly hack here. Docking and undocking destroys the window.
			// Normally, we call the default proc after dispatching a message, but
			// in this case this is not safe since CallDefaultProc is a virtual func.
			// The CallDefaultProc dispatch will therefore dereference the this pointer
			// which becomes invalid after docking
			if (wParam == 40333) // IDMN_MB_DOCK - declared in UnrealEd resources
				return 1;
			unguard;
		}
		else if( Message==WM_SYSCOMMAND )
		{
			guard(WM_SYSCOMMAND);
#if DEBUG_DLG
			OutputDebugString(TEXT("DLG:	WM_SYSCOMMAND\n"));
#endif
			if( OnSysCommand( wParam ) )
				return 1;
			unguard;
		}
#if DEBUG_DLG
		OutputDebugString(TEXT("DLG:	WM_UNKNOWN\n"));
#endif
		return CallDefaultProc( Message, wParam, lParam );
	}
	catch( ... )
	{
		// This exception prevents the message from being routed to the default proc.
		return 0;
	}
	unguard;
}

#if UNDYING_COMPAT_ED
INT WWindow::CallDefaultProc( UINT Message, WPARAM wParam, LPARAM lParam )
#else
LRESULT WWindow::CallDefaultProc( UINT Message, WPARAM wParam, LPARAM lParam )
#endif
{
	if( MdiChild )
		return DefMDIChildProcX( hWnd, Message, wParam, lParam );
	else
		return DefWindowProcX( hWnd, Message, wParam, lParam );
}

UBOOL WWindow::InterceptControlCommand( UINT Message, WPARAM wParam, LPARAM lParam )
{
	return 0;
}

FString WWindow::GetText()
{
	guard(WWindow::GetText);
	check(hWnd);
	INT Length = GetLength();
#if UNICODE
	if( GUnicode && !GUnicodeOS )
	{
		ANSICHAR* ACh = (ANSICHAR*)appAlloca((Length+1)*sizeof(ANSICHAR));
		SendMessageA( hWnd, WM_GETTEXT, Length+1, (LPARAM)ACh );
		return appFromAnsi(ACh);
	}
	else
#endif
	{
		TCHAR* Text = (TCHAR*)appAlloca((Length+1)*sizeof(TCHAR));
		SendMessage( hWnd, WM_GETTEXT, Length+1, (LPARAM)Text );
		return Text;
	}
	unguard;
}

void WWindow::SetText( const TCHAR* Text )
{
	guard(WWindow::SetText);
	check(hWnd);
	SendMessageLX( hWnd, WM_SETTEXT, 0, Text );
	unguard;
}

INT WWindow::GetLength()
{
	guard(WWindow::GetLength);
	check(hWnd);
	return SendMessageX( hWnd, WM_GETTEXTLENGTH, 0, 0 );
	unguard;
}

void WWindow::SetNotifyHook( FNotifyHook* InNotifyHook )
{
	guard(WWindow::SetNotifyHook);
	NotifyHook = InNotifyHook;
	unguard;
}

// WWindow notifications.
void WWindow::OnCopyData( HWND hWndSender, COPYDATASTRUCT* CD ) {}
void WWindow::OnSetFocus( HWND hWndLosingFocus ) {}
void WWindow::OnKillFocus( HWND hWndGaininFocus ) {}
void WWindow::OnSize( DWORD Flags, INT NewX, INT NewY ) {}
void WWindow::OnMove( INT NewX, INT NewY ) {}
void WWindow::OnCommand( INT Command ) {}
INT WWindow::OnSysCommand( INT Command ) { return 0; }
void WWindow::OnActivate( UBOOL Active ) {}
void WWindow::OnChar( TCHAR Ch ) {}
void WWindow::OnKeyDown( TCHAR Ch ) {}
void WWindow::OnCut() {}
void WWindow::OnCopy() {}
void WWindow::OnPaste() {}
void WWindow::OnShowWindow( UBOOL bShow ) {}
void WWindow::OnUndo() {}
void WWindow::OnVScroll( WPARAM wParam, LPARAM lParam ) {}
void WWindow::OnKeyUp( WPARAM wParam, LPARAM lParam ) {}
void WWindow::OnPaint() {}
void WWindow::OnCreate() {}
void WWindow::OnDrawItem( DRAWITEMSTRUCT* Info ) {}
void WWindow::OnMeasureItem( MEASUREITEMSTRUCT* Info ) {}
void WWindow::OnInitDialog() {}
void WWindow::OnEnterIdle() {}
void WWindow::OnMouseEnter() {}
void WWindow::OnMouseLeave() {}
void WWindow::OnMouseHover() {}
void WWindow::OnTimer() {}
void WWindow::OnReleaseCapture() {}
void WWindow::OnMdiActivate( UBOOL Active ) {}
void WWindow::OnMouseMove( DWORD Flags, FPoint Location ) {}
void WWindow::OnLeftButtonDown() {}
void WWindow::OnRightButtonDown() {}
void WWindow::OnLeftButtonUp() {}
void WWindow::OnRightButtonUp() {}
void WWindow::OnFinishSplitterDrag( WDragInterceptor* Drag, UBOOL Success ) {}
INT WWindow::OnSetCursor() { return 0; }

void WWindow::OnClose()
{
	guard(WWindow::OnClose);
	if( MdiChild )
		SendMessage( OwnerWindow->hWnd, WM_MDIDESTROY, (WPARAM)hWnd, 0 );
	else
		DestroyWindow( hWnd );
	unguard;
}

void WWindow::OnDestroy()
{
	guard(WWindow::OnDestroy);
	check(hWnd);
	SaveWindowPos();
	_Windows.RemoveItem( this );
	hWnd = NULL;
	unguard;
}

// WWindow functions.
void WWindow::SaveWindowPos()
{
	guard(WWindow::SaveWindowPos);
	if( PersistentName!=NAME_None && PersistentName.IsValid() )
	{
		RECT RR;
		::GetWindowRect(hWnd, &RR);
		FRect R = FRect(RR);
		UBOOL Maximized = IsZoomed(hWnd);
		if( GetWindowLong(hWnd, GWL_STYLE) & WS_MAXIMIZEBOX )
			GConfig->SetBool( TEXT("WindowPositions"), *(FString(*PersistentName)+TEXT(".Maximized")), Maximized );
		if( !Maximized && !IsIconic(hWnd) )
		{
			GConfig->SetString( TEXT("WindowPositions"), *PersistentName, *FString::Printf( TEXT("(X=%i,Y=%i,XL=%i,YL=%i)"), R.Min.X, R.Min.Y, R.Width(), R.Height() ) );
			GConfig->Flush(0);
		}
	}
	unguard;
}

void WWindow::MaybeDestroy()
{
	guard(WWindow::MaybeDestroy);
	if( !Destroyed )
	{
		Destroyed=1;
		DoDestroy();
	}
	unguard;
}

void WWindow::_CloseWindow()
{
	guard(WWindow::_CloseWindow);
	check(hWnd);
	DestroyWindow( hWnd );
	unguard;
}

void WWindow::SetFont( HFONT hFont )
{
	guard(WWindow::SetFont);
	SendMessageX( hWnd, WM_SETFONT, (WPARAM)hFont, MAKELPARAM(0,0) );
	unguard;
}

void WWindow::PerformCreateWindowEx( DWORD dwExStyle, LPCTSTR lpWindowName, DWORD dwStyle, INT x, INT y, INT nWidth, INT nHeight, HWND hWndParent, HMENU hMenu, HINSTANCE hInstanceUnused )
{
	guard(PerformCreateWindowEx);
	check(hWnd==NULL);

	// Retrieve remembered position.
	TCHAR Pos[256];
	if
	(	PersistentName!=NAME_None 
	&&	GConfig->GetString( TEXT("WindowPositions"), *PersistentName, Pos, ARRAY_COUNT(Pos) ) )
	{
		// Get saved position.
		Parse( Pos, TEXT("X="), x );
		Parse( Pos, TEXT("Y="), y );
		if( dwStyle & WS_SIZEBOX )
		{
			Parse( Pos, TEXT("XL="), nWidth );
			Parse( Pos, TEXT("YL="), nHeight );
		}

		// Count identical windows already opened.
		INT Count=0;
		for( INT i=0; i<_Windows.Num(); i++ )
		{
			Count += _Windows(i)->PersistentName==PersistentName;
		}
		if( Count )
		{
			// Move away.
			x += Count*16;
			y += Count*16;
		}

		// Clip size to screen.
		RECT Desktop;
		::GetWindowRect( GetDesktopWindow(), &Desktop );
		if( dwStyle & WS_SIZEBOX )
		{
			nWidth = Min((int)(Desktop.right - Desktop.left), nWidth);
			nHeight = Min((int)(Desktop.bottom - Desktop.top), nHeight);
		}
		if( x+nWidth  > Desktop.right  ) x = Desktop.right  - nWidth;
		if( y+nHeight > Desktop.bottom ) y = Desktop.bottom - nHeight;
		x = Max((int)(Desktop.left), x);
		y = Max((int)(Desktop.top), y);
	}
	if( PersistentName != NAME_None && (dwStyle & WS_MAXIMIZEBOX) )
	{
		UBOOL Maximized = FALSE;
		GConfig->GetBool( TEXT("WindowPositions"), *(FString(*PersistentName)+TEXT(".Maximized")), Maximized );
		if( Maximized )
			dwStyle |= WS_MAXIMIZE;
	}

	// Create window.
	_Windows.AddItem( this );
	TCHAR ClassName[256];
	GetWindowClassName( ClassName );
	//hinstance must match window class hinstance!!
	HWND hWndCreated = TCHAR_CALL_OS(CreateWindowEx(dwExStyle,ClassName,lpWindowName,dwStyle,x,y,nWidth,nHeight,hWndParent,hMenu,hInstanceWindow,this),CreateWindowExA(dwExStyle,TCHAR_TO_ANSI(ClassName),TCHAR_TO_ANSI(lpWindowName),dwStyle,x,y,nWidth,nHeight,hWndParent,hMenu,hInstanceWindow,this));
	if( !hWndCreated )
		appErrorf( TEXT("CreateWindowEx failed: %s"), appGetSystemErrorMessage() );
	check(hWndCreated);
	check(hWndCreated==hWnd);
	unguard;
}

void WWindow::SetRedraw( UBOOL Redraw )
{
	guard(WWindow::SetRedraw);
	if( !m_bShow )
		return;
	SendMessageX( hWnd, WM_SETREDRAW, Redraw, 0 );
	if( Redraw )
		RedrawWindow(hWnd, NULL, NULL, RDW_UPDATENOW | RDW_FRAME | RDW_INVALIDATE | RDW_ALLCHILDREN);
	unguard;
}

// Used in the editor ... used to draw window edges in custom colors.
void WWindow::MyDrawEdge( HDC hdc, LPRECT qrc, UBOOL bRaised )
{
	guard(WWindow::MyDrawEdge);

	HPEN penOld, penRaised = CreatePen( PS_SOLID, 1, RGB(159,159,159) ),
		penSunken = CreatePen( PS_SOLID, 1, RGB(106,106,106) );
	HDC	hDC = GetDC( hWnd );

	RECT rc = *qrc;
	rc.right -= 1;
	rc.bottom -= 1;

	penOld = (HPEN)SelectObject( hDC, (bRaised ? penRaised : penSunken ) );
	::MoveToEx( hDC, rc.left, rc.top, NULL );
	::LineTo( hDC, rc.right, rc.top );
	::MoveToEx( hDC, rc.left, rc.top, NULL );
	::LineTo( hDC, rc.left, rc.bottom);
	SelectObject( hDC, penOld );

	penOld = (HPEN)SelectObject( hDC, (bRaised ? penSunken : penRaised ) );
	::MoveToEx( hDC, rc.right, rc.bottom, NULL );
	::LineTo( hDC, rc.right, rc.top );
	::MoveToEx( hDC, rc.right, rc.bottom, NULL );
	::LineTo( hDC, rc.left, rc.bottom );
	SelectObject( hDC, penOld );

	DeleteObject( penRaised );
	DeleteObject( penSunken );
	::ReleaseDC( hWnd, hDC );

	unguard;
}



/*-----------------------------------------------------------------------------
	The End.
-----------------------------------------------------------------------------*/
