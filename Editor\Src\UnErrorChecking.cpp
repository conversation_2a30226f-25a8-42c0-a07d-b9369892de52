/*
UnErrorChecking.cpp
Actor Error checking functions
	Copyright 2001 Epic Games, Inc. All Rights Reserved.

Revision history:
	* Created by <PERSON>
=============================================================================*/

#include "EditorPrivate.h"

void ALevelInfo__CheckForErrors(ALevelInfo* LevelInfo)
{
	guard(ALevelInfo::CheckForErrors);

	if ( LevelInfo->GetLevel()->GetLevelInfo() != LevelInfo )
		GWarn->Logf( *FString::Printf(TEXT("Duplicate level info!") ) );
	else
	{
		//if ( !Screenshot )
		//	GWarn->Logf( *FString::Printf(TEXT("No screenshot for this level!") ) );
		if ( LevelInfo->Title.Len() == 0 )
			GWarn->Logf( *FString::Printf(TEXT("No title for this level!") ) );
	}

	INT NumLights = 0;
	for ( INT i=0; i<LevelInfo->GetLevel()->Actors.Num(); i++)
	{
		ALight *A = Cast<ALight>(LevelInfo->GetLevel()->Actors(i)); 
		if ( A && !A->bDeleteMe )
			NumLights++;
	}
	if ( NumLights > 1500 )
		GWarn->Logf( *FString::Printf(TEXT("Level has an excessive number of lights - may affect performance.") ) );

	unguard;
}

void AActor__CheckForErrors(AActor* Actor)
{
	guard(AActor::CheckForErrors);

	//if ( bObsolete )
	//{
	//	GWarn->Logf( *FString::Printf(TEXT("%s is obsolete and must be removed!!!"), GetName() ) );
	//	return;
	//}
	if ( Actor->GetClass()->GetDefaultActor()->bStatic && !Actor->bStatic )
		GWarn->Logf( *FString::Printf(TEXT("%s bStatic false, but is bStatic by default - map will fail in netplay"), Actor->GetName() ) );
	if ( Actor->GetClass()->GetDefaultActor()->bNoDelete && !Actor->bNoDelete )
		GWarn->Logf( *FString::Printf(TEXT("%s bNoDelete false, but is bNoDelete by default - map will fail in netplay"), Actor->GetName() ) );

	// check if placed in same location as another actor of same class
	if ( !(Actor->GetClass()->ClassFlags & CLASS_Abstract) && !Actor->IsBrush() && !Actor->IsA(ACamera::StaticClass()) )
		for ( INT i=0; i<Actor->GetLevel()->Actors.Num(); i++)
		{
			AActor *A = Actor->GetLevel()->Actors(i); 
			if ( A && (A != Actor) && ((A->Location - Actor->Location).SizeSquared() < 1.f) && (A->GetClass() == Actor->GetClass()) && (A->Rotation == Actor->Rotation)
				&& (A->DrawType == Actor->DrawType) && ((Actor->DrawType != DT_Mesh) || (Actor->Mesh == A->Mesh)) )
				GWarn->Logf( *FString::Printf(TEXT("%s in same location as %s"), Actor->GetName(), A->GetName() ) );
		}

	if ( !Actor->bStatic && (Actor->Mass == 0.f) )
		GWarn->Logf( *FString::Printf(TEXT("%s mass must be greater than zero!"), Actor->GetName() ) );

	// Check missing resources
	if( Actor->DrawType == DT_Sprite && !Actor->Texture )
		GWarn->Logf( *FString::Printf(TEXT("%s : NULL texture reference (DT_Sprite)"), Actor->GetName() ) );
	if( Actor->DrawType == DT_Mesh && !Actor->Mesh )
		GWarn->Logf( *FString::Printf(TEXT("%s : NULL mesh reference (DT_Mesh)"), Actor->GetName() ) );

	// Check for obsolete light effects.

	//if(Actor->LightEffect == LE_WateryShimmer || Actor->LightEffect == LE_FireWaver || Actor->LightEffect == LE_TorchWaver || Actor->LightEffect == LE_CloudCast)
	//	GWarn->Logf(*FString::Printf(TEXT("%s : Obsolete LightEffect"),Actor->GetName()));

	unguard;
}

void ABrush__CheckForErrors(ABrush* Brush)
{
	guard(ABrush::CheckForErrors);

	// NOTE : don't report NULL texture references on the builder brush - it doesn't matter there
	if( Brush->Brush && Brush != Brush->GetLevel()->Brush() )
	{
		// Check for missing textures
		for( INT x = 0 ; x < Brush->Brush->Polys->Element.Num() ; ++x )
		{
			FPoly* Poly = &(Brush->Brush->Polys->Element(x));
			if( !Poly->Texture )
			{
				GWarn->Logf( *FString::Printf(TEXT("%s : Brush has NULL texture reference(s)"), Brush->GetName() ) );
				break;
			}
		}
	}

	AActor__CheckForErrors(Brush);

	unguard;
}

void APickup__CheckForErrors(APickup* Pickup)
{
	guard(APickup::CheckForErrors);

	AActor__CheckForErrors(Pickup);

    if( !Pickup->MyMarker )
	{
		GWarn->Logf( TEXT("No inventory spot for this pickup!") );
        return;
	}

	FCheckResult Hit(1.f);
	Pickup->GetLevel()->SingleLineCheck( Hit, Pickup, Pickup->MyMarker->Location, Pickup->Location, TRACE_AllColliding );

	if ( Hit.Actor )
		GWarn->Logf( TEXT("Pickup embedded in collision geometry!") );
	else
	{
		Pickup->GetLevel()->SingleLineCheck( Hit, Pickup, Pickup->Location, Pickup->MyMarker->Location, TRACE_AllColliding, FVector(2.f,2.f,2.f) );
		if ( Hit.Actor )
			GWarn->Logf( TEXT("Pickup embedded in collision geometry!") );
	}
	unguard;
}

void CheckMap()
{
	GWarn->BeginSlowTask( TEXT("Checking map"), 1, 1 );
	for( INT i=0; i<GEditor->Level->Actors.Num(); i++ )
	{
		GWarn->StatusUpdatef( 0, i, TEXT("Checking map") );
		AActor* pActor = GEditor->Level->Actors(i);
		if( pActor )
		{
			ALevelInfo* LevelInfo = Cast<ALevelInfo>(pActor);
			if( LevelInfo )
				ALevelInfo__CheckForErrors(LevelInfo);
			else
			{
				ABrush* Brush = Cast<ABrush>(pActor);
				if( Brush )
					ABrush__CheckForErrors(Brush);
				else
				{
					APickup* Pickup = Cast<APickup>(pActor);
					if( Pickup )
						APickup__CheckForErrors(Pickup);
					else
						AActor__CheckForErrors(pActor);
				}
			}
		}
	}
	GWarn->EndSlowTask();
}
