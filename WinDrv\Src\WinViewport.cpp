/*=============================================================================
	UnWnCam.cpp: UWindowsViewport code.
	Copyright 1997-1999 Epic Games, Inc. All Rights Reserved.

	Revision history:
		* Created by <PERSON>
=============================================================================*/

#include "WinDrv.h"

#define DD_OTHERLOCKFLAGS 0 /*DDLOCK_NOSYSLOCK*/ /*0x00000800L*/
#define WM_MOUSEWHEEL 0x020A

#pragma warning(disable : 4644) /* usage of the macro-based offsetof pattern in constant expressions is non-standard; use offsetof defined in the C++ standard library instead */

/*-----------------------------------------------------------------------------
	Class implementation.
-----------------------------------------------------------------------------*/

IMPLEMENT_CLASS(UWindowsViewport);

/*-----------------------------------------------------------------------------
	UWindowsViewport Init/Exit.
-----------------------------------------------------------------------------*/

//
// Constructor.
//
UWindowsViewport::UWindowsViewport()
:	UViewport()
,	Status( WIN_ViewportOpening )
{
	guard(UWindowsViewport::UWindowsViewport);
	Window = new WWindowsViewportWindow( this );

	// Set color bytes based on screen resolution.
	HWND hwndDesktop = GetDesktopWindow();
	HDC  hdcDesktop  = GetDC(hwndDesktop);
	switch( GetDeviceCaps( hdcDesktop, BITSPIXEL ) )
	{
		case 8:
			ColorBytes  = 2;
			break;
		case 16:
			ColorBytes  = 2;
			Caps       |= CC_RGB565;
			break;
		case 24:
			ColorBytes  = 4;
			break;
		case 32: 
			ColorBytes  = 4;
			break;
		default: 
			ColorBytes  = 2; 
			Caps       |= CC_RGB565;
			break;
	}
	DesktopColorBytes = ColorBytes;

	// Init other stuff.
	ReleaseDC( hwndDesktop, hdcDesktop );
	SavedCursor.x = -1;

	StandardCursors[0] = LoadCursorX(NULL, IDC_ARROW);
	StandardCursors[1] = LoadCursorX(NULL, IDC_SIZEALL);
	StandardCursors[2] = LoadCursorX(NULL, IDC_SIZENESW);
	StandardCursors[3] = LoadCursorX(NULL, IDC_SIZENS);
	StandardCursors[4] = LoadCursorX(NULL, IDC_SIZENWSE);
	StandardCursors[5] = LoadCursorX(NULL, IDC_SIZEWE);
	StandardCursors[6] = LoadCursorX(NULL, IDC_WAIT);

	// Get mouse. (shared between viewports)
	if ( !GIsEditor && (Mouse == NULL) && !GetOuterUWindowsClient()->UsingRawInput )
	{
		HRESULT hr;
		if( FAILED( hr = DirectInput8->CreateDevice( GUID_SysMouse, &Mouse, NULL ) ) )
			DirectInputError( TEXT("Couldn't create mouse device"), hr, true ); 

		// Set data format.
		if( FAILED( hr = Mouse->SetDataFormat( &c_dfDIMouse2 ) ) )
			DirectInputError( TEXT("Couldn't set mouse data format"), hr, true );
	
		// Set mouse buffer.
		DIPROPDWORD Property;

		Property.diph.dwSize       = sizeof(DIPROPDWORD);
	    Property.diph.dwHeaderSize = sizeof(DIPROPHEADER);
	    Property.diph.dwObj        = 0;
	    Property.diph.dwHow        = DIPH_DEVICE;
	    Property.dwData            = 1024;	// buffer size
 
		if( FAILED( hr = Mouse->SetProperty(DIPROP_BUFFERSIZE, &Property.diph) ) )
			DirectInputError( TEXT("Couldn't set mouse buffer size"), hr, true );

		// Set mouse axis mode.
		Property.dwData				= DIPROPAXISMODE_REL;

		if( FAILED( hr = Mouse->SetProperty(DIPROP_AXISMODE, &Property.diph) ) )
			DirectInputError( TEXT("Couldn't set relative axis mode for mouse"), hr, true );
	}

	// Joystick. (shared between viewports)
	if ( !GIsEditor && (Joystick == NULL) )
	{
		HRESULT hr;
		if( FAILED( hr = DirectInput8->EnumDevices( 
			DI8DEVCLASS_GAMECTRL, 
			EnumJoysticksCallback,
			NULL, 
			DIEDFL_ATTACHEDONLY 
			)))
			DirectInputError( TEXT("Couldn't enumerate devices"), hr, true );

		// Make sure we got a joystick
		if( Joystick != NULL)
		{
			// Set joystick data format.
			if( FAILED( hr = Joystick->SetDataFormat( &c_dfDIJoystick2 ) ) )
				DirectInputError( TEXT("Couldn't set joystick data format"), hr, true );

			// Get caps.
			JoystickCaps.dwSize = sizeof(DIDEVCAPS);
			if ( FAILED( hr = Joystick->GetCapabilities(&JoystickCaps) ) )
				DirectInputError( TEXT("Couldn't get joystick caps"), hr, true );

			// Set axis range.
		    if ( FAILED( hr = Joystick->EnumObjects( EnumAxesCallback, NULL, DIDFT_AXIS ) ) )
				DirectInputError( TEXT("Couldn't enumerate joystick axis"), hr, true );
		}
	}

	Captured		= false;
	LastJoyPOV		= IK_None;

	NeedsRepainting	= 1;

	unguard;
}

//
// Destroy.
//
void UWindowsViewport::Destroy()
{
	guard(UWindowsViewport::Destroy);
	Super::Destroy();
	
	// Release devices.
	if( Mouse )
	{
		Mouse->Release();
		Mouse = NULL;
	}
	if( Joystick )
	{
		Joystick->Release();
		Joystick = NULL;
	}

	if( BlitFlags & BLIT_Temporary )
		appFree( ScreenPointer );
	Window->MaybeDestroy();
	delete Window;
	Window = NULL;
	unguard;
}

//
// Error shutdown.
//
void UWindowsViewport::ShutdownAfterError()
{
	if( ddBackBuffer )
	{
		ddBackBuffer->Release();
		ddBackBuffer = NULL;
	}
	if( ddFrontBuffer )
	{
		ddFrontBuffer->Release();
		ddFrontBuffer = NULL;
	}
	if( Window->hWnd )
	{
		DestroyWindow( Window->hWnd );
	}
	Super::ShutdownAfterError();
}

/*-----------------------------------------------------------------------------
	Command line.
-----------------------------------------------------------------------------*/

//
// Command line.
//
UBOOL UWindowsViewport::Exec( const TCHAR* Cmd, FOutputDevice& Ar )
{
	guard(UWindowsViewport::Exec);
	if( UViewport::Exec( Cmd, Ar ) )
	{
		return 1;
	}
	else if( ParseCommand(&Cmd,TEXT("EndFullscreen")) )
	{
		EndFullscreen();
		return 1;
	}
	else if( ParseCommand(&Cmd,TEXT("ToggleFullscreen")) )
	{
		ToggleFullscreen();
		return 1;
	}
	else if( ParseCommand(&Cmd,TEXT("GetCurrentRes")) )
	{
		Ar.Logf( TEXT("%ix%i"), SizeX, SizeY, (ColorBytes?ColorBytes:2)*8 );
		return 1;
	}
	else if( ParseCommand(&Cmd,TEXT("GetCurrentColorDepth")) )
	{
		Ar.Logf( TEXT("%i"), (ColorBytes?ColorBytes:2)*8 );
		return 1;
	}
	else if( ParseCommand(&Cmd,TEXT("GetColorDepths")) )
	{
		Ar.Log( TEXT("16 32") );
		return 1;
	}
	else if( ParseCommand(&Cmd,TEXT("GetCurrentRenderDevice")) )
	{
		Ar.Log( RenDev->GetClass()->GetPathName() );
		return 1;
	}
	else if( ParseCommand(&Cmd,TEXT("GetRes")) )
	{
		if( BlitFlags & BLIT_DirectDraw )
		{
			// DirectDraw modes.
			FString Result;
			for( INT i=0; i<GetOuterUWindowsClient()->DirectDrawModes[ColorBytes].Num(); i++ )
				Result += FString::Printf( TEXT("%ix%i "), (INT)GetOuterUWindowsClient()->DirectDrawModes[ColorBytes](i).X, (INT)GetOuterUWindowsClient()->DirectDrawModes[ColorBytes](i).Y );
			if( Result.Right(1)==TEXT(" ") )
				Result = Result.LeftChop(1);
			Ar.Log( *Result );
		}
		else if( BlitFlags & BLIT_DibSection )
		{
			// Windowed mode.
			Ar.Log( TEXT("320x240 400x300 512x384 640x480 800x600") );
		}
		return 1;
	}
	else if( ParseCommand(&Cmd,TEXT("SetRes")) )
	{
		INT X=appAtoi(Cmd);
		TCHAR* CmdTemp = appStrchr(Cmd,'x') ? appStrchr(Cmd,'x')+1 : appStrchr(Cmd,'X') ? appStrchr(Cmd,'X')+1 : TEXT("");
		INT Y=appAtoi(CmdTemp);
		Cmd = CmdTemp;
		CmdTemp = appStrchr(Cmd,'x') ? appStrchr(Cmd,'x')+1 : appStrchr(Cmd,'X') ? appStrchr(Cmd,'X')+1 : TEXT("");
		INT C=appAtoi(CmdTemp);
		INT NewColorBytes = C ? C/8 : ColorBytes;
		if( X && Y )
		{
			HoldCount++;
			UBOOL Result = RenDev->SetRes( X, Y, NewColorBytes, IsFullscreen() );
			HoldCount--;
			if( !Result )
				EndFullscreen();
		}
		return 1;
	}
	else if( ParseCommand(&Cmd,TEXT("Preferences")) )
	{
		UWindowsClient* Client = GetOuterUWindowsClient();
		Client->ConfigReturnFullscreen = 0;
		if( BlitFlags & BLIT_Fullscreen )
		{
			EndFullscreen();
			Client->ConfigReturnFullscreen = 1;
		}
		if( !Client->ConfigProperties )
		{
			Client->ConfigProperties = new WConfigProperties( TEXT("Preferences"), LocalizeGeneral("AdvancedOptionsTitle",TEXT("Window")) );
			Client->ConfigProperties->SetNotifyHook( Client );
			Client->ConfigProperties->OpenWindow( Window->hWnd );
			Client->ConfigProperties->ForceRefresh();
		}
		GetOuterUWindowsClient()->ConfigProperties->Show(1);
		SetFocus( *GetOuterUWindowsClient()->ConfigProperties );
		return 1;
	}
	else if( ParseCommand(&Cmd, TEXT("GetCurrentDriver")) )
	{
		TCHAR* device = (TCHAR*)RenDev->GetClass()->GetPathName();
		//TCHAR device[256] = TEXT("");
		//GConfig->GetString( TEXT("Engine.Engine"), TEXT("GameRenderDevice"), device, ARRAY_COUNT(device) );
		appStrupr(device);

		const TCHAR* returnName = TEXT("Unknown");

		if( appStrstr(device, TEXT("VULKAN")) )
		{
			returnName = TEXT("Vulkan");
		}
		else if( appStrstr(device, TEXT("OPENGL")) )
		{
			returnName = TEXT("OpenGL");
		}
		else if( appStrstr(device, TEXT("GLIDE")) )
		{
			returnName = TEXT("3DFX");
		}
		else if( appStrstr(device, TEXT("SOFT")) )
		{
			returnName = TEXT("Software");
		}
		else if( appStrstr(device, TEXT("D3D")) )
		{
			returnName = TEXT("DirectX");
		}
		else if( appStrstr(device, TEXT("METAL")) )
		{
			returnName = TEXT("Metal");
		}
		else if( appStrstr(device, TEXT("SGL")) )
		{
			returnName = TEXT("SGL");
		}
		else
		{
			// get the name from the localization file
			FString Path = RenDev->GetClass()->GetPathName();
			FString Left, Right;

			if( Path.Split(TEXT("."),&Left,&Right) )
				returnName = Localize(*Right, TEXT("ClassCaption"), *Left);
		}

		Ar.Log(returnName);

		return 1;
	}
	else return 0;
	unguard;
}

/*-----------------------------------------------------------------------------
	Window openining and closing.
-----------------------------------------------------------------------------*/

//
// Open this viewport's window.
//
void UWindowsViewport::OpenWindow( PTRINT InParentWindow, UBOOL IsTemporary, INT NewX, INT NewY, INT OpenX, INT OpenY )
{
	guard(UWindowsViewport::OpenWindow);
	check(Actor);
	check(!HoldCount);
	UBOOL DoRepaint=0, DoSetActive=0;
	UWindowsClient* C = GetOuterUWindowsClient();
	if( NewX!=INDEX_NONE )
		NewX = Align( NewX, 2 );

	// User window of launcher if no parent window was specified.
	if( !InParentWindow )
		Parse( appCmdLine(), TEXT("HWND="), InParentWindow );

	// Create frame buffer.
	if( IsTemporary )
	{
		// Create in-memory data.
		BlitFlags     = BLIT_Temporary;
		ColorBytes    = 2;
		SizeX         = NewX;
		SizeY         = NewY;
		ScreenPointer = (BYTE*)appMalloc( 2 * NewX * NewY, TEXT("TemporaryViewportData") );	
		Window->hWnd  = NULL;
		debugf( NAME_Log, TEXT("Opened temporary viewport") );
   	}
	else
	{
		// Clear BlitFlags.
		BlitFlags = 0;

		// Figure out physical window size we must specify to get appropriate client area.
		FRect rTemp( 100, 100, (NewX!=INDEX_NONE?NewX:C->WindowedViewportX) + 100, (NewY!=INDEX_NONE?NewY:C->WindowedViewportY) + 100 );

		// Get style and proper rectangle.
		DWORD Style;
		if( InParentWindow && (Actor->ShowFlags & SHOW_ChildWindow) )
		{
			Style = WS_VISIBLE | WS_CHILD | WS_CLIPSIBLINGS;
   			AdjustWindowRect( rTemp, Style, 0 );
		}
		else
		{
			Style = WS_VISIBLE | WS_OVERLAPPED | WS_CAPTION | WS_SYSMENU | WS_MINIMIZEBOX | WS_MAXIMIZEBOX | WS_THICKFRAME;
   			AdjustWindowRect( rTemp, Style, (Actor->ShowFlags & SHOW_Menu) ? TRUE : FALSE );
		}

		// Set position and size.
		if( OpenX==-1 )
			OpenX = CW_USEDEFAULT;
		if( OpenY==-1 )
			OpenY = CW_USEDEFAULT;
		INT OpenXL = rTemp.Width();
		INT OpenYL = rTemp.Height();

		// Create or update the window.
		if( !Window->hWnd )
		{
			// Creating new viewport.
			ParentWindow = (HWND)InParentWindow;
			hMenu = (Actor->ShowFlags & SHOW_Menu) ? LoadLocalizedMenu( hInstance, IDMENU_EditorCam, TEXT("IDMENU_EditorCam") ) : NULL;
			if( ParentWindow && (Actor->ShowFlags & SHOW_ChildWindow) )
				DeleteMenu( hMenu, ID_ViewTop, MF_BYCOMMAND );

			// Open the physical window.
			Window->PerformCreateWindowEx
			(
				WS_EX_APPWINDOW,
				TEXT(""),
				Style,
				OpenX, OpenY,
				OpenXL, OpenYL,
				ParentWindow,
				hMenu,
				hInstance
			);

			// Set parent window.
			if( ParentWindow && (Actor->ShowFlags & SHOW_ChildWindow) )
			{
				// Force this to be a child.
				SetWindowLongX( Window->hWnd, GWL_STYLE, WS_VISIBLE | WS_POPUP );
				if( Actor->ShowFlags & SHOW_Menu )
					SetMenu( Window->hWnd, hMenu );
			}
			debugf( NAME_Log, TEXT("Opened viewport") );
			DoSetActive = DoRepaint = 1;
		}
		else
		{
			// Resizing existing viewport.
			//!!only needed for old vb code
			SetWindowPos( Window->hWnd, NULL, OpenX, OpenY, OpenXL, OpenYL, SWP_NOACTIVATE );
		}
		ShowWindow( Window->hWnd, SW_SHOWNOACTIVATE );
		FindAvailableModes();		
		if( DoRepaint )
			UpdateWindow( Window->hWnd );
	}

	// Create rendering device.
	bool bStartFullscreen = C->StartupFullscreen && !ParseParam(appCmdLine(),TEXT("window")) && !ParseParam(appCmdLine(),TEXT("noddraw"));
	if( !RenDev && (BlitFlags & BLIT_Temporary) )
		TryRenderDevice( TEXT("SoftDrv.SoftwareRenderDevice"), NewX, NewY, ColorBytes, 0 );
	if( !RenDev && !GIsEditor && !ParseParam(appCmdLine(),TEXT("nohard")) )
		TryRenderDevice( TEXT("ini:Engine.Engine.GameRenderDevice"), NewX, NewY, INDEX_NONE, bStartFullscreen );
	// todo:
	// noddraw, nohard
	//debugf( TEXT("Trying to start software renderer, Fullscreen = %d."), bStartFullscreen );
	//TryRenderDevice( TEXT("SoftDrv.SoftwareRenderDevice"), NewX, NewY, INDEX_NONE, bStartFullscreen );
	if( !RenDev && !GIsEditor && bStartFullscreen )
	{
		debugf( TEXT("Trying to start windowed renderer fullscreen.") );
		TryRenderDevice( TEXT("ini:Engine.Engine.WindowedRenderDevice"), NewX, NewY, INDEX_NONE, 1 );
	}
	if( !RenDev )
	{
		debugf( TEXT("Trying to start windowed renderer windowed.") );
		TryRenderDevice( TEXT("ini:Engine.Engine.WindowedRenderDevice"), NewX, NewY, INDEX_NONE, 0 );
	}

	// Set cooperative level.
	HRESULT hr;
	DWORD Flags = DISCL_FOREGROUND | DISCL_NONEXCLUSIVE;

	if( Mouse )
	{
		Mouse->Unacquire();
		if( FAILED( hr = Mouse->SetCooperativeLevel( Window->hWnd, Flags ) ) )
			DirectInputError( TEXT("Couldn't set cooperative level"), hr, true );
		Mouse->Acquire();
	}

	if( Joystick )
	{
		Joystick->Unacquire();
		if( FAILED( hr = Joystick->SetCooperativeLevel( Window->hWnd, Flags ) ) )
			DirectInputError( TEXT("Couldn't set cooperative level"), hr, true );
		Joystick->Acquire();
	}

	check(RenDev);
	UpdateWindowFrame();
	if( DoRepaint )
		Repaint( 1 );
	if( DoSetActive )
		SetActiveWindow( Window->hWnd );

	unguard;
}

//
// Close a viewport window.  Assumes that the viewport has been openened with
// OpenViewportWindow.  Does not affect the viewport's object, only the
// platform-specific information associated with it.
//
void UWindowsViewport::CloseWindow()
{
	guard(UWindowsViewport::CloseWindow);
	if( Window->hWnd && Status==WIN_ViewportNormal )
	{
		Status = WIN_ViewportClosing;
		DestroyWindow( Window->hWnd );
	}
	unguard;
}

/*-----------------------------------------------------------------------------
	UWindowsViewport operations.
-----------------------------------------------------------------------------*/

//
// Find all available DirectDraw modes for a certain number of color bytes.
//
void UWindowsViewport::FindAvailableModes()
{
	guard(UWindowsViewport::FindAvailableModes);

	// Make sure we have a menu.
	if( !Window->hWnd )
		return;
	HMENU hMenu = GetMenu(Window->hWnd);
	if( !hMenu )
		return;

	// Get menus.
	INT nMenu = GIsEditor ? 3 : 2;
	HMENU hSizes = GetSubMenu( hMenu, nMenu );
	check(hSizes);

	// Completely rebuild the "Size" submenu based on what modes are available.
	while( GetMenuItemCount( hSizes ) )
		if( !DeleteMenu( hSizes, 0, MF_BYPOSITION ) )
			appErrorf( TEXT("DeleteMenu failed: %s"), appGetSystemErrorMessage() );

	// Add color depth items.
	AppendMenuX( hSizes, MF_STRING, ID_Color16Bit, LocalizeGeneral("Color16") );
	AppendMenuX( hSizes, MF_STRING, ID_Color32Bit, LocalizeGeneral("Color32") );

	// Add resolution items.
	if( !(Actor->ShowFlags & SHOW_ChildWindow) )
	{
		// Windows resolution items.
		AppendMenuX( hSizes, MF_SEPARATOR, 0, NULL );
		AppendMenuX( hSizes, MF_STRING, ID_Win320, TEXT("320x240") );
		AppendMenuX( hSizes, MF_STRING, ID_Win400, TEXT("400x300") );
		AppendMenuX( hSizes, MF_STRING, ID_Win512, TEXT("512x384") );
		AppendMenuX( hSizes, MF_STRING, ID_Win640, TEXT("640x480") );
		AppendMenuX( hSizes, MF_STRING, ID_Win800, TEXT("800x600") );

		// DirectDraw resolution items.
		if( GetOuterUWindowsClient()->DirectDrawModes[ColorBytes].Num() )
		{
			AppendMenuX( hSizes, MF_SEPARATOR, 0, NULL );
			for( INT i=0; i<GetOuterUWindowsClient()->DirectDrawModes[ColorBytes].Num(); i++ )
			{
				TCHAR Text[256];
				appSprintf( Text, TEXT("Fullscreen %ix%i"), (INT)GetOuterUWindowsClient()->DirectDrawModes[ColorBytes](i).X, (INT)GetOuterUWindowsClient()->DirectDrawModes[ColorBytes](i).Y );
				if( !AppendMenuX( hSizes, MF_STRING, ID_DDMode0+i, Text ) ) 
					appErrorf( TEXT("AppendMenu failed: %s"), appGetSystemErrorMessage() );
			}
		}
		DrawMenuBar( Window->hWnd );
	}
	unguard;
}

//
// Set window position according to menu's on-top setting:
//
void UWindowsViewport::SetTopness()
{
	guard(UWindowsViewport::SetTopness);
	HWND Topness = HWND_NOTOPMOST;
	if( GetMenu(Window->hWnd) && GetMenuState(GetMenu(Window->hWnd),ID_ViewTop,MF_BYCOMMAND)&MF_CHECKED )
		Topness = HWND_TOPMOST;
	SetWindowPos( Window->hWnd, Topness, 0, 0, 0, 0, SWP_NOMOVE|SWP_NOSIZE|SWP_SHOWWINDOW|SWP_NOACTIVATE );
	unguard;
}

//
// Repaint the viewport.
//
void UWindowsViewport::Repaint( UBOOL Blit )
{
	guard(UWindowsViewport::Repaint);
	NeedsRepainting = 0;
	GetOuterUWindowsClient()->Engine->Draw( this, Blit );
	unguard;
}

void UWindowsViewport::GUIEnter()
{
	guard(UWindowsViewport::GUIEnter);
	if( !InGui /*&& !diMouse*/ )
	{
		//SystemParametersInfoX( SPI_SETMOUSE, 0, GetOuterUWindowsClient()->NormalMouseInfo, 0 );
		InGui = 1;
	}
	unguard;
}

void UWindowsViewport::GUIExit()
{
	guard(UWindowsViewport::GUIExit);
	if( InGui /*&& !diMouse*/ )
	{
		//SystemParametersInfoX( SPI_SETMOUSE, 0, GetOuterUWindowsClient()->CaptureMouseInfo, 0 );
		InGui = 0;
	}
	unguard;
}

//
// Return whether fullscreen.
//
UBOOL UWindowsViewport::IsFullscreen()
{
	guard(UWindowsViewport::IsFullscreen);
	return (BlitFlags & BLIT_Fullscreen)!=0;
	unguard;
}

//
// Set the mouse cursor according to Unreal or UnrealEd's mode, or to
// an hourglass if a slow task is active.
//
void UWindowsViewport::SetModeCursor()
{
	guard(UWindowsViewport::SetModeCursor);
	enum EEditorMode
	{
		EM_None 			= 0,
		EM_ViewportMove		= 1,
		EM_ViewportZoom		= 2,
		EM_BrushRotate		= 5,
		EM_BrushSheer		= 6,
		EM_BrushScale		= 7,
		EM_BrushStretch		= 8,
		EM_TexturePan		= 11,
		EM_TextureRotate	= 13,
		EM_TextureScale		= 14,
		EM_BrushSnap		= 18,
		EM_TexView			= 19,
		EM_TexBrowser		= 20,
		EM_MeshView			= 21,
	};
	if( GIsSlowTask )
	{
		SetCursor(LoadCursorX(NULL,IDC_WAIT));
		return;
	}
	HCURSOR hCursor;
	switch( GetOuterUWindowsClient()->Engine->edcamMode(this) )
	{
		case EM_ViewportZoom:	hCursor = LoadCursorIdX(hInstance,IDCURSOR_CameraZoom); break;
		case EM_BrushRotate:	hCursor = LoadCursorIdX(hInstance,IDCURSOR_BrushRot); break;
		case EM_BrushSheer:		hCursor = LoadCursorIdX(hInstance,IDCURSOR_BrushSheer); break;
		case EM_BrushScale:		hCursor = LoadCursorIdX(hInstance,IDCURSOR_BrushScale); break;
		case EM_BrushStretch:	hCursor = LoadCursorIdX(hInstance,IDCURSOR_BrushStretch); break;
		case EM_BrushSnap:		hCursor = LoadCursorIdX(hInstance,IDCURSOR_BrushSnap); break;
		case EM_TexturePan:		hCursor = LoadCursorIdX(hInstance,IDCURSOR_TexPan); break;
		case EM_TextureRotate:	hCursor = LoadCursorIdX(hInstance,IDCURSOR_TexRot); break;
		case EM_TextureScale:	hCursor = LoadCursorIdX(hInstance,IDCURSOR_TexScale); break;
		case EM_None: 			hCursor = LoadCursorX(NULL,IDC_CROSS); break;
		case EM_ViewportMove: 	hCursor = LoadCursorX(NULL,IDC_CROSS); break;
		case EM_TexView:		hCursor = LoadCursorX(NULL,IDC_ARROW); break;
		case EM_TexBrowser:		hCursor = LoadCursorX(NULL,IDC_ARROW); break;
		case EM_MeshView:		hCursor = LoadCursorX(NULL,IDC_CROSS); break;
		default: 				hCursor = LoadCursorX(NULL,IDC_ARROW); break;
	}
	check(hCursor);
	SetCursor( hCursor );
	unguard;
}

//
// Update user viewport interface.
//
void UWindowsViewport::UpdateWindowFrame()
{
	guard(UWindowsViewport::UpdateWindowFrame);

	// If not a window, exit.
	if( HoldCount || !Window->hWnd || (BlitFlags&BLIT_Fullscreen) || (BlitFlags&BLIT_Temporary) )
		return;

	// Set viewport window's name to show resolution.
	TCHAR WindowName[80];
	if( !GIsEditor || (Actor->ShowFlags&SHOW_PlayerCtrl) )
	{
		appSprintf( WindowName, LocalizeGeneral("Product",appPackage()) );
	}
	else switch( Actor->RendMap )
	{
		case REN_Wire:		appStrcpy(WindowName,LocalizeGeneral("ViewPersp")); break;
		case REN_OrthXY:	appStrcpy(WindowName,LocalizeGeneral("ViewXY"   )); break;
		case REN_OrthXZ:	appStrcpy(WindowName,LocalizeGeneral("ViewXZ"   )); break;
		case REN_OrthYZ:	appStrcpy(WindowName,LocalizeGeneral("ViewYZ"   )); break;
		default:			appStrcpy(WindowName,LocalizeGeneral("ViewOther")); break;
	}
	Window->SetText( WindowName );

	// Set the menu.
	if( (Actor->ShowFlags & SHOW_Menu) && !(BlitFlags & BLIT_Fullscreen) )
	{
		if( !hMenu )
			hMenu = LoadLocalizedMenu( hInstance, IDMENU_EditorCam, TEXT("IDMENU_EditorCam") );
		UBOOL MustUpdate = !GetMenu(Window->hWnd);
		SetMenu( Window->hWnd, hMenu );
		if( MustUpdate )
			FindAvailableModes();
	}
	else SetMenu( Window->hWnd, NULL );

	// Update menu, Map rendering.
	CheckMenuItem(hMenu,ID_MapPlainTex,  (Actor->RendMap==REN_PlainTex  ? MF_CHECKED:MF_UNCHECKED));
	CheckMenuItem(hMenu,ID_MapDynLight,  (Actor->RendMap==REN_DynLight  ? MF_CHECKED:MF_UNCHECKED));
	CheckMenuItem(hMenu,ID_MapWire,      (Actor->RendMap==REN_Wire      ? MF_CHECKED:MF_UNCHECKED));
	CheckMenuItem(hMenu,ID_MapOverhead,  (Actor->RendMap==REN_OrthXY    ? MF_CHECKED:MF_UNCHECKED));
	CheckMenuItem(hMenu,ID_MapXZ, 		 (Actor->RendMap==REN_OrthXZ    ? MF_CHECKED:MF_UNCHECKED));
	CheckMenuItem(hMenu,ID_MapYZ, 		 (Actor->RendMap==REN_OrthYZ    ? MF_CHECKED:MF_UNCHECKED));
	CheckMenuItem(hMenu,ID_MapPolys,     (Actor->RendMap==REN_Polys     ? MF_CHECKED:MF_UNCHECKED));
	CheckMenuItem(hMenu,ID_MapPolyCuts,  (Actor->RendMap==REN_PolyCuts  ? MF_CHECKED:MF_UNCHECKED));
	CheckMenuItem(hMenu,ID_MapZones,     (Actor->RendMap==REN_Zones     ? MF_CHECKED:MF_UNCHECKED));
	CheckMenuItem(hMenu,ID_MapNoPass,    (Actor->RendMap==REN_NoPass    ? MF_CHECKED:MF_UNCHECKED));

	// Show-attributes.
	CheckMenuItem(hMenu,ID_ShowBrush,     ((Actor->ShowFlags&SHOW_Brush			)?MF_CHECKED:MF_UNCHECKED));
	CheckMenuItem(hMenu,ID_ShowBackdrop,  ((Actor->ShowFlags&SHOW_Backdrop  		)?MF_CHECKED:MF_UNCHECKED));
	CheckMenuItem(hMenu,ID_ShowCoords,    ((Actor->ShowFlags&SHOW_Coords    		)?MF_CHECKED:MF_UNCHECKED));
	CheckMenuItem(hMenu,ID_ShowMovingBrushes,((Actor->ShowFlags&SHOW_MovingBrushes)?MF_CHECKED:MF_UNCHECKED));
	CheckMenuItem(hMenu,ID_ShowPaths,     ((Actor->ShowFlags&SHOW_Paths)?MF_CHECKED:MF_UNCHECKED));

	// Actor showing.
	CheckMenuItem(hMenu,ID_ActorsIcons,  MF_UNCHECKED);
	CheckMenuItem(hMenu,ID_ActorsRadii,  MF_UNCHECKED);
	CheckMenuItem(hMenu,ID_ActorsShow,   MF_UNCHECKED);
	CheckMenuItem(hMenu,ID_ActorsHide,   MF_UNCHECKED);

	// Actor options.
	DWORD ShowFilter = Actor->ShowFlags & (SHOW_Actors | SHOW_ActorIcons | SHOW_ActorRadii);
	if		(ShowFilter==(SHOW_Actors | SHOW_ActorIcons)) CheckMenuItem(hMenu,ID_ActorsIcons,MF_CHECKED);
	else if (ShowFilter==(SHOW_Actors | SHOW_ActorRadii)) CheckMenuItem(hMenu,ID_ActorsRadii,MF_CHECKED);
	else if (ShowFilter==(SHOW_Actors                  )) CheckMenuItem(hMenu,ID_ActorsShow,MF_CHECKED);
	else CheckMenuItem(hMenu,ID_ActorsHide,MF_CHECKED);

	// Color depth.
	CheckMenuItem(hMenu,ID_Color16Bit,((ColorBytes==2)?MF_CHECKED:MF_UNCHECKED));
	CheckMenuItem(hMenu,ID_Color32Bit,((ColorBytes==4)?MF_CHECKED:MF_UNCHECKED));

	// Update parent window.
	if( ParentWindow )
		SendMessageX( ParentWindow, WM_CHAR, 0, 0 );

	unguard;
}

//
// Return the viewport's window.
//
void* UWindowsViewport::GetWindow()
{
	guard(UWindowsViewport::GetWindow);
	return Window->hWnd;
	unguard;
}

/*-----------------------------------------------------------------------------
	Input.
-----------------------------------------------------------------------------*/

//
// Input event router.
//
UBOOL UWindowsViewport::CauseInputEvent( INT iKey, EInputAction Action, FLOAT Delta )
{
	guard(UWindowsViewport::CauseInputEvent);

	// Route to engine if a valid key; some keyboards produce key
	// codes that go beyond IK_MAX.
	if( iKey>=0 && iKey<IK_MAX )
		return GetOuterUWindowsClient()->Engine->InputEvent( this, (EInputKey)iKey, Action, Delta );
	else
		return 0;

	unguard;
}

//
// If the cursor is currently being captured, stop capturing, clipping, and 
// hiding it, and move its position back to where it was when it was initially
// captured.
//
void UWindowsViewport::SetMouseCapture( UBOOL Capture, UBOOL Clip, UBOOL OnlyFocus )
{
	guard(UWindowsViewport::SetMouseCapture);

	// If only handling focus windows, store the clipping and capture state, then exit out
	// If capturing, windows requires clipping in order to keep focus.
	Clip |= Capture;

	if( Clip && OnlyFocus && Window->hWnd != GetFocus() )
		return;

	// Get window rectangle.
	RECT TempRect;
	::GetClientRect( Window->hWnd, &TempRect );
	MapWindowPoints( Window->hWnd, NULL, (POINT*)&TempRect, 2 );

	// Handle capturing.
	if( Capture )
	{
		if( SavedCursor.x == -1 )
		{
			// Confine cursor to window.
			::GetCursorPos( &SavedCursor );
			//OrigCursor = FVector( SavedCursor.x, SavedCursor.y, 0 );
		}
			
		// Bring window to the top (including all Win32 mojo).
		HWND hWndForeground = ::GetForegroundWindow();
		UBOOL Attach		= (hWndForeground == Window->hWnd);

		if( Attach )
			AttachThreadInput(GetWindowThreadProcessId(hWndForeground, NULL), GetCurrentThreadId(), true);

		SetForegroundWindow( Window->hWnd );
		SetActiveWindow( Window->hWnd );

		if( Attach )
			AttachThreadInput(GetWindowThreadProcessId(hWndForeground, NULL), GetCurrentThreadId(), false);

		//if( GetOuterUWindowsClient()->Engine->edcamMouseControl( this ) == MOUSEC_Locked )
			SetCursorPos( (TempRect.left+TempRect.right)/2, (TempRect.top+TempRect.bottom)/2 );

		// Start capturing cursor.
		SetCapture( Window->hWnd );
		//SystemParametersInfoX( SPI_SETMOUSE, 0, GetOuterUWindowsClient()->CaptureMouseInfo, 0 );
		while( ShowCursor(FALSE)>=0 );
	}
	else
	{
		// Release captured cursor.
		if( !(BlitFlags & BLIT_Fullscreen) )
		{
			SetCapture( NULL );
			//SystemParametersInfoX( SPI_SETMOUSE, 0, GetOuterUWindowsClient()->NormalMouseInfo, 0 );
		}

		// Restore position.
		if( SavedCursor.x != -1 )
		{
			//if( GetOuterUWindowsClient()->Engine->edcamMouseControl( this ) == MOUSEC_Locked )
				SetCursorPos( SavedCursor.x, SavedCursor.y );
			SavedCursor.x = -1;
		}

		while( ShowCursor(TRUE)<0 );
	}

	// Handle clipping.
	ClipCursor( Clip ? &TempRect : NULL );

	// Memorize state.
	Captured = Capture;

	unguard;
}

//
// Update input for viewport.
//
UBOOL UWindowsViewport::JoystickInputEvent( FLOAT Delta, EInputKey Key, FLOAT Scale, UBOOL DeadZone )
{
	guard(UWindowsViewport::JoystickInputEvent);
	Delta = (Delta-32768.0)/32768.0;
	if( DeadZone )
	{
		if( Delta > 0.2 )
			Delta = (Delta - 0.2) / 0.8;
		else if( Delta < -0.2 )
			Delta = (Delta + 0.2) / 0.8;
		else
			Delta = 0.0;
	}
	return CauseInputEvent( Key, IST_Axis, Scale * Delta );
	unguard;
}

//
// Update input for this viewport.
//
void UWindowsViewport::UpdateInput( UBOOL Reset )
{
	guard(UWindowsViewport::UpdateInput);
	BYTE Processed[256];
	appMemset( Processed, 0, 256 );
	//debugf(TEXT("%i"),(INT)GTempDouble);

	// From Legend - fix for key strokes left in input buffer
	// e.g. bind a key to exit, press and hold it down while exiting,
	// next time engine starts, appExit will be called because of
	// remaining key strokes left in input buffer without this fix.
	if( Reset )
	{
		BYTE KeyStates[256];
		appMemset( KeyStates, 0, 256 );

		// don't wipe out toggleable key settings:
		KeyStates[IK_NumLock]		= GetKeyState(IK_NumLock);
		KeyStates[IK_CapsLock]		= GetKeyState(IK_CapsLock);
		KeyStates[IK_ScrollLock]	= GetKeyState(IK_ScrollLock);
        KeyStates[IK_Ctrl]			= GetKeyState(IK_Ctrl);
        KeyStates[IK_Shift]			= GetKeyState(IK_Shift);
        KeyStates[IK_Alt]			= GetKeyState(IK_Alt);

		SetKeyboardState(KeyStates);
	}

	HRESULT hr;

	// Joystick.
#if 0
	// todo: this will also fix crashing on exit when controller is plugged in
	UWindowsClient* Client = GetOuterUWindowsClient();
	if( Client->JoyCaps.wNumButtons )
	{
		JOYINFOEX JoyInfo;
		appMemzero( &JoyInfo, sizeof(JoyInfo) );
		JoyInfo.dwSize  = sizeof(JoyInfo);
		JoyInfo.dwFlags = JOY_RETURNBUTTONS | JOY_RETURNCENTERED | JOY_RETURNPOV | JOY_RETURNR | JOY_RETURNU | JOY_RETURNV | JOY_RETURNX | JOY_RETURNY | JOY_RETURNZ;
		MMRESULT Result = joyGetPosEx( JOYSTICKID1, &JoyInfo );
		if( Result==JOYERR_NOERROR )
		{ 
			// Pass buttons to app.
			for( INT Index=0; Index<16; Index++,JoyInfo.dwButtons/=2 )
			{
				if( !Input->KeyDown(IK_Joy1+Index) && (JoyInfo.dwButtons & 1) )
					CauseInputEvent( IK_Joy1+Index, IST_Press );
				else if( Input->KeyDown(IK_Joy1+Index) && !(JoyInfo.dwButtons & 1) )
					CauseInputEvent( IK_Joy1+Index, IST_Release );
				Processed[IK_Joy1+Index] = 1;
			}

			// Pass axes to app.
			JoystickInputEvent( JoyInfo.dwXpos, IK_JoyX, Client->ScaleXYZ, Client->DeadZoneXYZ );
			JoystickInputEvent( JoyInfo.dwYpos, IK_JoyY, Client->ScaleXYZ * (Client->InvertVertical ? 1.0 : -1.0), Client->DeadZoneXYZ );
			if( Client->JoyCaps.wCaps & JOYCAPS_HASZ )
				JoystickInputEvent( JoyInfo.dwZpos, IK_JoyZ, Client->ScaleXYZ, Client->DeadZoneXYZ );
			if( Client->JoyCaps.wCaps & JOYCAPS_HASR )
				JoystickInputEvent( JoyInfo.dwRpos, IK_JoyR, Client->ScaleRUV, Client->DeadZoneRUV );
			if( Client->JoyCaps.wCaps & JOYCAPS_HASU )
				JoystickInputEvent( JoyInfo.dwUpos, IK_JoyU, Client->ScaleRUV, Client->DeadZoneRUV );
			if( Client->JoyCaps.wCaps & JOYCAPS_HASV )
				JoystickInputEvent( JoyInfo.dwVpos, IK_JoyV, Client->ScaleRUV * (Client->InvertVertical ? 1.0 : -1.0), Client->DeadZoneRUV );
			if( Client->JoyCaps.wCaps & (JOYCAPS_POV4DIR|JOYCAPS_POVCTS) )
			{
				if( JoyInfo.dwPOV==JOY_POVFORWARD )
				{
					if( !Input->KeyDown(IK_JoyPovUp) )
						CauseInputEvent( IK_JoyPovUp, IST_Press );
					Processed[IK_JoyPovUp] = 1;
				}
				else if( JoyInfo.dwPOV==JOY_POVBACKWARD )
				{
					if( !Input->KeyDown(IK_JoyPovDown) )
						CauseInputEvent( IK_JoyPovDown, IST_Press );
					Processed[IK_JoyPovDown] = 1;
				}
				else if( JoyInfo.dwPOV==JOY_POVLEFT )
				{
					if( !Input->KeyDown(IK_JoyPovLeft) )
						CauseInputEvent( IK_JoyPovLeft, IST_Press );
					Processed[IK_JoyPovLeft] = 1;
				}
				else if( JoyInfo.dwPOV==JOY_POVRIGHT )
				{
					if( !Input->KeyDown(IK_JoyPovRight) )
						CauseInputEvent( IK_JoyPovRight, IST_Press );
					Processed[IK_JoyPovRight] = 1;
				}
			}
		}
	}
#endif

	// DirectInput mouse.
	DWORD	Elements			= 1;
	FLOAT MouseXMultiplier = 1.0;
	FLOAT MouseYMultiplier = 1.0;

	while( !GIsEditor && Elements && Mouse )
	{
        DIDEVICEOBJECTDATA Event;
 
		if (FAILED(hr = Mouse->GetDeviceData(sizeof(DIDEVICEOBJECTDATA), &Event, &Elements, 0) ) )
		{	
			Mouse->Acquire();
			Elements = 0;
		}
		else if ( Elements )
		{
			// Look at the element to see what occurred
			switch (Event.dwOfs) 
			{
			case DIMOFS_X: 
				CauseInputEvent( IK_MouseX, IST_Axis, +MouseXMultiplier * ((INT) Event.dwData) );
				break;
			case DIMOFS_Y: 
				CauseInputEvent( IK_MouseY, IST_Axis, -MouseYMultiplier * ((INT) Event.dwData) );
				break; 
			case DIMOFS_Z:
				CauseInputEvent( IK_MouseW, IST_Axis, (INT) Event.dwData );
				if( ((INT)Event.dwData) < 0)
				{
					CauseInputEvent( IK_MWheelDown, IST_Press );
					CauseInputEvent( IK_MWheelDown, IST_Release );
				}
				else if( ((INT)Event.dwData) > 0)
				{
					CauseInputEvent( IK_MWheelUp, IST_Press );
					CauseInputEvent( IK_MWheelUp, IST_Release );
				}
				break;
            case DIMOFS_BUTTON0: // left button
				if (Event.dwData & 0x80) 
					CauseInputEvent( IK_LeftMouse, IST_Press );
				else
					CauseInputEvent( IK_LeftMouse, IST_Release );
				break;
			case DIMOFS_BUTTON1: // right button
				if (Event.dwData & 0x80) 
					CauseInputEvent( IK_RightMouse, IST_Press );
				else
					CauseInputEvent( IK_RightMouse, IST_Release );
				break;
			case DIMOFS_BUTTON2: // middle button
				if (Event.dwData & 0x80) 
					CauseInputEvent( IK_MiddleMouse, IST_Press );
				else
					CauseInputEvent( IK_MiddleMouse, IST_Release );
				break;
			case DIMOFS_BUTTON3:
				if (Event.dwData & 0x80) 
					CauseInputEvent( IK_Mouse4, IST_Press );
				else
					CauseInputEvent( IK_Mouse4, IST_Release );
				break;
			case DIMOFS_BUTTON4:
				if (Event.dwData & 0x80) 
					CauseInputEvent( IK_Mouse5, IST_Press );
				else
					CauseInputEvent( IK_Mouse5, IST_Release );
				break;
#if 0
			case DIMOFS_BUTTON5:
				if (Event.dwData & 0x80) 
					CauseInputEvent( IK_Mouse6, IST_Press );
				else
					CauseInputEvent( IK_Mouse6, IST_Release );
				break;
			case DIMOFS_BUTTON6:
				if (Event.dwData & 0x80) 
					CauseInputEvent( IK_Mouse7, IST_Press );
				else
					CauseInputEvent( IK_Mouse7, IST_Release );
				break;
			case DIMOFS_BUTTON7:
				if (Event.dwData & 0x80) 
					CauseInputEvent( IK_Mouse8, IST_Press );
				else
					CauseInputEvent( IK_Mouse8, IST_Release );
				break;
#endif
			default:
                break;        
			}
		}
	}

	Processed[IK_LeftMouse]		= 1;
	Processed[IK_RightMouse]	= 1;
	Processed[IK_MiddleMouse]	= 1;
	Processed[IK_Mouse4]		= 1;
	Processed[IK_Mouse5]		= 1;
	//Processed[IK_Mouse6]		= 1;
	//Processed[IK_Mouse7]		= 1;
	//Processed[IK_Mouse8]		= 1;

	// Keyboard.
	Reset = Reset && GetFocus()==Window->hWnd;
	for( INT i=0; i<256; i++ )
	{
		if( !Processed[i] )
		{
			if( !Input->KeyDown(i) )
			{
				//old: if( Reset && (GetAsyncKeyState(i) & 0x8000) )
				if( Reset && (GetKeyState(i) & 0x8000) )
					CauseInputEvent( i, IST_Press );
			}
			else
			{
				//old: if( !(GetAsyncKeyState(i) & 0x8000) )
				if( !(GetKeyState(i) & 0x8000) )
					CauseInputEvent( i, IST_Release );
			}
		}
	}

	unguard;
}

void UWindowsViewport::UpdateMousePosition()
{
	guard(UWindowsViewport::UpdateMousePosition);

	POINT pt;

	::GetCursorPos( &pt );
	//MouseScreenPos = FVector( pt.x, pt.y, 0 );

	::ScreenToClient( (HWND)GetWindow(), &pt );
	//MouseClientPos = FVector( pt.x, pt.y, 0 );

	WindowsMouseX = pt.x;
	WindowsMouseY = pt.y;

	if( GetLegacyShowWindowsMouse() && SelectedCursor >= 0 && SelectedCursor <= 6 )
		SetCursor( StandardCursors[SelectedCursor] );

	unguard;
}

// original undying unrealscript code sets bShowWindowsMouse to 1 regardless of bWindowsMouseAvailable
// to preserve compatibility with original code, you would call this function instead of checking bShowWindowsMouse directly
UBOOL UWindowsViewport::GetLegacyShowWindowsMouse()
{
	return bShowWindowsMouse && bWindowsMouseAvailable;
}

/*-----------------------------------------------------------------------------
	Viewport WndProc.
-----------------------------------------------------------------------------*/

//
// Main viewport window function.
//
LRESULT UWindowsViewport::ViewportWndProc( UINT iMessage, WPARAM wParam, LPARAM lParam )
{
	guard(UWindowsViewport::ViewportWndProc);
	UWindowsClient* Client = GetOuterUWindowsClient();
	if( HoldCount || Client->Viewports.FindItemIndex(this)==INDEX_NONE || !Actor )
		return DefWindowProcX( Window->hWnd, iMessage, wParam, lParam );

	// Statics.
	static UBOOL MovedSinceLeftClick=0;
	static UBOOL MovedSinceRightClick=0;
	static UBOOL MovedSinceMiddleClick=0;
	static DWORD StartTimeLeftClick=0;
	static DWORD StartTimeRightClick=0;
	static DWORD StartTimeMiddleClick=0;

	// Updates.
	if( iMessage==WindowMessageMouseWheel )
	{
		iMessage = WM_MOUSEWHEEL;
		wParam   = MAKEWPARAM(0,wParam);
	}

	// Message handler.
	switch( iMessage )
	{
		case WM_CREATE:
		{
			guard(WM_CREATE);

         	// Set status.
			Status = WIN_ViewportNormal; 

			// Make this viewport current and update its title bar.
			GetOuterUClient()->MakeCurrent( this );

			// Fix for the "losing focus when starting up fullscreen" bug.
			if( !GIsEditor )
				SetFocus(Window->hWnd);

			return 0;
			unguard;
		}
		case WM_HOTKEY:
		{
			return 0;
		}
		case WM_DESTROY:
		{
			guard(WM_DESTROY);

			// If there's an existing Viewport structure corresponding to
			// this window, deactivate it.
			if( BlitFlags & BLIT_Fullscreen )
				EndFullscreen();

			// Free DIB section stuff (if any).
			if( hBitmap )
				DeleteObject( hBitmap );

			// Restore focus to caller if desired.
			PTRINT ParentWindow=0;
			Parse( appCmdLine(), TEXT("HWND="), ParentWindow );
			if( ParentWindow )
			{
				::SetParent( Window->hWnd, NULL );
				SetFocus( (HWND)ParentWindow );
			}

			// Stop clipping.
			SetDrag( 0 );
			if( Status==WIN_ViewportNormal )
			{
				// Closed by user clicking on window's close button, so delete the viewport.
				Status = WIN_ViewportClosing; // Prevent recursion.
				delete this;
			}
			debugf( NAME_Log, TEXT("Closed viewport") );
			return 0;
			unguard;
		}
		case WM_PAINT:
		{
			guard(WM_PAINT);
			if( BlitFlags & (BLIT_Fullscreen|BLIT_Direct3D|BLIT_HardwarePaint) )
			{
				if( BlitFlags & BLIT_HardwarePaint )
					Repaint( 1 );
				ValidateRect( Window->hWnd, NULL );
				return 0;
			}
			else if( IsWindowVisible(Window->hWnd) && SizeX && SizeY && hBitmap )
			{
				PAINTSTRUCT ps;
				BeginPaint( Window->hWnd, &ps );
				HDC hDC = GetDC( Window->hWnd );
				if( hDC == NULL )
					appErrorf( TEXT("GetDC failed: %s"), appGetSystemErrorMessage() );
				if( SelectObject( Client->hMemScreenDC, hBitmap ) == NULL )
					appErrorf( TEXT("SelectObject failed: %s"), appGetSystemErrorMessage() );
				if( BitBlt( hDC, 0, 0, SizeX, SizeY, Client->hMemScreenDC, 0, 0, SRCCOPY ) == NULL )
					appErrorf( TEXT("BitBlt failed: %s"), appGetSystemErrorMessage() );
				if( ReleaseDC( Window->hWnd, hDC ) == NULL )
					appErrorf( TEXT("ReleaseDC failed: %s"), appGetSystemErrorMessage() );
				EndPaint( Window->hWnd, &ps );
				return 0;
			}
			else return 1;
			unguard;
		}
		case WM_COMMAND:
		{
			guard(WM_COMMAND);

			if( GIsEditor )
			{
				HWND hwndEditorFrame = GetAncestor(ParentWindow, GA_ROOT);
				PostMessage(hwndEditorFrame, WM_COMMAND, wParam, lParam); // Pass through to Editor for accelerators.
				UpdateWindowFrame();			
			}

      		switch( wParam )
			{
				case ID_MapDynLight:
				{
					Actor->RendMap=REN_DynLight;
					break;
				}
				case ID_MapPlainTex:
				{
					Actor->RendMap=REN_PlainTex;
					break;
				}
				case ID_MapWire:
				{
					Actor->RendMap=REN_Wire;
					break;
				}
				case ID_MapOverhead:
				{
					Actor->RendMap=REN_OrthXY;
					break;
				}
				case ID_MapXZ:
				{
					Actor->RendMap=REN_OrthXZ;
					break;
				}
				case ID_MapYZ:
				{
					Actor->RendMap=REN_OrthYZ;
					break;
				}
				case ID_MapPolys:
				{
					Actor->RendMap=REN_Polys;
					break;
				}
				case ID_MapPolyCuts:
				{
					Actor->RendMap=REN_PolyCuts;
					break;
				}
				case ID_MapZones:
				{
					Actor->RendMap=REN_Zones;
					break;
				}
				case ID_MapNoPass:
				{
					Actor->RendMap=REN_NoPass;
					break;
				}
				case ID_Win320:
				{
					RenDev->SetRes( 320, 240, ColorBytes, 0 );
					break;
				}
				case ID_Win400:
				{
					RenDev->SetRes( 400, 300, ColorBytes, 0 );
					break;
				}
				case ID_Win512:
				{
					RenDev->SetRes( 512, 384, ColorBytes, 0 );
					break;
				}
				case ID_Win640:
				{
					RenDev->SetRes( 640, 480, ColorBytes, 0 );
					break;
				}
				case ID_Win800:
				{
					RenDev->SetRes( 800, 600, ColorBytes, 0 );
					break;
				}
				case ID_Color16Bit:
				{
					RenDev->SetRes( SizeX, SizeY, 2, 0 );
					Repaint( 1 );
					FindAvailableModes();
					break;
				}
				case ID_Color32Bit:
				{
					RenDev->SetRes( SizeX, SizeY, 4, 0 );
					Repaint( 1 );
					FindAvailableModes();
					break;
				}
				case ID_ShowBackdrop:
				{
					Actor->ShowFlags ^= SHOW_Backdrop;
					break;
				}
				case ID_ActorsShow:
				{
					Actor->ShowFlags &= ~(SHOW_Actors | SHOW_ActorIcons | SHOW_ActorRadii);
					Actor->ShowFlags |= SHOW_Actors; 
					break;
				}
				case ID_ActorsIcons:
				{
					Actor->ShowFlags &= ~(SHOW_Actors | SHOW_ActorIcons | SHOW_ActorRadii); 
					Actor->ShowFlags |= SHOW_Actors | SHOW_ActorIcons;
					break;
				}
				case ID_ActorsRadii:
				{
					Actor->ShowFlags &= ~(SHOW_Actors | SHOW_ActorIcons | SHOW_ActorRadii); 
					Actor->ShowFlags |= SHOW_Actors | SHOW_ActorRadii;
					break;
				}
				case ID_ActorsHide:
				{
					Actor->ShowFlags &= ~(SHOW_Actors | SHOW_ActorIcons | SHOW_ActorRadii); 
					break;
				}
				case ID_ShowPaths:
				{
					Actor->ShowFlags ^= SHOW_Paths;
					break;
				}
				case ID_ShowCoords:
				{
					Actor->ShowFlags ^= SHOW_Coords;
					break;
				}
				case ID_ShowBrush:
				{
					Actor->ShowFlags ^= SHOW_Brush;
					break;
				}
				case ID_ShowMovingBrushes:
				{
					Actor->ShowFlags ^= SHOW_MovingBrushes;
					break;
				}
				case ID_ViewLog:
				{
					Exec( TEXT("SHOWLOG"), *this );
					break;
				}
				case ID_FileExit:
				{
					DestroyWindow( Window->hWnd );
					return 0;
				}
				case ID_ViewTop:
				{
					ToggleMenuItem(GetMenu(Window->hWnd),ID_ViewTop);
					SetTopness();
					break;
				}
				case ID_ViewAdvanced:
				{
					Exec( TEXT("Preferences"), *GLog );
					break;
				}
				default:
				{
					if( wParam>=ID_DDMode0 && wParam<=ID_DDMode9 )
						ResizeViewport( BLIT_Fullscreen|BLIT_DirectDraw, Client->DirectDrawModes[ColorBytes](wParam-ID_DDMode0).X, Client->DirectDrawModes[ColorBytes](wParam-ID_DDMode0).Y );
					break;
				}
			}
			RequestRepaint( 1 );
			UpdateWindowFrame();
			return 0;
			unguard;
		}
		case WM_KEYDOWN:
		case WM_SYSKEYDOWN:
		{
			guard(WM_KEYDOWN);

			// Get key code.
			EInputKey Key = (EInputKey)wParam;

			// Send key to input system.
			if( Key==IK_Enter && (GetKeyState(VK_MENU)&0x8000) )
			{
				ToggleFullscreen();
			}
			else if( CauseInputEvent( Key, IST_Press ) )
			{	
				// Redraw if the viewport won't be redrawn on timer.
				if( !IsRealtime() )
					RequestRepaint( 1 );
			}

			// Send to UnrealEd.
			if( ParentWindow && GIsEditor )
			{
				if( Key==IK_F1 )
					PostMessageX( ParentWindow, iMessage, IK_F2, lParam );
				else if( Key!=IK_Tab && Key!=IK_Enter && Key!=IK_Alt )
					PostMessageX( ParentWindow, iMessage, wParam, lParam );
			}

			// Set the cursor.
			if( GIsEditor )
				SetModeCursor();

			return 0;
			unguard;
		}
		case WM_KEYUP:
		case WM_SYSKEYUP:
		{
			guard(WM_KEYUP);

			// Send to the input system.
			EInputKey Key = (EInputKey)wParam;
			if( CauseInputEvent( Key, IST_Release ) )
			{	
				// Redraw if the viewport won't be redrawn on timer.
				if( !IsRealtime() )
					RequestRepaint( 1 );
			}

			// Capture mouse if focus was lost.
			if( !GIsEditor && Captured == GetLegacyShowWindowsMouse() )
				SetMouseCapture(!GetLegacyShowWindowsMouse(),0,1);

			// Pass keystroke on to UnrealEd.
			if( ParentWindow && GIsEditor )
			{				
				if( Key == IK_F1 )
					PostMessageX( ParentWindow, iMessage, IK_F2, lParam );
				else if( Key!=IK_Tab && Key!=IK_Enter && Key!=IK_Alt )
					PostMessageX( ParentWindow, iMessage, wParam, lParam );
			}
			if( GIsEditor )
				SetModeCursor();
			return 0;
			unguard;
		}
		case WM_SYSCHAR:
		case WM_CHAR:
		{
			guard(WM_CHAR);
			EInputKey Key = (EInputKey)wParam;
			if( Key!=IK_Enter && Client->Engine->Key( this, Key ) )
			{
				// Redraw if needed.
				if( !IsRealtime() )
					RequestRepaint( 1 );
				
				if( GIsEditor )
					SetModeCursor();
			}
			else if( iMessage == WM_SYSCHAR )
			{
				// Perform default processing.
				return DefWindowProcX( Window->hWnd, iMessage, wParam, lParam );
			}
			return 0;
			unguard;
		}
		case WM_ERASEBKGND:
		{
			// Prevent Windows from repainting client background in white.
			return 0;
		}
		case WM_SETCURSOR:
		{
			guard(WM_SETCURSOR);
			if( (LOWORD(lParam)==1) || GIsSlowTask )
			{
				// In client area or processing slow task.
				if( GIsEditor )
					SetModeCursor();
				return 0;
			}
			else
			{
				// Out of client area.
				return DefWindowProcX( Window->hWnd, iMessage, wParam, lParam );
			}
			unguard;
		}
		case WM_LBUTTONDBLCLK:
		{
			if( GIsEditor && SizeX && SizeY && !(BlitFlags&BLIT_Fullscreen) )
			{
				Client->Engine->Click( this, MOUSE_LeftDouble, LOWORD(lParam), HIWORD(lParam) );
				if( !IsRealtime() )
					RequestRepaint( 1 );
			}
			return 0;
		}
		case WM_LBUTTONDOWN:
		case WM_RBUTTONDOWN:
		case WM_MBUTTONDOWN:
		{
			guard(WM_BUTTONDOWN);

			if( Client->InMenuLoop )
				return DefWindowProcX( Window->hWnd, iMessage, wParam, lParam );

			if( GIsEditor )
			{
				//
				// Doing this allows the editor to know where the mouse was last clicked in
				// world coordinates without having to do hit tests and such.
				//

#if UNDYING_MEM
				FMemMark MemMark(GMem);
				FMemMark DynMark(GDynMem);
				FMemMark SceneMark(GSceneMem);
#endif

				// Figure out where the mouse was clicked, in world coordinates.
				FVector V;
				FSceneNode* Frame = Client->Engine->Render->CreateMasterFrame( this, Actor->Location, Actor->Rotation, NULL );
				Client->Engine->Render->PreRender( Frame );
				Client->Engine->Render->Deproject( Frame, LOWORD(lParam), HIWORD(lParam), V );
				Client->Engine->Render->FinishMasterFrame();

				Client->Engine->edSetClickLocation(V);

#if UNDYING_MEM
				MemMark.Pop();
				DynMark.Pop();
				SceneMark.Pop();
#endif

				// Send a notification message to the editor frame.  This of course relies on the 
				// window hierarchy not changing ... if it does, update this!
				HWND hwndEditorFrame = GetParent(GetParent(GetParent(ParentWindow)));
				SendMessageX( hwndEditorFrame, WM_COMMAND, WM_SETCURRENTVIEWPORT, (LPARAM)this );
			}

			if( iMessage == WM_LBUTTONDOWN )
			{
				if( GIsEditor )
				{
					// Allows the editor to properly initiate box selections
					if( Input->KeyDown(IK_Ctrl) && Input->KeyDown(IK_Alt) )
						Client->Engine->Click( this, MOUSE_Left, LOWORD(lParam), HIWORD(lParam) );
				}
				MovedSinceLeftClick = 0;
				StartTimeLeftClick = GetMessageTime();
				CauseInputEvent( IK_LeftMouse, IST_Press );
			}
			else if( iMessage == WM_RBUTTONDOWN )
			{
				MovedSinceRightClick = 0;
				StartTimeRightClick = GetMessageTime();
				CauseInputEvent( IK_RightMouse, IST_Press );
			}
			else if( iMessage == WM_MBUTTONDOWN )
			{
				MovedSinceMiddleClick = 0;
				StartTimeMiddleClick = GetMessageTime();
				CauseInputEvent( IK_MiddleMouse, IST_Press );
			}
			SetDrag( 1 );
			return 0;
			unguard;
		}
		case WM_MOUSEACTIVATE:
		{
			if( !GIsEditor && !Captured && !GetLegacyShowWindowsMouse() )
			{
				POINT pt;
				::GetCursorPos( &pt );
				::ScreenToClient( (HWND)GetWindow(), &pt );

				if ( pt.y < 0 )
					Dragging = 1;
			}
			// Activate this window and send the mouse-down message.
			return MA_ACTIVATE;
		}
		case WM_ACTIVATE:
		{
			guard(WM_ACTIVATE);

			// If window is becoming inactive, release the cursor.
			if( wParam == WA_INACTIVE )
				SetDrag( 0 );

			return DefWindowProcX( Window->hWnd, iMessage, wParam, lParam );
			unguard;
		}
		case WM_LBUTTONUP:
		case WM_RBUTTONUP:
		case WM_MBUTTONUP:
		{
			guard(WM_BUTTONUP);

			if( !GIsEditor )
			{
//				debugf(NAME_DebugRon, TEXT("MOUSE BUTTON UP    Dragging: %i  Captured: %i  bShowWindowsMouse: %i"), Dragging, Captured, GetLegacyShowWindowsMouse());
				if ( Dragging )
					Dragging = 0;

				if ( Captured == GetLegacyShowWindowsMouse() )
					SetMouseCapture(!GetLegacyShowWindowsMouse(),0,0); // changed OnlyFocus to 0, to fix mouse not activating window
			}

			// Exit if in menu loop.
			if( !GIsEditor || Client->InMenuLoop )
				return DefWindowProcX( Window->hWnd, iMessage, wParam, lParam );

			// Get mouse cursor position.
			POINT TempPoint={0,0};
			::ClientToScreen( Window->hWnd, &TempPoint );
			INT MouseX = SavedCursor.x!=-1 ? SavedCursor.x-TempPoint.x : LOWORD(lParam);
			INT MouseY = SavedCursor.x!=-1 ? SavedCursor.y-TempPoint.y : HIWORD(lParam);

			// Get time interval to determine if a click occured.
			INT DeltaTime, Button;
			EInputKey iKey;
			if( iMessage == WM_LBUTTONUP )
			{
				DeltaTime = GetMessageTime() - StartTimeLeftClick;
				iKey      = IK_LeftMouse;
				Button    = MOUSE_Left;
			}
			else if( iMessage == WM_MBUTTONUP )
			{
				DeltaTime = GetMessageTime() - StartTimeMiddleClick;
				iKey      = IK_MiddleMouse;
				Button    = MOUSE_Middle;
			}
			else
			{
				DeltaTime = GetMessageTime() - StartTimeRightClick;
				iKey      = IK_RightMouse;
				Button    = MOUSE_Right;
			}

			// Send to the input system.
			CauseInputEvent( iKey, IST_Release );

			// Release the cursor.
			if
			(	!Input->KeyDown(IK_LeftMouse)
			&&	!Input->KeyDown(IK_MiddleMouse)
			&&	!Input->KeyDown(IK_RightMouse) 
			&&	!(BlitFlags & BLIT_Fullscreen) )
                SetDrag( 0 );

			// Handle viewport clicking.
			if
			(	!(BlitFlags & BLIT_Fullscreen)
			&&	SizeX && SizeY 
			&&	!(MovedSinceLeftClick || MovedSinceRightClick || MovedSinceMiddleClick) )
			{
				Client->Engine->Click( this, Button, MouseX, MouseY );
				if( !IsRealtime() )
					RequestRepaint( 1 );
			}

			// Update times.
			if		( iMessage == WM_LBUTTONUP ) MovedSinceLeftClick	= 0;
			else if	( iMessage == WM_RBUTTONUP ) MovedSinceRightClick	= 0;
			else if	( iMessage == WM_MBUTTONUP ) MovedSinceMiddleClick	= 0;

			return 0;
			unguard;
		}
		case WM_ENTERMENULOOP:
		{
			guard(WM_ENTERMENULOOP);
			Client->InMenuLoop = 1;
			SetDrag( 0 );
			UpdateWindowFrame();
			if( Mouse )
				Mouse->Unacquire();
			if( Joystick )
				Joystick->Unacquire();
			return 0;
			unguard;
		}
		case WM_EXITMENULOOP:
		{
			guard(WM_EXITMENULOOP);
			Client->InMenuLoop = 0;
			if( Mouse )
				Mouse->Acquire();
			if( Joystick )
				Joystick->Acquire();
			return 0;
			unguard;
		}
		case WM_CANCELMODE:
		{
			guard(WM_CANCELMODE);
			SetDrag( 0 );
			return 0;
			unguard;
		}
		case WM_MOUSEWHEEL:
		{
			guard(WM_MOUSEWHEEL);
			SWORD zDelta = HIWORD(wParam);
			if( GIsEditor && zDelta )
			{
				CauseInputEvent( IK_MouseW, IST_Axis, zDelta );
				if( zDelta < 0 )
				{
					CauseInputEvent( IK_MWheelDown, IST_Press );
					CauseInputEvent( IK_MWheelDown, IST_Release );
				}
				else if( zDelta > 0 )
				{
					CauseInputEvent( IK_MWheelUp, IST_Press );
					CauseInputEvent( IK_MWheelUp, IST_Release );
				}
			}
			return 0;
			unguard;
		}
		case WM_MOUSEMOVE:
		{
			guard(WM_MOUSEMOVE);
			if( GIsEditor )
			{
				//GTempDouble=GTempDouble+1;

				// If in a window, see if cursor has been captured; if not, ignore mouse movement.
				if( Client->InMenuLoop )
					break;

				// Get window rectangle.
				RECT TempRect;
				::GetClientRect( Window->hWnd, &TempRect );
				WORD Buttons = wParam & (MK_LBUTTON | MK_RBUTTON | MK_MBUTTON);

				// Update the reference mouse positions
				if( GIsEditor )
				{
					POINT pt;
					::GetCursorPos( &pt );
					//MouseScreenPos = FVector( pt.x, pt.y, 0 );

					::ScreenToClient( (HWND)GetWindow(), &pt );
					//MouseClientPos = FVector( pt.x, pt.y, 0 );
				}

				// If cursor isn't captured, just do MousePosition.
				if( !(BlitFlags & BLIT_Fullscreen) && SavedCursor.x==-1 )
				{
					// Do mouse messaging.
					POINTS Point = MAKEPOINTS(lParam);
					DWORD ViewportButtonFlags = 0;
					if( Buttons & MK_LBUTTON     ) ViewportButtonFlags |= MOUSE_Left;
					if( Buttons & MK_RBUTTON     ) ViewportButtonFlags |= MOUSE_Right;
					if( Buttons & MK_MBUTTON     ) ViewportButtonFlags |= MOUSE_Middle;
					if( Input->KeyDown(IK_Shift) ) ViewportButtonFlags |= MOUSE_Shift;
					if( Input->KeyDown(IK_Ctrl ) ) ViewportButtonFlags |= MOUSE_Ctrl;
					if( Input->KeyDown(IK_Alt  ) ) ViewportButtonFlags |= MOUSE_Alt;
					Client->Engine->MousePosition( this, Buttons, Point.x-TempRect.left, Point.y-TempRect.top );
					//if( GetOuterUWindowsClient()->Engine->edcamMouseControl( this ) == MOUSEC_Free )
					//	Client->Engine->MouseDelta( this, ViewportButtonFlags, 0, 0 );
					if( GetLegacyShowWindowsMouse() && SelectedCursor >= 0 && SelectedCursor <= 6 )
						SetCursor( StandardCursors[SelectedCursor] );
					break;
				}

				// Get center of window.			
				POINT TempPoint, Base;
				TempPoint.x = (TempRect.left + TempRect.right )/2;
				TempPoint.y = (TempRect.top  + TempRect.bottom)/2;
				Base = TempPoint;

				// Movement accumulators.
				UBOOL Moved=0;
				INT Cumulative=0;

				// Grab all pending mouse movement.
				INT DX=0, DY=0;
				Loop:
				Buttons		  = wParam & (MK_LBUTTON | MK_RBUTTON | MK_MBUTTON);
				POINTS Points = MAKEPOINTS(lParam);
				INT X         = Points.x - Base.x;
				INT Y         = Points.y - Base.y;
				Cumulative += Abs(X) + Abs(Y);
				DX += X;
				DY += Y;

				// Process valid movement.
				DWORD ViewportButtonFlags = 0;
				if( Buttons & MK_LBUTTON     ) ViewportButtonFlags |= MOUSE_Left;
				if( Buttons & MK_RBUTTON     ) ViewportButtonFlags |= MOUSE_Right;
				if( Buttons & MK_MBUTTON     ) ViewportButtonFlags |= MOUSE_Middle;
				if( Input->KeyDown(IK_Shift) ) ViewportButtonFlags |= MOUSE_Shift;
				if( Input->KeyDown(IK_Ctrl ) ) ViewportButtonFlags |= MOUSE_Ctrl;
				if( Input->KeyDown(IK_Alt  ) ) ViewportButtonFlags |= MOUSE_Alt;

				// Move viewport with buttons.
				if( X || Y )
				{
					Moved = 1;
					Client->Engine->MouseDelta( this, ViewportButtonFlags, X, Y );
				}

				// Handle any more mouse moves.
				MSG Msg;
				if( PeekMessageX( &Msg, Window->hWnd, WM_MOUSEMOVE, WM_MOUSEMOVE, PM_REMOVE ) )
				{
					lParam = Msg.lParam;
					wParam = Msg.wParam;
					Base.x = Points.x;
					Base.y = Points.y;
					goto Loop;
				}

				// Set moved-flags.
				if( Cumulative>4 )
				{
					if( wParam & MK_LBUTTON ) MovedSinceLeftClick   = 1;
					if( wParam & MK_RBUTTON ) MovedSinceRightClick  = 1;
					if( wParam & MK_MBUTTON ) MovedSinceMiddleClick = 1;
				}

				// Send to input subsystem.
				if( DX ) CauseInputEvent( IK_MouseX, IST_Axis, +DX );
				if( DY ) CauseInputEvent( IK_MouseY, IST_Axis, -DY );

				// Put cursor back in middle of window.
				if( DX || DY )
				{
					::ClientToScreen( Window->hWnd, &TempPoint );
					SetCursorPos( TempPoint.x, TempPoint.y );
				}
				//else GTempDouble += 1000;
				
				// Viewport isn't realtime, so we must update the frame here and now.
				if( !IsRealtime() )
				{
					if( Input->KeyDown(IK_Space) )
						for( INT i=0; i<Client->Viewports.Num(); i++ )
							Client->Viewports(i)->RequestRepaint( 1 );
					else
						RequestRepaint( 1 );
				}

				// Dispatch keyboard events.
				while( PeekMessageX( &Msg, NULL, WM_KEYFIRST, WM_KEYLAST, PM_REMOVE ) )
				{
					TranslateMessage( &Msg );
					DispatchMessageX( &Msg );
				}
			}
			else	
			{
				UpdateMousePosition();
			}
			return 0;
			unguard;
		}
		case WM_SIZE:
		{
			guard(WM_SIZE);
			INT NewX = LOWORD(lParam);
			INT NewY = HIWORD(lParam);

			UBOOL ForceFullscreen = 0;

			UBOOL Minimized = wParam == SIZE_MINIMIZED, Restored = wParam == SIZE_RESTORED;
			if( wParam == SIZE_MAXIMIZED )
			{
				SetMouseCapture(1,1,1);
				ForceFullscreen = 1;
			}
			if( Minimized )
				SetMouseCapture(0,0,0);

			if( BlitFlags & BLIT_Fullscreen )
			{
				// Window forced out of fullscreen.
				if( Restored )
				{
					HoldCount++;
					Window->MoveWindow( SavedWindowRect, 1 );
					HoldCount--;
				}
				return 0;
			}
#if 1 // disable when not needed
			else if( wParam==SIZE_RESTORED && DirectDrawMinimized )
			{
				DirectDrawMinimized = 0;
				ToggleFullscreen();
				return 0;
			}
#endif
			else
			{
				// Use resized window.
				if( RenDev && (BlitFlags & (BLIT_OpenGL|BLIT_Direct3D)) )
				{
					RenDev->SetRes( NewX, NewY, ColorBytes, (BlitFlags & BLIT_Fullscreen) || RenDev->FullscreenOnly || ForceFullscreen );
					//RenDev->PrecacheOnFlip = 0; // added
				}
				else
				{
					//ResizeViewport( BlitFlags|BLIT_NoWindowChange, NewX, NewY, ColorBytes );
					ResizeViewport( BlitFlags, NewX, NewY, ColorBytes );
				}
				if( GIsEditor )
					Repaint( 0 );
      			return 0;
        	}
			unguard;
		}
		case WM_KILLFOCUS:
		{
			guard(WM_KILLFOCUS);
			SetMouseCapture( 0, 0, 0 );
			if( GIsRunning )
				Exec( TEXT("SETPAUSE 1"), *this );
			SetDrag( 0 );
			if( !GIsEditor && Input )
				Input->ResetInput();
#if ADDITIONS_IMPROVEMENTS
			if( IsFullscreen() && (!RenDev->SupportsBorderless || !Client->BorderlessFullscreen) )
#else
			if( IsFullscreen() && (!Client->BorderlessFullscreen) )
#endif
			{
				debugf(TEXT("WM_KILLFOCUS"));
				EndFullscreen();
#if 1 // disable when not needed
				if( BlitFlags & BLIT_Direct3D )
				{
					HoldCount++;
					ShowWindow( Window->hWnd, SW_SHOWMINNOACTIVE );
					HoldCount--;
					DirectDrawMinimized = 1;
				}
#endif
			}

			//GetOuterUClient()->MakeCurrent( NULL ); Changed by Demiurge (PSE) to allow real-time previewing
			return 0;
			unguard;
		}
		case WM_SETFOCUS:
		{
			guard(WM_SETFOCUS);

			// Grab the mouse.
			if( !GIsEditor && Captured == GetLegacyShowWindowsMouse() && !Dragging) // gam
				SetMouseCapture( !GetLegacyShowWindowsMouse(), 0, 1 );

			// Reset viewport's input.
			Exec( TEXT("SETPAUSE 0"), *this );
			if( Input )
				Input->ResetInput();

			// Acquire devices.
			if( Mouse )
				Mouse->Acquire();
			if( Joystick )
				Joystick->Acquire();

			// Make this viewport current.
			GetOuterUClient()->MakeCurrent( this );
			SetModeCursor();
            return 0;
			unguard;
		}
		case WM_SYSCOMMAND:
		{
			guard(WM_SYSCOMMAND);
			DWORD nID = wParam & 0xFFF0;
			if( nID==SC_SCREENSAVE || nID==SC_MONITORPOWER )
			{
				// Return 1 to prevent screen saver.
				if( nID==SC_SCREENSAVE )
					debugf( NAME_Log, TEXT("Received SC_SCREENSAVE") );
				else
					debugf( NAME_Log, TEXT("Received SC_MONITORPOWER") );
				return 0;
			}
			else if( nID==SC_MAXIMIZE )
			{
				// Maximize.
				ToggleFullscreen();
				return 0;
			}
			else if
			(	(BlitFlags & BLIT_Fullscreen)
			&&	(nID==SC_NEXTWINDOW || nID==SC_PREVWINDOW || nID==SC_TASKLIST || nID==SC_HOTKEY) )
			{
				// Don't allow window changes here.
				return 0;
			}
			else return DefWindowProcX(Window->hWnd,iMessage,wParam,lParam);
			unguard;
		}
		case WM_POWER:
		{
			guard(WM_POWER);
			if( wParam )
			{
				if( wParam == PWR_SUSPENDREQUEST )
				{
					debugf( NAME_Log, TEXT("Received WM_POWER suspend") );

					// Prevent powerdown if dedicated server or using joystick.
					if( 1 )
						return PWR_FAIL;
					else
						return PWR_OK;
				}
				else
				{
					debugf( NAME_Log, TEXT("Received WM_POWER") );
					return DefWindowProcX( Window->hWnd, iMessage, wParam, lParam );
				}
			}
			return 0;
			unguard;
		}
		case WM_DISPLAYCHANGE:
		{
			guard(WM_DISPLAYCHANGE);
			debugf( NAME_Log, TEXT("Viewport %s: WM_DisplayChange"), GetName() );
			unguard;
			return 0;
		}
		case WM_WININICHANGE:
		{
			guard(WM_WININICHANGE);
			if( !DeleteDC(Client->hMemScreenDC) )
				appErrorf( TEXT("DeleteDC failed: %s"), appGetSystemErrorMessage() );
			Client->hMemScreenDC = CreateCompatibleDC (NULL);
			return 0;
			unguard;
		}
		case WM_INPUT:
		{
			static BYTE data[1024];
			RAWINPUT* raw = (RAWINPUT*)data;
			UINT size = sizeof(data);

			if (GET_RAWINPUT_CODE_WPARAM(wParam) != RIM_INPUT /* app is in the background */
				|| GetRawInputData((HRAWINPUT)lParam, RID_INPUT, data, &size, sizeof(RAWINPUTHEADER)) == (UINT)-1)
			{
				return DefWindowProcX( Window->hWnd, iMessage, wParam, lParam );
			}
		
			if (raw->header.dwType == RIM_TYPEKEYBOARD)
			{
				const RAWKEYBOARD& keyboard = raw->data.keyboard;
				USHORT flags = keyboard.Flags;

				bool bIgnoreKey = false;

				// Ignore key overrun state
				if (keyboard.MakeCode == KEYBOARD_OVERRUN_MAKE_CODE)
					bIgnoreKey = true;

				// Ignore keys not mapped to any VK code
				// This effectively filters out scan code pre/postfix for some keys like PrintScreen.
				if (keyboard.VKey >= 0xff/*VK__none_*/)
					bIgnoreKey = true;

				if (!bIgnoreKey)
				{
					unsigned int vkCode = keyboard.VKey;

					bool bKeyDown = (flags & RI_KEY_BREAK) == 0;	

					if (bKeyDown)
						CauseInputEvent((EInputKey)vkCode, IST_Press);
					else
						CauseInputEvent((EInputKey)vkCode, IST_Release);
				}	
			}
			else if (raw->header.dwType == RIM_TYPEMOUSE)
			{
				//if (!overWindow)
				//{
				//	// mouse is outside the window
				//	return 0;
				//}
				if (raw->data.mouse.usButtonFlags & RI_MOUSE_WHEEL)
				{
					SHORT DW = raw->data.mouse.usButtonData;
					if (DW != 0)
					{
						EInputKey key = DW > 0 ? IK_MWheelUp : IK_MWheelDown;

						CauseInputEvent(IK_MouseW, IST_Axis, DW);
						CauseInputEvent(key, IST_Press);
						CauseInputEvent(key, IST_Release);
					}
				}

				if (raw->data.mouse.usButtonFlags & RI_MOUSE_LEFT_BUTTON_DOWN)
				{
					CauseInputEvent(IK_LeftMouse, IST_Press);
				}
				if (raw->data.mouse.usButtonFlags & RI_MOUSE_LEFT_BUTTON_UP)
				{
					CauseInputEvent(IK_LeftMouse, IST_Release);
				}

				if (raw->data.mouse.usButtonFlags & RI_MOUSE_RIGHT_BUTTON_DOWN)
				{
					CauseInputEvent(IK_RightMouse, IST_Press);
				}
				if (raw->data.mouse.usButtonFlags & RI_MOUSE_RIGHT_BUTTON_UP)
				{
					CauseInputEvent(IK_RightMouse, IST_Release);
				}

				if (raw->data.mouse.usButtonFlags & RI_MOUSE_MIDDLE_BUTTON_DOWN)
				{
					CauseInputEvent(IK_MiddleMouse, IST_Press);
				}
				if (raw->data.mouse.usButtonFlags & RI_MOUSE_MIDDLE_BUTTON_UP)
				{
					CauseInputEvent(IK_MiddleMouse, IST_Release);
				}

				// Send to input subsystem.
				if (raw->data.mouse.lLastX)
					CauseInputEvent(IK_MouseX, IST_Axis, +raw->data.mouse.lLastX);
				if (raw->data.mouse.lLastY)
					CauseInputEvent(IK_MouseY, IST_Axis, -raw->data.mouse.lLastY);
			}
			return 0;
		}
		default:
		{
			guard(WM_UNKNOWN);
			return DefWindowProcX( Window->hWnd, iMessage, wParam, lParam );
			unguard;
		}
	}
	unguard;
	return 0;
}
W_IMPLEMENT_CLASS(WWindowsViewportWindow)

/*-----------------------------------------------------------------------------
	DirectDraw support.
-----------------------------------------------------------------------------*/

//
// Set DirectDraw to a particular mode, with full error checking
// Returns 1 if success, 0 if failure.
//
UBOOL UWindowsViewport::ddSetMode( INT NewX, INT NewY, INT ColorBytes )
{
	guard(UWindowsViewport::ddSetMode);
	UWindowsClient* Client = GetOuterUWindowsClient();
	check(Client->dd);
	HRESULT	Result;

	// Set the display mode.
	debugf( NAME_Log, TEXT("Setting %ix%ix%i"), NewX, NewY, ColorBytes*8 );
	Result = Client->dd->SetDisplayMode( NewX, NewY, ColorBytes*8, 0, 0 );
	if( Result!=DD_OK )
	{
		debugf( NAME_Log, TEXT("DirectDraw Failed %ix%ix%i: %s"), NewX, NewY, ColorBytes*8, ddError(Result) );
		Result = Client->dd->SetCooperativeLevel( NULL, DDSCL_NORMAL );
   		return 0;
	}

	// Create surfaces.
	DDSURFACEDESC SurfaceDesc;
	appMemzero( &SurfaceDesc, sizeof(DDSURFACEDESC) );
	SurfaceDesc.dwSize = sizeof(DDSURFACEDESC);
	SurfaceDesc.dwFlags = DDSD_CAPS | DDSD_BACKBUFFERCOUNT;
	SurfaceDesc.ddsCaps.dwCaps
	=	DDSCAPS_PRIMARYSURFACE
	|	DDSCAPS_FLIP
	|	DDSCAPS_COMPLEX
	|	(Client->SlowVideoBuffering ? DDSCAPS_SYSTEMMEMORY : DDSCAPS_VIDEOMEMORY);

	// Create the best possible surface for rendering.
	TCHAR* Descr=NULL;
	if( 1 )
	{
		// Try triple-buffered video memory surface.
		SurfaceDesc.dwBackBufferCount = 2;
		Result = Client->dd->CreateSurface( &SurfaceDesc, &ddFrontBuffer, NULL );
		Descr  = TEXT("Triple buffer");
	}
	if( Result != DD_OK )
   	{
		// Try to get a double buffered video memory surface.
		SurfaceDesc.dwBackBufferCount = 1; 
		Result = Client->dd->CreateSurface( &SurfaceDesc, &ddFrontBuffer, NULL );
		Descr  = TEXT("Double buffer");
    }
	if( Result != DD_OK )
	{
		// Settle for a main memory surface.
		SurfaceDesc.ddsCaps.dwCaps &= ~DDSCAPS_VIDEOMEMORY;
		Result = Client->dd->CreateSurface( &SurfaceDesc, &ddFrontBuffer, NULL );
		Descr  = TEXT("System memory");
    }
	if( Result != DD_OK )
	{
		debugf( NAME_Log, TEXT("DirectDraw, no available modes %s"), ddError(Result) );
		Client->dd->RestoreDisplayMode();
		Client->dd->FlipToGDISurface();
	   	return 0;
	}
	debugf( NAME_Log, TEXT("DirectDraw: %s, %ix%i, Stride=%i"), Descr, NewX, NewY, SurfaceDesc.lPitch );
	debugf( NAME_Log, TEXT("DirectDraw: Rate=%i"), SurfaceDesc.dwRefreshRate );

	// Clear the screen.
	DDBLTFX ddbltfx;
	ddbltfx.dwSize = sizeof( ddbltfx );
	ddbltfx.dwFillColor = 0;
	ddFrontBuffer->Blt( NULL, NULL, NULL, DDBLT_COLORFILL, &ddbltfx );

	// Get a pointer to the back buffer.
	DDSCAPS caps;
	caps.dwCaps = DDSCAPS_BACKBUFFER;
	if( ddFrontBuffer->GetAttachedSurface( &caps, &ddBackBuffer )!=DD_OK )
	{
		debugf( NAME_Log, TEXT("DirectDraw GetAttachedSurface failed %s"), ddError(Result) );
		ddFrontBuffer->Release();
		ddFrontBuffer = NULL;
		Client->dd->RestoreDisplayMode();
		Client->dd->FlipToGDISurface();
		return 0;
	}

	// Get pixel format.
	DDPIXELFORMAT PixelFormat;
	PixelFormat.dwSize = sizeof(DDPIXELFORMAT);
	Result = ddFrontBuffer->GetPixelFormat( &PixelFormat );
	if( Result!=DD_OK )
	{
		debugf( TEXT("DirectDraw GetPixelFormat failed: %s"), ddError(Result) );
		ddBackBuffer->Release();
		ddBackBuffer = NULL;
		ddFrontBuffer->Release();
		ddFrontBuffer = NULL;
		Client->dd->RestoreDisplayMode();
		Client->dd->FlipToGDISurface();
		return 0;
	}

	// See if we're in a 16-bit color mode.
	Caps &= ~CC_RGB565;
	if( ColorBytes==2 && PixelFormat.dwRBitMask==0xf800 ) 
		Caps |= CC_RGB565;

	// Flush the cache.
	GCache.Flush();

	// Success.
	return 1;
	unguard;
}


/*-----------------------------------------------------------------------------
	Lock and Unlock.
-----------------------------------------------------------------------------*/

//
// Lock the viewport window and set the approprite Screen and RealScreen fields
// of Viewport.  Returns 1 if locked successfully, 0 if failed.  Note that a
// lock failing is not a critical error; it's a sign that a DirectDraw mode
// has ended or the user has closed a viewport window.
//
UBOOL UWindowsViewport::Lock( FPlane FlashScale, FPlane FlashFog, FPlane ScreenClear, DWORD RenderLockFlags, BYTE* HitData, INT* HitSize )
{
	guard(UWindowsViewport::LockWindow);
	UWindowsClient* Client = GetOuterUWindowsClient();
	appClock(Client->DrawCycles);

	// Make sure window is lockable.
	if( (Window->hWnd && !IsWindow(Window->hWnd)) || HoldCount || !SizeX || !SizeY || !RenDev )
      	return 0;

	// Get info.
	Stride = SizeX;
	if( BlitFlags & BLIT_DirectDraw )
	{
		// Lock DirectDraw.
		check(!(BlitFlags&BLIT_DibSection));
		HRESULT Result;
  		if( ddFrontBuffer->IsLost() == DDERR_SURFACELOST )
		{
			Result = ddFrontBuffer->Restore();
   			if( Result != DD_OK )
			{
				debugf( NAME_Log, TEXT("DirectDraw Lock Restore failed %s"), ddError(Result) );
				ResizeViewport( BLIT_DibSection );//!!failure of d3d?
				return 0;
			}
		}
		appMemzero( &ddSurfaceDesc, sizeof(ddSurfaceDesc) );
  		ddSurfaceDesc.dwSize = sizeof(ddSurfaceDesc);
		Result = ddBackBuffer->Lock( NULL, &ddSurfaceDesc, DDLOCK_WAIT|DD_OTHERLOCKFLAGS, NULL );
  		if( Result != DD_OK )
		{
			debugf( NAME_Log, TEXT("DirectDraw Lock failed: %s"), ddError(Result) );
  			return 0;
		}
		if( ddSurfaceDesc.lPitch )
			Stride = ddSurfaceDesc.lPitch/ColorBytes;
		ScreenPointer = (BYTE*)ddSurfaceDesc.lpSurface;
		check(ScreenPointer);
	}
	else if( BlitFlags & BLIT_DibSection )
	{
		check(!(BlitFlags&BLIT_DirectDraw));
		check(ScreenPointer);
	}

	// Success here, so pass to superclass.
	appUnlock(Client->DrawCycles);
	return UViewport::Lock(FlashScale,FlashFog,ScreenClear,RenderLockFlags,HitData,HitSize);

	unguard;
}

//
// Unlock the viewport window.  If Blit=1, blits the viewport's frame buffer.
//
void UWindowsViewport::Unlock( UBOOL Blit )
{
	guard(UWindowsViewport::Unlock);
	UWindowsClient* Client = GetOuterUWindowsClient();
	check(!HoldCount);
	Client->DrawCycles=0;
	appClock(Client->DrawCycles);
	UViewport::Unlock( Blit );
	if( BlitFlags & BLIT_DirectDraw )
	{
		// Handle DirectDraw.
		guard(UnlockDirectDraw);
		HRESULT Result;
		Result = ddBackBuffer->Unlock( ddSurfaceDesc.lpSurface );
		if( Result ) 
		 	appErrorf( TEXT("DirectDraw Unlock: %s"), ddError(Result) );
		if( Blit )
		{
			HRESULT Result = ddFrontBuffer->Flip( NULL, DDFLIP_WAIT );
			if( Result != DD_OK )
				appErrorf( TEXT("DirectDraw Flip failed: %s"), ddError(Result) );
		}
		unguard;
	}
	else if( BlitFlags & BLIT_DibSection )
	{
		// Handle CreateDIBSection.
		if( Blit )
		{
			HDC hDC = GetDC( Window->hWnd );
			if( hDC == NULL )
				appErrorf( TEXT("GetDC failed: %s"), appGetSystemErrorMessage() );
			if( SelectObject( Client->hMemScreenDC, hBitmap ) == NULL )
				appErrorf( TEXT("SelectObject failed: %s"), appGetSystemErrorMessage() );
			if( BitBlt( hDC, 0, 0, SizeX, SizeY, Client->hMemScreenDC, 0, 0, SRCCOPY ) == NULL )
				appErrorf( TEXT("BitBlt failed: %s"), appGetSystemErrorMessage() );
			if( ReleaseDC( Window->hWnd, hDC ) == NULL )
				appErrorf( TEXT("ReleaseDC failed: %s"), appGetSystemErrorMessage() );
		}
	}
	appUnclock(Client->DrawCycles);
	unguard;
}

/*-----------------------------------------------------------------------------
	Viewport modes.
-----------------------------------------------------------------------------*/

//
// Try switching to a new rendering device.
//
void UWindowsViewport::TryRenderDevice( const TCHAR* ClassName, INT NewX, INT NewY, INT NewColorBytes, UBOOL Fullscreen )
{
	guard(UWindowsViewport::TryRenderDevice);

	// Shut down current rendering device.
	if( RenDev )
	{
		RenDev->Exit();
		delete RenDev;
		RenDev = NULL;
	}

	// Only allow fullscreen if window can be brought on top.
	HWND hWndForeground = ::GetForegroundWindow();
	UBOOL Focus			= 1;
	UBOOL Attach		= (hWndForeground == Window->hWnd);
	if( Attach )
		AttachThreadInput(GetWindowThreadProcessId(hWndForeground, NULL), GetCurrentThreadId(), true);
	if( !SetForegroundWindow( Window->hWnd ) && !Attach)
	{
		debugf( TEXT("Couldn't bring window to foreground.") );
		Focus = 0;
	}
	if( !SetActiveWindow( Window->hWnd ) )
	{
		debugf( TEXT("Couldn't set window as active one.") );
		Focus = 0;
	}
	if( Attach )
		AttachThreadInput(GetWindowThreadProcessId(hWndForeground, NULL), GetCurrentThreadId(), false);

	// Use appropriate defaults.
	UWindowsClient* C = GetOuterUWindowsClient();
	if( NewX==INDEX_NONE )
		NewX = (Fullscreen && Focus) ? C->FullscreenViewportX : C->WindowedViewportX;
	if( NewY==INDEX_NONE )
		NewY = (Fullscreen && Focus) ? C->FullscreenViewportY : C->WindowedViewportY;
	if( NewColorBytes==INDEX_NONE )
		NewColorBytes = (Fullscreen && Focus) ? C->FullscreenColorBits/8 : ColorBytes;

	// Find device driver.
	UClass* RenderClass = UObject::StaticLoadClass( URenderDevice::StaticClass(), NULL, ClassName, NULL, 0, NULL );
	if( RenderClass )
	{
		HoldCount++;
		RenDev = ConstructObject<URenderDevice>( RenderClass, this );
		if( RenDev->Init( this, NewX, NewY, NewColorBytes, (Fullscreen && Focus) || RenDev->FullscreenOnly ) )
		{
			if( GIsRunning )
				Actor->GetLevel()->DetailChange( RenDev->HighDetailActors );
		}
		else
		{
			debugf( NAME_Log, LocalizeError("Failed3D") );
			delete RenDev;
			RenDev = NULL;
		}
		if( !Focus && RenDev->FullscreenOnly )			
		{
			SetMouseCapture( 0, 0, 0 );
			ShowWindow( Window->hWnd, SW_MINIMIZE );
		}
		HoldCount--;
	}
	GRenderDevice = RenDev;
	unguard;
}

//
// If in fullscreen mode, end it and return to Windows.
//
void UWindowsViewport::EndFullscreen()
{
	guard(UWindowsViewport::EndFullscreen);
	UWindowsClient* Client = GetOuterUWindowsClient();
	debugf(TEXT("EndFullscreen"));
	if( RenDev && RenDev->FullscreenOnly )
	{
		// This device doesn't support fullscreen, so use a window-capable rendering device.
		TryRenderDevice( TEXT("ini:Engine.Engine.WindowedRenderDevice"), INDEX_NONE, INDEX_NONE, INDEX_NONE, 0 );
		check(RenDev);
	}
	else if( RenDev && (BlitFlags & (BLIT_Direct3D|BLIT_Vulkan)))
	{
		RenDev->SetRes( Client->WindowedViewportX, Client->WindowedViewportY, ColorBytes, 0 );
	}
	else if( RenDev && (BlitFlags & BLIT_OpenGL) )
	{
		RenDev->SetRes( INDEX_NONE, INDEX_NONE, ColorBytes, 0 );
	}
	else
	{
		ResizeViewport( BLIT_DibSection );
	}
	UpdateWindowFrame();
	if( Input )
		Input->ResetInput();
	unguard;
}

//
// Toggle fullscreen.
//
void UWindowsViewport::ToggleFullscreen()
{
	guard(UWindowsViewport::ToggleFullscreen);
	if( BlitFlags & BLIT_Fullscreen )
	{
		EndFullscreen();

		if( GIsEditor )
			SetMouseCapture(1,1,1);
		else if( Captured == GetLegacyShowWindowsMouse() )
			SetMouseCapture(!GetLegacyShowWindowsMouse(),0,1);
	}
	else if( !(Actor->ShowFlags & SHOW_ChildWindow) )
	{
		debugf(TEXT("AttemptFullscreen"));
		TryRenderDevice( TEXT("ini:Engine.Engine.GameRenderDevice"), INDEX_NONE, INDEX_NONE, INDEX_NONE, 1 );
		if( !RenDev )
			TryRenderDevice( TEXT("ini:Engine.Engine.WindowedRenderDevice"), INDEX_NONE, INDEX_NONE, INDEX_NONE, 1 );
		if( !RenDev )
			TryRenderDevice( TEXT("ini:Engine.Engine.WindowedRenderDevice"), INDEX_NONE, INDEX_NONE, INDEX_NONE, 0 );
	}
	unguard;
}

//
// Resize the viewport.
//
UBOOL UWindowsViewport::ResizeViewport( DWORD NewBlitFlags, INT InNewX, INT InNewY, INT InNewColorBytes )
{
	guard(UWindowsViewport::ResizeViewport);
	UWindowsClient* Client = GetOuterUWindowsClient();

	// Handle temporary viewports.
	if( BlitFlags & BLIT_Temporary )
		NewBlitFlags &= ~(BLIT_DirectDraw | BLIT_DibSection);

	// Handle DirectDraw not available.
	if( (NewBlitFlags & BLIT_DirectDraw) && !Client->dd )
		NewBlitFlags = (NewBlitFlags | BLIT_DibSection) & ~(BLIT_Fullscreen | BLIT_DirectDraw);

	// If going windowed, but the rendering device is fullscreen-only, switch to the software renderer.
	if( RenDev && RenDev->FullscreenOnly && !(NewBlitFlags & BLIT_Fullscreen) )
	{
		guard(SoftwareBail);
		if( !(GetFlags() & RF_Destroyed) )
		{
			TryRenderDevice( TEXT("ini:Engine.Engine.WindowedRenderDevice"), INDEX_NONE, INDEX_NONE, InNewColorBytes, 0 );
			check(RenDev);
		}
		return 0;
		unguard;
	}

	// Remember viewport.
	UViewport* SavedViewport = NULL;
	if( Client->Engine->Audio && !GIsEditor && !(GetFlags() & RF_Destroyed) )
		SavedViewport = Client->Engine->Audio->GetViewport();

	// Accept default parameters.
	INT NewX          = InNewX         ==INDEX_NONE ? SizeX      : InNewX;
	INT NewY          = InNewY         ==INDEX_NONE ? SizeY      : InNewY;
	INT NewColorBytes = InNewColorBytes==INDEX_NONE ? ColorBytes : InNewColorBytes;

	// Shut down current frame.
	if( BlitFlags & BLIT_DirectDraw )
	{
		debugf( NAME_Log, TEXT("DirectDraw session ending") );
		if( SavedViewport )
			Client->Engine->Audio->SetViewport( NULL );
		check(ddBackBuffer);
		ddBackBuffer->Release();
		check(ddFrontBuffer);
		ddFrontBuffer->Release();
		if( !(NewBlitFlags & BLIT_DirectDraw) )
		{
			HoldCount++;
			Client->ddEndMode();
			HoldCount--;
		}
	}
	else if( BlitFlags & BLIT_DibSection )
	{
		if( hBitmap )
			DeleteObject( hBitmap );
		hBitmap = NULL;
	}

	// Get this window rect.
	FRect WindowRect = SavedWindowRect;
	if( Window->hWnd && !(BlitFlags & BLIT_Fullscreen) && !(NewBlitFlags&BLIT_NoWindowChange) )
		WindowRect = Window->GetWindowRect();

	// Default resolution handling.
	NewX = InNewX!=INDEX_NONE ? InNewX : (NewBlitFlags&BLIT_Fullscreen) ? Client->FullscreenViewportX : Client->WindowedViewportX;
	NewY = InNewX!=INDEX_NONE ? InNewY : (NewBlitFlags&BLIT_Fullscreen) ? Client->FullscreenViewportY : Client->WindowedViewportY;

	// Align NewX.
	check(NewX>=0);
	check(NewY>=0);
	NewX = Align(NewX,2);

	// If currently fullscreen, end it.
	if( BlitFlags & BLIT_Fullscreen )
	{
		// Saved parameters.
		SetFocus( Window->hWnd );
		if( InNewColorBytes==INDEX_NONE )
			NewColorBytes = SavedColorBytes;

		// Remember saved info.
		WindowRect          = SavedWindowRect;
		Caps                = SavedCaps;

		// Restore window topness.
		SetTopness();
		SetDrag( 0 );

		// Stop inhibiting windows keys.
		UnregisterHotKey( Window->hWnd, Client->hkAltEsc  );
		UnregisterHotKey( Window->hWnd, Client->hkAltTab  );
		UnregisterHotKey( Window->hWnd, Client->hkCtrlEsc );
		UnregisterHotKey( Window->hWnd, Client->hkCtrlTab );
		//DWORD Old=0;
		//SystemParametersInfoX( SPI_SCREENSAVERRUNNING, 0, &Old, 0 );
	}

	// If transitioning into fullscreen.
	if( (NewBlitFlags & BLIT_Fullscreen) && !(BlitFlags & BLIT_Fullscreen) )
	{
		// Save window parameters.
		SavedWindowRect = WindowRect;
		SavedColorBytes	= ColorBytes;
		SavedCaps       = Caps;

		// Make "Advanced Options" not return fullscreen after this.
		if( Client->ConfigProperties )
		{
			Client->ConfigReturnFullscreen = 0;
			DestroyWindow( *Client->ConfigProperties );
		}

		// Turn off window border and menu.
		HoldCount++;
		SendMessageX( Window->hWnd, WM_SETREDRAW, 0, 0 );
		SetMenu( Window->hWnd, NULL );
		if( !GIsEditor )
		{
			SetWindowLongX( Window->hWnd, GWL_STYLE, GetWindowLongX(Window->hWnd,GWL_STYLE) & ~(WS_CAPTION|WS_THICKFRAME) );
			Borderless = 1;
		}
		SendMessageX( Window->hWnd, WM_SETREDRAW, 1, 0 );
		HoldCount--;
	}

	// Handle display method.
	if( NewBlitFlags & BLIT_DirectDraw )
	{
		// Go into closest matching DirectDraw mode.
		INT BestMode=-1, BestDelta=MAXINT;		
		for( INT i=0; i<Client->DirectDrawModes[ColorBytes].Num(); i++ )
		{
			INT Delta = Abs(Client->DirectDrawModes[ColorBytes](i).X-NewX) + Abs(Client->DirectDrawModes[ColorBytes](i).Y-NewY);
			if( Delta < BestDelta )
			{
				BestMode  = i;
				BestDelta = Delta;
			}
		}
		if( BestMode>=0 )
		{
			// Try to go into DirectDraw.
			NewX = Client->DirectDrawModes[ColorBytes](BestMode).X;
			NewY = Client->DirectDrawModes[ColorBytes](BestMode).Y;
			HoldCount++;
			if( !(BlitFlags & BLIT_DirectDraw) )
			{
				if( SavedViewport )
					Client->Engine->Audio->SetViewport( NULL );
				HRESULT Result = Client->dd->SetCooperativeLevel( Window->hWnd, DDSCL_EXCLUSIVE | DDSCL_FULLSCREEN | DDSCL_ALLOWREBOOT );
				if( Result != DD_OK )
				{
					debugf( TEXT("DirectDraw SetCooperativeLevel failed: %s"), ddError(Result) );
   					return 0;
				}
			}
			SetCursor( NULL );
			UBOOL Result = ddSetMode( NewX, NewY, NewColorBytes );
			SetForegroundWindow( Window->hWnd );
			HoldCount--;
			if( !Result )
			{
				// DirectDraw failed.
				HoldCount++;
				Client->dd->SetCooperativeLevel( NULL, DDSCL_NORMAL );
				Window->MoveWindow( SavedWindowRect, 1 );
				SetTopness();
				HoldCount--;
				debugf( LocalizeError("DDrawMode") );
				return 0;
			}
		}
		else
		{
			debugf( TEXT("No DirectDraw modes") );
			return 0;
		}
	}
	else if( (NewBlitFlags&BLIT_DibSection) && NewX && NewY )
	{
		// Create DIB section.
		struct { BITMAPINFOHEADER Header; RGBQUAD Colors[256]; } Bitmap;

		// Init BitmapHeader for DIB.
		appMemzero( &Bitmap, sizeof(Bitmap) );
		Bitmap.Header.biSize			= sizeof(BITMAPINFOHEADER);
		Bitmap.Header.biWidth			= NewX;
		Bitmap.Header.biHeight			= -NewY;
		Bitmap.Header.biPlanes			= 1;
		Bitmap.Header.biBitCount		= NewColorBytes * 8;
		Bitmap.Header.biSizeImage		= NewX * NewY * NewColorBytes;

		// Handle color depth.
		if( NewColorBytes==2 )
		{
			// 16-bit color (565).
			Bitmap.Header.biCompression = BI_BITFIELDS;
			*(DWORD *)&Bitmap.Colors[0] = (Caps & CC_RGB565) ? 0xF800 : 0x7C00;
			*(DWORD *)&Bitmap.Colors[1] = (Caps & CC_RGB565) ? 0x07E0 : 0x03E0;
			*(DWORD *)&Bitmap.Colors[2] = (Caps & CC_RGB565) ? 0x001F : 0x001F;
		}
		else if( NewColorBytes==3 || NewColorBytes==4 )
		{
			// 24-bit or 32-bit color.
			Bitmap.Header.biCompression = BI_RGB;
			*(DWORD *)&Bitmap.Colors[0] = 0;
		}
		else appErrorf( TEXT("Invalid DibSection color depth %i"), NewColorBytes );

		// Create DIB section.
		HDC TempDC = GetDC(0);
		check(TempDC);
		hBitmap = CreateDIBSection( TempDC, (BITMAPINFO*)&Bitmap.Header, DIB_RGB_COLORS, (void**)&ScreenPointer, NULL, 0 );
		ReleaseDC( 0, TempDC );
		if( !hBitmap )
			appErrorf( LocalizeError("OutOfMemory",TEXT("Core")) );
		check(ScreenPointer);
	}
	else if( !(NewBlitFlags & BLIT_Temporary) )
	{
		ScreenPointer = NULL;
	}

	// OpenGL handling.
	if( (NewBlitFlags & BLIT_Fullscreen) && !GIsEditor && RenDev && appStricmp(RenDev->GetClass()->GetName(),TEXT("OpenGLRenderDevice"))==0 )
	{
		// Turn off window border and menu.
		HoldCount++;
		SendMessageX( Window->hWnd, WM_SETREDRAW, 0, 0 );
		Window->MoveWindow( FRect(0,0,NewX,NewY), 0 );
		SendMessageX( Window->hWnd, WM_SETREDRAW, 1, 0 );
		HoldCount--;
	}

	// Set new info.
	DWORD OldBlitFlags = BlitFlags;
	BlitFlags          = NewBlitFlags & ~BLIT_ParameterFlags;
	SizeX              = NewX;
	SizeY              = NewY;
	ColorBytes         = NewColorBytes ? NewColorBytes : ColorBytes;

	// If transitioning out of fullscreen.
	if( !(NewBlitFlags & BLIT_Fullscreen) && (OldBlitFlags & BLIT_Fullscreen) && Captured )
	{
		SetMouseCapture( 0, 0, 0 );
	}

	// Handle type.
	if( NewBlitFlags & BLIT_Fullscreen )
	{	
		// Handle fullscreen input.
		SetDrag( 1 );
		SetMouseCapture( 1, 1, 0 );
		RegisterHotKey( Window->hWnd, Client->hkAltEsc,  MOD_ALT,     VK_ESCAPE );
		RegisterHotKey( Window->hWnd, Client->hkAltTab,  MOD_ALT,     VK_TAB    );
		RegisterHotKey( Window->hWnd, Client->hkCtrlEsc, MOD_CONTROL, VK_ESCAPE );
		RegisterHotKey( Window->hWnd, Client->hkCtrlTab, MOD_CONTROL, VK_TAB    );
		//DWORD Old=0;
		//SystemParametersInfoX( SPI_SCREENSAVERRUNNING, 1, &Old, 0 );
	}
	else if( !(NewBlitFlags & BLIT_Temporary) && !(NewBlitFlags & BLIT_NoWindowChange) )
	{
		// Turn on window border and menu.
		if( Borderless )
		{
			HoldCount++;
			SetWindowLongX( Window->hWnd, GWL_STYLE, GetWindowLongX(Window->hWnd,GWL_STYLE) | (WS_CAPTION|WS_THICKFRAME) );
			HoldCount--;
		}

		// Going to a window.
		FRect ClientRect(0,0,NewX,NewY);
		AdjustWindowRect( ClientRect, GetWindowLongX(Window->hWnd,GWL_STYLE), (Actor->ShowFlags & SHOW_Menu)!=0 );

		// Resize the window and repaint it.
		if( !(Actor->ShowFlags & SHOW_ChildWindow) )
		{
			HoldCount++;
			Window->MoveWindow( FRect(WindowRect.Min,WindowRect.Min+ClientRect.Size()), 1 );
			HoldCount--;
		}
		SetDrag( 0 );
	}

	// Update audio.
	if( SavedViewport && SavedViewport!=Client->Engine->Audio->GetViewport() )
		Client->Engine->Audio->SetViewport( SavedViewport );

	// Update the window.
	UpdateWindowFrame();

	bWindowsMouseAvailable = !(NewBlitFlags & BLIT_Fullscreen);

	// Save info.
	if( RenDev && !GIsEditor )
	{
		if( NewBlitFlags & BLIT_Fullscreen )
		{
			if( NewX && NewY )
			{
				Client->FullscreenViewportX  = NewX;
				Client->FullscreenViewportY  = NewY;
				Client->FullscreenColorBits  = NewColorBytes*8;
			}
		}
		else
		{
			if( NewX && NewY )
			{
				Client->WindowedViewportX  = NewX;
				Client->WindowedViewportY  = NewY;
				Client->WindowedColorBits  = NewColorBytes*8;
			}
		}
		Client->SaveConfig();
	}

	return 1;
	unguard;
}

//
// DirectInput joystick callback.
//
BOOL CALLBACK UWindowsViewport::EnumJoysticksCallback( const DIDEVICEINSTANCE* pdidInstance, VOID* pContext )
{
    HRESULT hr;
	
	if( FAILED( hr = DirectInput8->CreateDevice( pdidInstance->guidInstance, &Joystick, NULL ) ) ) 
	{
		Joystick = NULL;
        return DIENUM_CONTINUE;
	}

    return DIENUM_STOP;
}

BOOL CALLBACK UWindowsViewport::EnumAxesCallback( const DIDEVICEOBJECTINSTANCE* pdidoi, VOID* pContext )
{
    DIPROPRANGE diprg; 
    diprg.diph.dwSize       = sizeof(DIPROPRANGE); 
    diprg.diph.dwHeaderSize = sizeof(DIPROPHEADER); 
    diprg.diph.dwHow        = DIPH_BYOFFSET; 
    diprg.diph.dwObj        = pdidoi->dwOfs; // Specify the enumerated axis
    diprg.lMin              = 0;//-32768; 
    diprg.lMax              = 65535;//+32767; 
    
	// Set the range for the axis
	Joystick->SetProperty( DIPROP_RANGE, &diprg.diph );
//	if( FAILED( Joystick->SetProperty( DIPROP_RANGE, &diprg.diph ) ) )
//		return DIENUM_STOP;

    return DIENUM_CONTINUE;
}

LPDIRECTINPUTDEVICE8	UWindowsViewport::Mouse					= NULL;
LPDIRECTINPUTDEVICE8	UWindowsViewport::Joystick				= NULL;
LPDIRECTINPUT8			UWindowsViewport::DirectInput8			= NULL;
DIDEVCAPS				UWindowsViewport::JoystickCaps;

/*-----------------------------------------------------------------------------
	The End.
-----------------------------------------------------------------------------*/
